@font-face {
  font-family: "EuclidSquare-Bold";
  src: url("/admin/assets/font/EuclidSquare/EuclidSquare-Bold.ttf");
}
@font-face {
  font-family: "EuclidSquare-Regular";
  src: url("/admin/assets/font/EuclidSquare/EuclidSquare-Regular.ttf");
}
@font-face {
  font-family: "EuclidSquare-Medium";
  src: url("/admin/assets/font/EuclidSquare/EuclidSquare-Medium.ttf");
}



/* @import url('https://fonts.cdnfonts.com/css/gilroy-bold'); */

body {
  margin: 0;
  color: #000126;
  font-size: 13px;
  position: relative;
  font-family: "EuclidSquare-Regular" !important;
  overflow-x: hidden;
  background-color: #f5f5f5;
}




h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
  margin: 0;
  padding: 0;
}



ol,
ul {
  padding: 0 !important;
  list-style: none !important;
}

p,
ul,
ol,
div,
footer,
header,
main {
  font-family: "EuclidSquare-Regular" !important;
}

/* total width */
::-webkit-scrollbar {
  background-color: #fff;
  width: 16px;
}

/* background of the scrollbar except button or resizer */
::-webkit-scrollbar-track {
  background-color: #fff;
}

::-webkit-scrollbar-track:hover {
  background-color: #f4f4f4;
}

/* scrollbar itself */
::-webkit-scrollbar-thumb {
  background-color: #babac0;
  border-radius: 16px;
  border: 5px solid #fff;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a5;
  border: 4px solid #f4f4f4;
}

/* set button(top and bottom of the scrollbar) */
::-webkit-scrollbar-button {
  display: none;
}



a {
  padding: 0;
  margin: 0;
  transition: all 0.5s ease;
  text-decoration: none !important;
  color: #000126 !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700 !important;
}

address,
dl,
ol,
p,
ul {
  margin: 0 !important;
}

a:hover,
a:focus,
a:active {
  outline: none;
  box-shadow: none;
  text-decoration: none;
}


.container {
  width: 100% !important;
  max-width: 100%;
  padding: 0 100px;
  margin: 0 auto;
}

.lazy-loading{
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dot{
    width: 6px;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    margin: 0 7px !important;
}


.ant-dropdown {
  min-width: 244px;
  margin-right: 20px;
  margin-top: 9px;
  max-width: 300px;
}

.ant-dropdown-menu-title-content {
    white-space: normal;
}
.ant-upload-select {
  background: #fff !important;
}

.custom-modal .ant-modal-content {
  padding: 0 !important;
  border-radius: 4px;
  background-image: linear-gradient(to bottom, rgba(248, 251, 255, 0.5), rgba(225, 233, 247, 0.5));
}
.custom-modal .ant-modal-header {
  padding: 16px !important;
  border-bottom: 1px solid rgba(198, 209, 230, 0.5);
  background-color: transparent;
}
.custom-modal .ant-modal-header  .ant-modal-title{
  color: #3a3a3c;
}
.custom-modal .ant-modal-body {
  padding: 5px 30px 15px 30px;
}

.custom-modal .ant-modal-footer {
  padding: 15px 35px;
}
.custom-modal .ant-form-item{
  margin-top: 20px !important;
}
.custom-modal .ant-form-item .ant-form-item-label {
  padding-bottom: 2px !important;
}
.custom-modal .ant-form-item label{
  color:#1b1b1f !important;
  padding-bottom: 2px !important;
  font-weight: 600;
}

.table-dropdown .ant-space {
  border: 1px solid #8d96a7 !important;
  width: 120px;
  padding: 0 10px;
  border-radius: 6px;
  height: 30px;
  justify-content: space-between;
}
.custom-tabs .ant-tabs-tab {
  border-radius: 4px;
  padding: 8px 45px;
  margin: 0 !important;
}
.custom-tabs .ant-tabs-nav-list {
  border-radius: 4px;
  border: solid 1px #c6d1e6;
  background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
}

.custom-tabs .ant-tabs-nav-wrap {
  justify-content: center;
 
  
}

.custom-tabs .ant-tabs-nav-wrap::before {
  content: none !important;
}

.custom-tabs .ant-tabs-nav::before {
  content: none;
}

.custom-tabs .ant-tabs-tab-active {
  background-image: linear-gradient(to bottom, #5d92c5, #35569a);
  border-bottom: 0 !important;
  outline: none;
}

.custom-tabs .ant-tabs-tab-active>div {
  color: #fff !important;
  border: 0 !important;
}
.ant-tabs-ink-bar.ant-tabs-ink-bar-animated{
  display: none;
}
.custom-modal  .form-items{
  margin-top:20px ;
}
.custom-modal  .form-items .ant-picker {
  height: 40px;
}
.custom-modal  .form-items label{
  font-weight: 600;
  margin-bottom: 5px;
}
.PhoneInputInput {
  background: #ffffff;
  border-width: 1px;
  border-style: solid;
  border-color: #d9d9d9;
  height: 40px;
  transition: all 0.2s;
  border-radius: 8px;
  padding: 7px 11px;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.88);
}
.PhoneInputInput:active,.PhoneInputInput:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
  outline: 0;
  background-color: #ffffff;
}
.PhoneInputInput::placeholder {
  color: #bfc8d2;
  opacity: 1; /* Firefox */
}

.PhoneInputInput::-ms-input-placeholder { /* Edge 12 -18 */
  color: #bfc8d2;
}
.PhoneInput--disabled .PhoneInputInput{
  background: #dddddd;
}
.PhoneInput--disabled select:disabled {
  opacity: 0 !important;
}
.large-folder img{
  width: 48px;
  height: 48px;
}
.large-folder a{
  font-size: 24px;
}
.color-green{
  color: #008000;
}
.member-modal .ant-modal-body {
  padding: 5px 15px 15px 15px;
  max-height: 500px;
  overflow-y: auto;
}
.brand-logo {
  width: 304px;
  height: 78px;
}
.filter-dot {
    display: inline-block;
    background-color: red;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
    position: absolute;
    top: -3px;
    right: 0px;
  }


  

  .chat-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 80vh;
  overflow-y: auto;
}

.message-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.message-wrapper.own {
  justify-content: flex-end;
}

.message-wrapper.other {
  justify-content: flex-start;
}

.avatar {
  width: 36px;
  height: 36px;
  background-color: #3182ce;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.message-bubble {
  background-color: #eee;
  padding: 10px 12px;
  border-radius: 12px;
  max-width: 70%;
  position: relative;
  word-break: break-word;
}

.message-wrapper.own .message-bubble {
  background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
  text-align: right;
}

.message-img {
  max-width: 200px;
  border-radius: 10px;
}

.message-text {
  display: block;
  font-size: 14px;
}

.message-time {
  font-size: 11px;
  color: gray;
  margin-top: 4px;
  text-align: right;
}

.skeleton-wrapper {
  padding: 16px;
}
.message-img {
  max-width: 200px;
  border-radius: 8px;
}

.message-link, .message-pdf {
  color: #007bff;
  text-decoration: underline;
  font-weight: 500;
}


.attributes-slider {
  margin-bottom: 20px;
}

.slider-item {
  padding: 10px;
}

.attributes-img {
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.view-pdf-btn {
  margin-top: 10px;
  padding: 4px 12px;
  background-color: #4893ca;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Slider arrow styles */
.slick-prev,
.slick-next {
  z-index: 1;
  width: 30px;
  height: 30px;
}

.slick-prev {
  left: -15px;
}

.slick-next {
  right: -15px;
}

.slick-dots {
  bottom: -25px;
}

/* Modal styles */
.ant-modal-content {
  border-radius: 8px;
}
.custom-arrow-btn {
  background-color: #1890ff; /* Use theme primary color */
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
      width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.custom-arrow-btn:hover {
  background-color: #40a9ff; /* Slightly lighter on hover */
}

.custom-arrow-prev {
  position: absolute;
  top: 50%;
  left: 0px;
  transform: translateY(-50%);
}

.custom-arrow-next {
  position: absolute;
  top: 50%;
  right: 0px;
  transform: translateY(-50%);
}
.notification-setting-modal .ant-modal-footer {
  display: none;
}

span.ant-menu-title-content {
  color: #3a3a3c !important;
  font-size: 16px !important;
}

.ant-menu-item .ant-badge {
  color: #3a3a3c !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}
.notification-dot {
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  flex-shrink: 0;
}

.notification-text {
  color: rgba(0, 0, 0, 0.85);
  flex: 1;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
}