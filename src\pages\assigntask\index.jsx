import React, { useState, memo, useMemo, useCallback } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import BaseInput from "../../components/shared/inputs";
import FlatButton from "../../components/shared/button/flatbutton";
import AssignCard from "../../components/shared/card/assigncard";
import CustomModal from "../../components/shared/modal";
import PageTitle from "../../components/shared/pagetitle";
import AssignItems from "../../components/shared/card/assignitems";
import CreateTaskForm from "../../components/partial/modalforms/createtaskform";
import AssignFilterForm from "../../components/partial/modalforms/assignfilterform";
import IconButton from "../../components/shared/button/iconbutton";
import { Skeleton, Empty } from "antd";
import { useFetch } from "../../hooks";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import "./assigntask.css";

const TASK_COLUMNS = [
  {
    id: "pending",
    title: "Assigned",
    status: "pending",
    avatarClass: "assign-user-avatar",
    nextStatus: "in-process",
  },
  {
    id: "in-process",
    title: "In Process",
    status: "in-process",
    avatarClass: "assign-user-avatar avatar-process",
    nextStatus: "completed",
  },
  {
    id: "completed",
    title: "Completed",
    status: "completed",
    avatarClass: "assign-user-avatar avatar-complete",
    nextStatus: "verified",
  },
];

const LoadingSkeleton = () => (
  <div className="py-5 px-3">
    <Skeleton avatar paragraph={{ rows: 3 }} active />
  </div>
);

const AssignTask = () => {
  const user = window.user?.user;
  const [modals, setModals] = useState({
    create: false,
    filter: false,
  });
  const { loading: projectLoading, data: projectData } = useFetch("project", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 1000 },
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    filter_by: "",
    start_at: "",
    end_at: "",
    page: 1,
    limit: 1000,
  });
  const [selectedProject, setSelectedProject] = useState("all");
  const [isFilterApplied, setIsFilterApplied] = useState(false);

  const { loading: updateLoading, postData } = useFetch("update_task", {
    type: "submit",
  });
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });

  const {
    data: taskData,
    loading,
    fetchApi: fetchTask,
    setQueryParams,
  } = useFetch("task", {
    enablePagination: false,
    defaultQueryParams: {
      page: 1,
      limit: 1000,
    },
  });

  const filteredData = useMemo(() => {
    if (!searchTerm.trim() || !taskData) return taskData;

    const searchValue = searchTerm.toLowerCase();
    return taskData.filter(
      (task) =>
        task.title?.toLowerCase().includes(searchValue) ||
        task.description?.toLowerCase().includes(searchValue) ||
        task.assignee?.name?.toLowerCase().includes(searchValue)
    );
  }, [taskData, searchTerm]);

  const toggleModal = (modalType) => {
    setModals((prev) => ({
      ...prev,
      [modalType]: !prev[modalType],
    }));
  };
  const handleProjectChange = useCallback(
    (value) => {
      setSelectedProject(value);
      setQueryParams((prevParams) => ({
        ...prevParams,
        project_id: value === "all" || value === "" ? "" : value,
      }));
    },
    [setQueryParams]
  );
  const handleFilterApply = (formData) => {
    const newFilters = {
      ...formData,
      page: 1,
      limit: 1000,
    };

    setFilters(newFilters);
    setQueryParams(newFilters);
    setIsFilterApplied(
      Boolean(formData.filter_by || formData.start_at || formData.end_at)
    );
    setModals((prev) => ({ ...prev, filter: false }));
  };

  const handleResetFilters = () => {
    const resetFilters = {
      filter_by: "",
      start_at: "",
      end_at: "",
      page: 1,
      limit: 1000,
    };

    setFilters(resetFilters);
    setQueryParams(resetFilters);
    setIsFilterApplied(false);
    setModals((prev) => ({ ...prev, filter: false }));
  };

  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value);
  }, []);

  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;

    if (source.droppableId === destination.droppableId) return;

    const sourceColumn = TASK_COLUMNS.find(
      (col) => col.id === source.droppableId
    );
    const destColumn = TASK_COLUMNS.find(
      (col) => col.id === destination.droppableId
    );

    if (destColumn.status !== sourceColumn.status) {
      const fd = new FormData();
      fd.append("status", destColumn.status);

      postData(
        fd,
        (res) => {
          if (res?.statusCode === 200) {
            fetchTask();
          }
        },
        draggableId
      );
    }
  };

  const canCreateTask = useMemo(() => {
    return (
      userObj?.role === "company" ||
      userObj?.policies?.some(
        (policy) => policy.module === "task" && policy.can_create
      )
    );
  }, [userObj]);

  const renderAssignCards = (title, statusKey, avatarClass) => {
    const statusFilteredData = filteredData?.filter(
      (item) => item.status === statusKey
    );

    return (
      <div className="col-12 col-md-6 col-lg-4" key={statusKey}>
        <AssignCard title={title}>
          {loading ? (
            <LoadingSkeleton />
          ) : (
            <Droppable droppableId={statusKey}>
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  style={{ minHeight: "100px" }}
                >
                  {statusFilteredData?.length ? (
                    statusFilteredData.map((item, index) => (
                      <Draggable
                        key={item._id}
                        draggableId={item._id}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={{
                              ...provided.draggableProps.style,
                              opacity: snapshot.isDragging ? 0.5 : 1,
                            }}
                          >
                            <AssignItems
                              className={avatarClass}
                              {...item}
                              index={index + 1}
                            />
                          </div>
                        )}
                      </Draggable>
                    ))
                  ) : (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="No tasks found"
                      className="py-5"
                    />
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          )}
        </AssignCard>
      </div>
    );
  };

  const renderActionButtons = () => (
    <>
      <BaseInput
        type="select"
        defaultValue={selectedProject}
        options={[
          { value: "all", label: "All Task" },
          ...(projectData?.map((item) => ({
            value: item._id,
            label: item.title,
          })) || []),
        ]}
        onChange={handleProjectChange}
        value={selectedProject}
        loading={projectLoading}
        showSearch={true}
        style={{ width: "300px" }}
      />

      <div>
        {canCreateTask && (
          <FlatButton
            title="+ Add New"
            className="mx-auto add-new-btn me-3"
            onClick={() => toggleModal("create")}
          />
        )}
      </div>
      <div>
        <BaseInput
          name="search"
          placeholder="Search Task"
          className="me-2"
          icon={<img src="/admin/assets/img/search-icon.png" alt="search" />}
          value={searchTerm}
          onChange={handleSearch}
        />
      </div>
      <div>
        <IconButton
          title={
            <>
              Filter
              {isFilterApplied && <span className="filter-dot"></span>}
            </>
          }
          className="mx-auto ms-3 share-btn"
          icon={<img src="/admin/assets/img/filter-icon.png" alt="filter" />}
          iconPosition="right"
          onClick={() => toggleModal("filter")}
        />
      </div>
    </>
  );

  return (
    <>
      <InnerLayout>
        <PageTitle title="Tasks" buttons={renderActionButtons()} />
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="row mb-4 mt-4">
            {TASK_COLUMNS.map(({ id, title, status, avatarClass }) =>
              renderAssignCards(title, status, avatarClass)
            )}
          </div>
        </DragDropContext>
      </InnerLayout>

      <CustomModal
        width={900}
        title="Create Task"
        onCancel={() => toggleModal("create")}
        open={modals.create}
        className="custom-modal"
        footer={false}
      >
        <CreateTaskForm
          onCancel={() => toggleModal("create")}
          fetchTask={fetchTask}
        />
      </CustomModal>

      <CustomModal
        title="Filters"
        onCancel={() => toggleModal("filter")}
        open={modals.filter}
        className="custom-modal"
        footer={false}
      >
        <AssignFilterForm
          onApplyFilter={handleFilterApply}
          onCancel={() => toggleModal("filter")}
          isFilterRemove={handleResetFilters}
          projectdetail="false"
        />
      </CustomModal>
    </>
  );
};

export default memo(AssignTask);
