import React, { useState, useEffect, useCallback } from "react";
import { Upload, message, Image } from "antd";
import { PlusOutlined } from "@ant-design/icons";

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

const CustomUpload = ({
  value = [],
  onChange,
  allowedTypes = ["application/pdf"],
  maxSizeMB = 20,
  resetImages = false,
  maxFiles = 10,
}) => {
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState("");
  const [previewOpen, setPreviewOpen] = useState(false);

  const initializeFiles = useCallback(
    (files) =>
      files.map((item, index) => ({
        uid: `existing-${index}`,
        name:
          typeof item === "string"
            ? item.split("/").pop()
            : item.name || `File ${index + 1}`,
        status: "done",
        url: typeof item === "string" ? item : undefined,
        originFileObj: typeof item !== "string" ? item : undefined,
        type: typeof item !== "string" ? item.type : undefined,
      })),
    []
  );

  useEffect(() => {
    if (resetImages && value.length === 0) {
      setFileList([]);
      return;
    }

    const newFiles = initializeFiles(value);
    setFileList(newFiles);
  }, [value, resetImages, initializeFiles]);

  const handleChange = useCallback(
    ({ file, fileList: updatedFileList }) => {
      // Check if total files exceed limit
      if (updatedFileList.length > maxFiles) {
        // Use the same consolidated error approach
        if (!window.uploadErrorShown) {
          window.uploadErrorShown = true;
          message.error(`You can only upload up to ${maxFiles} files`);
          setTimeout(() => {
            window.uploadErrorShown = false;
          }, 1000);
        }
        return;
      }

      const newFileList = updatedFileList.map((file) => ({
        ...file,
        status: file.status || "done",
      }));

      setFileList(newFileList);

      if (onChange) {
        const completedFiles = newFileList
          .filter((file) => file.status === "done" || file.originFileObj)
          .map((file) => file.originFileObj || file.url);
        onChange(completedFiles);
      }
    },
    [onChange, maxFiles]
  );

  const handlePreview = useCallback(async (file) => {
    if (file.type === "application/pdf") {
      window.open(
        file.url || URL.createObjectURL(file.originFileObj),
        "_blank"
      );
      return;
    }

    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  }, []);

  const beforeUpload = useCallback(
    (file, uploadFileList) => {
      const errors = [];

      // Check total files including currently uploading files
      const totalFiles = fileList.length + uploadFileList.length;
      if (totalFiles > maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed`);
      }

      const isAllowedType = allowedTypes.includes(file.type);
      if (!isAllowedType) {
        errors.push(`Please upload ${allowedTypes.join(", ")} files only`);
      }

      const isLtMaxSize = file.size / 1024 / 1024 < maxSizeMB;
      if (!isLtMaxSize) {
        errors.push(`File must be smaller than ${maxSizeMB}MB`);
      }

      // Show consolidated error message if there are any errors
      if (errors.length > 0) {
        // Use a flag to prevent multiple error messages for bulk uploads
        if (!window.uploadErrorShown) {
          window.uploadErrorShown = true;
          message.error(errors.join("; "));
          // Reset the flag after a short delay
          setTimeout(() => {
            window.uploadErrorShown = false;
          }, 1000);
        }
        return Upload.LIST_IGNORE;
      }

      return true;
    },
    [fileList.length, maxFiles, allowedTypes, maxSizeMB]
  );

  const handleRemove = useCallback(
    (file) => {
      const updatedList = fileList.filter((item) => item.uid !== file.uid);
      setFileList(updatedList);

      if (onChange) {
        const completedFiles = updatedList
          .filter((f) => f.status === "done" || f.originFileObj)
          .map((f) => f.originFileObj || f.url);
        onChange(completedFiles);
      }
    },
    [fileList, onChange]
  );

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <>
      <Upload
        listType="picture-card"
        fileList={fileList}
        onChange={handleChange}
        beforeUpload={beforeUpload}
        onRemove={handleRemove}
        onPreview={handlePreview}
        multiple={true}
        customRequest={({ onSuccess }) => setTimeout(() => onSuccess("ok"), 0)}
      >
        {fileList.length >= maxFiles ? null : uploadButton}
      </Upload>

      {previewImage && (
        <Image
          wrapperStyle={{ display: "none" }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
          }}
          src={previewImage}
        />
      )}
    </>
  );
};

export default React.memo(CustomUpload);
