import React, { memo } from "react";
import InnerLayout from "../layout/innerlayout";
import PageTitle from "../pagetitle";
import BaseInput from "../inputs";
import FlatButton from "../button/flatbutton";
const UserRoleLayout = ({ children, title, handleSearch, handleAddNew }) => (
  <InnerLayout>
    <PageTitle
      title={title}
      buttons={
        <>
          <div>
            <BaseInput
              name="search"
              placeholder="Search"
              icon={<img src="/admin/assets/img/search-icon.png" />}
              onChange={handleSearch}
            />
          </div>
          <div>
            <FlatButton
              title="+ Add New"
              className="mx-auto add-new-btn"
              onClick={handleAddNew}
            />
          </div>
        </>
      }
    />
    {children}
  </InnerLayout>
);

export default memo(UserRoleLayout);
