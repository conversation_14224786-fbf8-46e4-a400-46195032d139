import{r as t,j as e,h as _,b as V,R as q,a as H,l as J,S as U}from"./index-DS_hjASx.js";import{A as I,a as N,b as W,I as X,P as Z}from"./index-B0HTmHEd.js";import{T as ee,F as se,a as D,u as g,E as le,B as ae,e as ie}from"./rules-_ZUHH97D.js";import{C as te}from"./customtable-GeATQPOH.js";import{S as oe}from"./index-C-b0xv5C.js";import{B as ce}from"./backbutton-C1zuWfk9.js";import{A as ne}from"./assignfilterform-D8gUecUk.js";import{I as T}from"./iconbutton-CAjTjxyq.js";import{R as b}from"./index-diLtVJ_H.js";import{A as re}from"./addfolder-BLm3JZSn.js";/* empty css                 */import{C as de}from"./tabledata-s2KXI6uj.js";import"./index-B37y91Rn.js";import"./notification-7LrlFXRF.js";import"./Pagination-DGfRHkE5.js";const me=({children:c,imageCount:x})=>{const[n,j]=t.useState(0);function r(o){const{className:u,onClick:d}=o;return e.jsx("div",{className:u,onClick:d,children:e.jsx("img",{src:"../assets/img/right-arrow-slider.png",alt:"Next"})})}function l(o){const{className:u,onClick:d}=o;return e.jsx("div",{className:u,onClick:d,children:e.jsx("img",{src:"../assets/img/left-arrow-slider.png",alt:"Previous"})})}const i={dots:!0,infinite:!1,speed:500,slidesToShow:1,slidesToScroll:1,beforeChange:(o,u)=>j(u),nextArrow:x>1&&n<x-1?e.jsx(r,{}):null,prevArrow:x>1&&n>0?e.jsx(l,{}):null};return e.jsx(oe,{...i,children:c})},he=({title:c,address:x,members:n,start_at:j,project_status:r,completion_at:l,user:i,type:o})=>{const[u,d]=t.useState(!1);return e.jsxs("div",{children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail mb-2",children:[e.jsx("h2",{className:"color-black font-16",children:"Project Name"}),e.jsx("p",{className:"color-black font-16",children:c})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail mb-2",children:[e.jsx("h2",{className:"color-black font-16",children:"Location"}),e.jsx("p",{className:"color-dark-blue font-16",children:x})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail mb-2",children:[e.jsx("h2",{className:"color-black font-16",children:"Type"}),e.jsx("p",{className:"color-dark-blue font-16",children:o})]})})]}),e.jsxs("div",{className:"row mt-4",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail mb-2",children:[e.jsx("h2",{className:"color-black font-16 mb-1",children:"Assignee Members"}),e.jsxs("div",{className:"d-flex align-items-center",children:[n.slice(0,4).map(s=>e.jsx(ee,{title:s._id===window.user.user._id?"You":s.name,children:e.jsx(I,{src:s.image_url,title:s._id===window.user.user._id?"You":s.name,children:!s.image&&s.name[0]})},s._id)),n.length>4&&e.jsx("span",{className:"text-blue-500 cursor-pointer ms-2 font-12",onClick:()=>d(!0),children:"View All Members"})]})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail",children:[e.jsx("h2",{className:"color-black font-16",children:"Date"}),e.jsx("p",{className:"color-dark-blue font-16",children:_(j).format("D MMM, YYYY")})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail",children:[e.jsx("h2",{className:"color-black font-16",children:"Created by"}),e.jsxs("p",{className:"color-dark-blue font-16",children:[i==null?void 0:i.name," ",(i==null?void 0:i._id)===window.user.user._id?"(me)":""]})]})})]}),e.jsxs("div",{className:"row mt-4",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail",children:[e.jsx("h2",{className:"color-black font-16",children:"Status"}),e.jsx("p",{className:`font-16 ${r==="in-process"?"color-yellow ":r==="completed"?"color-dark-blue":""}`,children:r==="in-process"?"In Process":r==="completed"?"Completed":""})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:e.jsxs("div",{className:"body-detail",children:[e.jsx("h2",{className:"color-black font-16",children:"Completion Date"}),e.jsx("p",{className:"color-dark-blue font-16",children:_(l).format("D MMM, YYYY")})]})})]}),e.jsx(N,{title:"All Members",onCancel:()=>d(!1),open:u,className:"custom-modal member-modal",footer:!1,children:e.jsx("div",{className:"",children:n.map(s=>e.jsxs("div",{className:"d-flex align-items-center mb-2",children:[e.jsx(I,{size:36,src:s.image_url,alt:s.name}),e.jsxs("span",{className:"text-lg font-medium ms-2",children:[" ",s.name," ",(s==null?void 0:s._id)===window.user.user._id?"(me)":""]})]},s._id))})})]})},xe=({onCancel:c})=>{const{viewType:x,setViewType:n}=V(),[j,r]=t.useState(x),l=o=>{r(o.target.value)},i=()=>{n(j),c()};return e.jsxs(se,{name:"filter",layout:"vertical",onFinish:()=>setIsViewingModalOpen(!1),initialValues:{remember:!0},autoComplete:"off",children:[e.jsx(b.Group,{onChange:l,value:j,children:e.jsxs(W,{direction:"vertical",children:[e.jsx(b,{value:1,children:"List"}),e.jsx(b,{value:2,children:"Large Thumbnails "})]})}),e.jsx("div",{className:"text-end mt-5",children:e.jsx(D,{title:"Save",className:"add-new-btn",onClick:i})})]})},ue=t.memo(xe),je=q.memo(he),pe=()=>{var k,A,F,P,M;let{id:c}=H();const{viewType:x}=V(),n=(k=window.user)==null?void 0:k.user,{showAlert:j}=ie(),{loading:r,data:l,fetchApi:i}=g("project",{slug:`/${c}`}),{loading:o,data:u,fetchApi:d,pagination:s,setQueryParams:p}=g("directories",{slug:`/?project_id=${c}&`,enablePagination:!0,defaultQueryParams:{page:1,limit:10}}),{postData:Y}=g("delete_directories",{type:"submit"}),{data:m}=g("get_profile",{type:"mount",slug:`/${n._id}`,enablePagination:!1}),[f,B]=t.useState(""),[E,v]=t.useState(!1),[R,w]=t.useState(!1),[$,y]=t.useState(!1),[O,S]=t.useState(!1);t.useEffect(()=>{i(),d()},[c]),t.useEffect(()=>{p(a=>({...a,keyword:f}))},[f]);const z=a=>{p(h=>({...h,...a})),S(!0),w(!1)},L=async a=>{(await j({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&Y("",K,a)},Q=(a,h)=>{p(C=>({...C,page:a,limit:h,keyword:f}))},G=J.debounce(a=>{const h=a.target.value;B(h),p(C=>({...C,keyword:h,page:1}))},300);if(r&&o)return e.jsx("div",{style:{display:"flex",justifyContent:"center",height:"100vh",alignItems:"center"},children:e.jsx(U,{size:"large"})});if(window.lodash.isEmpty(l))return e.jsx("div",{style:{display:"flex",justifyContent:"center",height:"100vh",alignItems:"center"},children:e.jsx(le,{description:"No Data Available"})});const K=a=>{a.statusCode===200&&d()};return e.jsxs(e.Fragment,{children:[e.jsxs(X,{children:[e.jsx(Z,{title:e.jsx(ce,{}),buttons:e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx(ae,{name:"search",placeholder:"Search",value:f,onChange:G,icon:e.jsx("img",{src:"/admin/assets/img/search-icon.png"})})}),((m==null?void 0:m.role)==="company"||(m==null?void 0:m._id)===((A=l==null?void 0:l.user)==null?void 0:A._id)&&((F=m==null?void 0:m.policies)==null?void 0:F.some(a=>a.module==="project"&&a.can_create)))&&e.jsx("div",{children:e.jsx(D,{title:"+ Add Folder",className:"mx-auto add-new-btn me-3",onClick:()=>v(!0)})}),e.jsx("div",{children:e.jsx(T,{title:"Viewing",className:"mx-auto ms-3 share-btn",icon:e.jsx("img",{src:"/admin/assets/img/viewing-icon.png"}),iconPosition:"right",onClick:()=>y(!0)})}),e.jsx("div",{children:e.jsx(T,{title:e.jsxs(e.Fragment,{children:["Filter",O&&e.jsx("span",{className:"filter-dot"})]}),className:"mx-auto ms-3 share-btn",icon:e.jsx("img",{src:"/admin/assets/img/filter-icon.png"}),iconPosition:"right",onClick:()=>w(!0)})})]})}),e.jsx("div",{className:"detail-slider-box mt-4",children:e.jsxs("div",{className:"row gx-0 align-items-center",children:[e.jsx("div",{className:"col-12 col-sm-5 col-md-4 col-lg-4",children:e.jsx("div",{className:"slider-container",children:e.jsx(me,{imageCount:((P=l==null?void 0:l.image_url)==null?void 0:P.length)||0,children:((M=l==null?void 0:l.image_url)==null?void 0:M.length)>0&&l.image_url.map((a,h)=>e.jsx("div",{className:"slider-img",children:e.jsx("img",{src:a,alt:`Slider image ${h+1}`})},h))})})}),e.jsx("div",{className:"col-12 col-sm-7 col-md-8 col-lg-8 p-4",children:e.jsx(je,{...l})})]})}),e.jsx("div",{className:"detail-table mt-4 mb-5",children:e.jsx(te,{columns:de(L,x,m),data:u,loading:o,rowKey:"_id",pagination:{current:s==null?void 0:s.currentPage,total:s==null?void 0:s.count,pageSize:s==null?void 0:s.perPage},showPagination:!0,onChange:Q})})]}),e.jsx(N,{title:"Add Folder",onCancel:()=>v(!1),open:E,className:"custom-modal",footer:!1,children:e.jsx(re,{onCancel:()=>v(!1),onSuccess:d,projectId:c})}),e.jsx(N,{title:"Filters",onCancel:()=>w(!1),open:R,className:"custom-modal",footer:!1,children:e.jsx(ne,{projectdetail:"true",onApplyFilter:z,isFilterRemove:()=>S(!1)})}),e.jsx(N,{title:"Viewing Option",onCancel:()=>y(!1),open:$,className:"custom-modal",footer:!1,children:e.jsx(ue,{onCancel:()=>y(!1)})})]})},Ie=t.memo(pe);export{Ie as default};
