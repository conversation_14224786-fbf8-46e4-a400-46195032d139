import React, { memo, useEffect, useRef, useState } from "react";
import useLoadConversations from "../../../hooks/useLoadConversations";
import { message, Skeleton } from "antd";
import moment from "moment";
import { useNavigate } from "react-router-dom";

const ChatMessage = ({ data }) => {
  const navigate = useNavigate();
  const socket = window.socket;
  const user = window.user.user;
  const { messages, loading } = useLoadConversations(socket, data?._id);
  const [allMessages, setAllMessages] = useState(messages || []);
  const messagesEndRef = useRef(null);
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (messages && messages.length > 0) {
      setAllMessages(messages);
    }
  }, [messages]);

  useEffect(() => {
    if (socket) {
      socket.on("message", (res) => {
        if (res.statusCode === 200) {
          setAllMessages((prevMessages) => {
            const newMessage = {
              _id: res.data.task_id,
              message: res.data.message,
              type: res.data.type,
              url: res.data.url,
              user: res.data.user,
              created_at: res.data.created_at,
            };

            const messageExists = prevMessages.some(
              (msg) => msg._id === res._id
            );
            if (!messageExists) {
              return [...prevMessages, newMessage];
            }
            return prevMessages;
          });
        } else {
          message.error(
            "This task has been deleted, you cannot send messages now."
          );
          navigate("/assign-task");
        }
      });
    }

    return () => {
      socket?.off("message");
    };
  }, [socket]);

  useEffect(() => {
    scrollToBottom();
  }, [allMessages]);

  const isImageUrl = (url) => /\.(jpeg|jpg|gif|png|webp)$/i.test(url);

  const isPdfUrl = (url) => /\.pdf$/i.test(url);

  const isValidUrl = (text) => {
    try {
      new URL(text);
      return true;
    } catch (_) {
      return false;
    }
  };

  const renderContent = (msg) => {
    const text = msg.message;

    if (msg.type === "image") {
      // Only display images, no PDFs or other file types
      if (msg.url) {
        return (
          <img
            src={msg.url}
            alt="attachment"
            className="message-img"
            style={{
              maxWidth: "100%",
              maxHeight: "200px",
              borderRadius: "8px",
            }}
          />
        );
      }
    }

    if (msg.type === "text") {
      return <span className="message-text">{text}</span>;
    }

    return null;
  };

  const renderMessage = (msg) => {
    const isOwnMessage = msg.user?._id === user?._id;
    const messageTime = moment(msg.created_at).format("D/M/YY h:mm A");
    const uniqueKey = `${msg._id}-${msg.created_at}`;
    return (
      <div
        key={uniqueKey}
        className={`message-wrapper ${isOwnMessage ? "own" : "other"}`}
      >
        {!isOwnMessage && (
          <div className="avatar">
            {window.helper.getInitials(msg.user?.name)}
          </div>
        )}
        <div className={`message-bubble ${isOwnMessage ? "own" : "other"}`}>
          {renderContent(msg)}
          <div className="message-time">{messageTime}</div>
        </div>
      </div>
    );
  };

  const sortedMessages = [...allMessages].sort(
    (a, b) => new Date(a.created_at) - new Date(b.created_at)
  );

  return (
    <div className="chat-container">
      {loading ? (
        <div className="skeleton-wrapper">
          {Array.from({ length: 4 }).map((_, idx) => (
            <Skeleton key={idx} avatar paragraph={{ rows: 1 }} active />
          ))}
        </div>
      ) : (
        <>
          {sortedMessages.map(renderMessage)}
          <div ref={messagesEndRef} />
        </>
      )}
    </div>
  );
};

export default memo(ChatMessage);
