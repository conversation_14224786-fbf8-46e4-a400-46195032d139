import React, { useState } from "react";
import { Table, Pagination } from "antd";

const CustomTable = ({
  columns,
  data,
  loading,
  rowKey,
  showPagination,
  pagination,
  onChange,
  scroll = { x: 1000 },
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState("checkbox");
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {},
    onSelect: (record, selected, selectedRows) => {},
    onSelectAll: (selected, selectedRows, changeRows) => {},
  };

  return (
    <>
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data}
        pagination={false}
        loading={loading}
        rowKey={rowKey}
        scroll={scroll}
      />
      {showPagination && (
        <div className="custom-pagination d-flex justify-content-end mt-4">
          <Pagination
            current={pagination.current || 1} // Fallback to 1 if undefined
            total={pagination.total || 0} // Fallback to 0 if undefined
            pageSize={pagination.pageSize || 10} // Fallback to 10 if undefined
            onChange={onChange}
          />
        </div>
      )}
    </>
  );
};

export default CustomTable;
