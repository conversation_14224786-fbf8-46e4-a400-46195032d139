import React, { memo } from "react";
import CustomModal from "../../components/shared/modal";
import AddUserForm from "../../components/partial/modalforms/adduserform";
const EmplpoyeeModal = memo(
  ({ isModalOpen, onClose, editData, refreshDataTable }) => (
    <CustomModal
      title={editData ? "Edit Employee" : "Add New Employee"}
      width={900}
      open={isModalOpen}
      onCancel={onClose}
      className="custom-modal"
      footer={false}
    >
      <AddUserForm
        onCancel={onClose}
        refreshDataTable={refreshDataTable}
        editData={editData}
      />
    </CustomModal>
  )
);

export default EmplpoyeeModal;
