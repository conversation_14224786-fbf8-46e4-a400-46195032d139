import { Form } from "antd";
import React, { memo } from "react";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { DateTime } from "luxon";

const ProjectFilterForm = ({ onFilterApply, onCancel, isFilterRemove }) => {
  let user = window.user.user;
  const [form] = Form.useForm();
  const { data } = useFetch("employee", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 2000 },
  });

  const serializeFilters = (filters) => {
    const params = new URLSearchParams();

    if (filters.address) params.append("address", filters.address);
    if (filters.start_at) params.append("start_at", filters.start_at);

    if (filters.members?.length) {
      filters.members.forEach((memberId) =>
        params.append("members[]", memberId)
      );
    }

    return params; // Return URLSearchParams directly
  };

  const areFiltersEmpty = (filters) => {
    return (
      !filters.address &&
      !filters.start_at &&
      (!filters.members || filters.members.length === 0)
    );
  };

  const onFinish = (values) => {
    const formattedFilters = {
      address: values.address || "",
      start_at: values.start_at
        ? DateTime.fromJSDate(values.start_at.toDate()).toISODate()
        : "",
      members: values.members || [], // Ensure members stay an array here
    };

    if (areFiltersEmpty(formattedFilters)) {
      onCancel();
      return;
    }

    onFilterApply(formattedFilters);
  };

  const onReset = () => {
    // const currentValues = form.getFieldsValue();
    // if (areFiltersEmpty(currentValues)) {
    //   onCancel(); // Prevent reset API call if already empty
    //   return;
    // }

    form.resetFields();
    onFilterApply({
      page: 1,
      limit: 9, // Default pagination parameters
      address: "",
      start_at: "",
      members: [], // Empty members
    });
    isFilterRemove();
    onCancel();
  };

  return (
    <Form
      name="project-filter"
      layout="vertical"
      onFinish={onFinish}
      form={form}
      autoComplete="off"
    >
      <BaseInput name="address" placeholder="Enter location" label="Location" />
      <BaseInput name="start_at" type="datepiker" placeholder="" label="Date" />
      {user.role === "company" && (
        <BaseInput
          name="members"
          mode="multiple"
          type="select"
          placeholder="Select members"
          label="Assigned Members"
          options={data?.map((item) => ({
            value: item._id,
            label: item.name,
          }))}
        />
      )}

      <div className="text-end mt-5">
        <FlatButton title="Reset" className="reset-btn" onClick={onReset} />
        <FlatButton title="Apply" className="add-new-btn" htmlType="submit" />
      </div>
    </Form>
  );
};

export default memo(ProjectFilterForm);
