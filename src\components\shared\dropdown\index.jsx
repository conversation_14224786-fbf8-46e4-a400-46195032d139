import React from 'react';
import { Dropdown, Space } from 'antd';
import { DownOutlined } from '@ant-design/icons';

const CustomDropdown = ({title ,items,className ,icon}) => (
  <Dropdown
    menu={{items}}
    trigger={['click']}
    className={className}
  >
    <a onClick={(e) => e.preventDefault()}>
      <Space>
        {title}
        {icon && <DownOutlined />}
      </Space>
    </a>
  </Dropdown>
);
export default CustomDropdown;
