import React from 'react'
import SuscriptionCard from '../../shared/card/suscriptioncard'
import SuscriptionCardDetail from '../configdata/sucriptioncarddetail'

const MonthlyPlan = () => {
  return (
    <div className="row">
        {SuscriptionCardDetail?.map((item, index) =>
            <div className="col-12 col-md-6 col-lg-4 col-xl-3" key={index}>
                 <SuscriptionCard {...item} />
            </div>
          )}
    </div>
  )
}

export default MonthlyPlan