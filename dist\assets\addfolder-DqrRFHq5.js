import{r as i,j as e}from"./index-C6D6r1Zc.js";import{F as d,u as c,B as _,a as q}from"./rules-CIBIafmf.js";import{C as v}from"./index-D5NY69qa.js";const y=({onCancel:p,onSuccess:f,projectId:F,parentId:m,type:r,title:l})=>{const[n]=d.useForm(),{loading:g,postData:h}=c("create_directories",{type:"submit"}),{loading:x,postData:b}=c("upload_multiple_files",{type:"submit"}),[s,j]=i.useState([]),[S,u]=i.useState(!1),D=a=>{const t=new FormData;u(!0),m&&t.append("parent_id",m),r&&(s==null?void 0:s.length)>0&&s.forEach(o=>{t.append("files",o)}),t.append("project_id",F);for(const o in a)o!=="document"&&t.append(o,a[o]);(r==="upload-pdf"?b:h)(t,N)},N=a=>{a.statusCode===200&&(p(),n.resetFields(),f(),setTimeout(()=>u(!1),0))};return e.jsxs(d,{name:"create-folder",layout:"vertical",onFinish:D,initialValues:{remember:!0},form:n,children:[r==="upload-pdf"?e.jsx("div",{className:"mt-4",children:e.jsx(d.Item,{name:"document",rules:[{required:!0,message:"PDF is required!"}],validateTrigger:"onSubmit",children:e.jsx(v,{maxFiles:10,allowedTypes:["application/pdf"],maxSizeMB:20,value:s,resetImages:S,onChange:a=>j(a)})})}):e.jsx(_,{name:"title",placeholder:"",label:l||"Folder Name",rules:[{required:!0,message:"Sub Folder Name is required!"},{min:2,message:"Sub Folder Name must be more than 2 characters"},{max:40,message:"Sub Folder Name must not be more than 40 characters"}]}),e.jsx("div",{className:"text-end mt-4",children:e.jsx(q,{title:"Add",className:"add-new-btn",htmlType:"submit",loading:r==="upload-pdf"?x:g})})]})},w=i.memo(y);export{w as A};
