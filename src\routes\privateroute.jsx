import React from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";

// PublicRoute for unauthenticated users
const PublicRoute = () => {
  const user = window.user?.user;

  return user ? <Navigate to="/projects" replace /> : <Outlet />;
};

// PrivateRoute for authenticated users
const PrivateRoute = () => {
  const user = window.user?.user;
  const location = useLocation(); // To track where the user came from

  return user ? (
    <Outlet />
  ) : (
    <Navigate to={`/`} state={{ from: location }} replace />
  );
};

// AuthRoute for role-based access (Company or Employee)
const AuthRoute = ({ allowedRoles }) => {
  const user = window.user?.user;
  const userRole = user?.role;
  const location = useLocation(); // To track where the user came from

  if (!user) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  if (allowedRoles.includes(userRole)) {
    return <Outlet />;
  }

  return <Navigate to={location.state?.from || "/"} replace />;
};

// CompanyRoute for company-specific routes
const CompanyRoute = () => <AuthRoute allowedRoles={["company"]} />;

// EmployeeRoute for employee-specific routes
const EmployeeRoute = () => <AuthRoute allowedRoles={["user"]} />;

export { PublicRoute, PrivateRoute, CompanyRoute, EmployeeRoute, AuthRoute };
