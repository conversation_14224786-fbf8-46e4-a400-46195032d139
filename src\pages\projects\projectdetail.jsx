import React, { useState, useEffect, memo } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import BaseInput from "../../components/shared/inputs";
import CustomTable from "../../components/shared/table/customtable";
import { useParams } from "react-router-dom";
import CustomModal from "../../components/shared/modal";
import FlatButton from "../../components/shared/button/flatbutton";
import PageTitle from "../../components/shared/pagetitle";
import CustomSlider from "../../components/shared/slider";
import ProjectDetailCard from "../../components/shared/card/projectdetailcard";
import BackButton from "../../components/shared/button/backbutton";
import AssignFilterForm from "../../components/partial/modalforms/assignfilterform";
import IconButton from "../../components/shared/button/iconbutton";
import ViewingForm from "../../components/partial/modalforms/viewingform";
import AddFolder from "../../components/partial/modalforms/addfolder";
import { Spin, Empty } from "antd";
import "./projects.css";
import { ColumnsProjectDetail } from "../../components/partial/configdata/tabledata";
import { useFetch } from "../../hooks";
import useSweetAlert from "../../hooks/useSweetAlert";
import { debounce } from "lodash";
import { useViewContext } from "../../store/viewContext";
const MemoizedProjectCard = React.memo(ProjectDetailCard);
const ProjectDetail = () => {
  let { id } = useParams();
  const { viewType } = useViewContext();
  const user = window.user?.user;
  const { showAlert } = useSweetAlert();
  const { loading, data, fetchApi } = useFetch("project", { slug: `/${id}` });
  const {
    loading: loader,
    data: directoriesData,
    fetchApi: getAPi,
    pagination,
    setQueryParams,
  } = useFetch("directories", {
    slug: `/?project_id=${id}&`,
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 10 },
  });
  const { postData: deleteItem } = useFetch("delete_directories", {
    type: "submit",
  });
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });

  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isViewingModalOpen, setIsViewingModalOpen] = useState(false);
  const [isFilterApplied, setIsFilterApplied] = useState(false);

  useEffect(() => {
    fetchApi();
    getAPi();
  }, [id]);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      keyword: search,
    }));
  }, [search]);

  const handleApplyFilter = (filter) => {
    setQueryParams((prev) => ({
      ...prev,
      ...filter,
    }));
    setIsFilterApplied(true);
    setIsFilterModalOpen(false);
  };

  const deleteRow = async (id) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteItem("", cbSuccess, id);
    }
  };

  const handlePageChange = (page, pageSize) => {
    setQueryParams((prev) => ({
      ...prev,
      page,
      limit: pageSize,
      keyword: search,
    }));
  };

  const handleSearchChange = debounce((e) => {
    const value = e.target.value;
    setSearch(value);
    setQueryParams((prev) => ({
      ...prev,
      keyword: value,
      page: 1,
    }));
  }, 300);

  if (loading && loader) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          height: "100vh",
          alignItems: "center",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (window.lodash.isEmpty(data)) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          height: "100vh",
          alignItems: "center",
        }}
      >
        <Empty description="No Data Available" />
      </div>
    );
  }

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      getAPi();
    }
  };

  return (
    <>
      <InnerLayout>
        <PageTitle
          title={<BackButton />}
          buttons={
            <>
              <div>
                <BaseInput
                  name="search"
                  placeholder="Search"
                  value={search}
                  onChange={handleSearchChange}
                  icon={<img src="/admin/assets/img/search-icon.png" />}
                />
              </div>
              {(userObj?.role === "company" ||
                (userObj?._id === data?.user?._id &&
                  userObj?.policies?.some(
                    (policy) => policy.module === "project" && policy.can_create
                  ))) && (
                <div>
                  <FlatButton
                    title="+ Add Folder"
                    className="mx-auto add-new-btn me-3"
                    onClick={() => setIsModalOpen(true)}
                  />
                </div>
              )}

              <div>
                <IconButton
                  title="Viewing"
                  className="mx-auto ms-3 share-btn"
                  icon={<img src="/admin/assets/img/viewing-icon.png" />}
                  iconPosition="right"
                  onClick={() => setIsViewingModalOpen(true)}
                />
              </div>
              <div>
                <IconButton
                  title={
                    <>
                      Filter
                      {isFilterApplied && <span className="filter-dot"></span>}
                    </>
                  }
                  className="mx-auto ms-3 share-btn"
                  icon={<img src="/admin/assets/img/filter-icon.png" />}
                  iconPosition="right"
                  onClick={() => setIsFilterModalOpen(true)}
                />
              </div>
            </>
          }
        />
        <div className="detail-slider-box mt-4">
          <div className="row gx-0 align-items-center">
            <div className="col-12 col-sm-5 col-md-4 col-lg-4">
              <div className="slider-container">
                <CustomSlider imageCount={data?.image_url?.length || 0}>
                  {data?.image_url?.length > 0 &&
                    data.image_url.map((imageUrl, i) => (
                      <div key={i} className="slider-img">
                        <img src={imageUrl} alt={`Slider image ${i + 1}`} />
                      </div>
                    ))}
                </CustomSlider>
              </div>
            </div>
            <div className="col-12 col-sm-7 col-md-8 col-lg-8 p-4">
              <MemoizedProjectCard {...data} />
            </div>
          </div>
        </div>
        <div className="detail-table mt-4 mb-5">
          <CustomTable
            columns={ColumnsProjectDetail(deleteRow, viewType, userObj)}
            data={directoriesData}
            loading={loader}
            rowKey={"_id"}
            pagination={{
              current: pagination?.currentPage,
              total: pagination?.count,
              pageSize: pagination?.perPage,
            }}
            showPagination={true}
            onChange={handlePageChange}
          />
        </div>
      </InnerLayout>
      <CustomModal
        title="Add Folder"
        onCancel={() => setIsModalOpen(false)}
        open={isModalOpen}
        className="custom-modal"
        footer={false}
      >
        <AddFolder
          onCancel={() => setIsModalOpen(false)}
          onSuccess={getAPi}
          projectId={id}
        />
      </CustomModal>
      <CustomModal
        title="Filters"
        onCancel={() => setIsFilterModalOpen(false)}
        open={isFilterModalOpen}
        className="custom-modal"
        footer={false}
      >
        <AssignFilterForm
          projectdetail="true"
          onApplyFilter={handleApplyFilter}
          isFilterRemove={() => setIsFilterApplied(false)}
        />
      </CustomModal>
      <CustomModal
        title="Viewing Option"
        onCancel={() => setIsViewingModalOpen(false)}
        open={isViewingModalOpen}
        className="custom-modal"
        footer={false}
      >
        <ViewingForm onCancel={() => setIsViewingModalOpen(false)} />
      </CustomModal>
    </>
  );
};

export default memo(ProjectDetail);
