import React, { Suspense, lazy } from "react";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
} from "react-router-dom";
import {
  PublicRoute,
  PrivateRoute,
  CompanyRoute,
  EmployeeRoute,
  AuthRoute,
} from "./privateroute";
import Loader from "../components/shared/loader";
const Login = lazy(() => import("../pages/auth/login"));
const Home = lazy(() => import("../pages/landing"));
const Projects = lazy(() => import("../pages/projects"));
const SignUp = lazy(() => import("../pages/auth/signup"));
const ForgetPassword = lazy(() => import("../pages/auth/forgetpassword"));
const ProjectDetail = lazy(() => import("../pages/projects/projectdetail"));
const Directories = lazy(() => import("../pages/directories"));
const DirectoriesDetail = lazy(() => import("../pages/directories/detail"));
const PdfSingle = lazy(() => import("../pages/directories/pdfsingle"));
const AssignTask = lazy(() => import("../pages/assigntask"));
const Notification = lazy(() => import("../pages/notifications"));
const StaticPages = lazy(() => import("../pages/static"));
const AssignTaskDetail = lazy(() => import("../pages/assigntask/detail"));
const UserRole = lazy(() => import("../pages/userrole"));
const Employees = lazy(() => import("../pages/employees"));
const Subscription = lazy(() => import("../pages/subscription"));
const loading = <Loader />;
const App = () => {
  return (
    <Suspense fallback={loading}>
      <Routes>
        {/* Public Routes */}
        <Route element={<PublicRoute />}>
          <Route exact path="/" name="home" element={<Home />} />
          <Route exact path="/sign-up" name="sign-up" element={<SignUp />} />
          <Route exact path="/login/:role" name="login" element={<Login />} />
          <Route
            exact
            path="/forget-password"
            name="forget-password"
            element={<ForgetPassword />}
          />
        </Route>
        {/* Private Routes - Common for all authenticated users */}
        <Route element={<PrivateRoute />}>
          <Route
            exact
            path="/projects"
            name="projects"
            element={<Projects />}
          />
          <Route
            exact
            path="/projects/:id"
            name="project-detail"
            element={<ProjectDetail />}
          />
          <Route
            path="/directories/:project_id/:parent_id"
            element={<Directories />}
          />
          <Route
            exact
            path="/directories-detail/:project_id/:parent_id"
            name="directories-detail"
            element={<DirectoriesDetail />}
          />
          <Route
            exact
            path="/view-pdf/:project_id/:parent_id"
            name="sub-category"
            element={<PdfSingle />}
          />
          <Route
            exact
            path="/assign-task"
            name="assign-task"
            element={<AssignTask />}
          />
          <Route
            exact
            path="/assign-task/:id"
            name="assign-task"
            element={<AssignTaskDetail />}
          />
          <Route
            exact
            path="/notification"
            name="notification"
            element={<Notification />}
          />
        </Route>
        {/* Company-specific Routes */}
        <Route element={<CompanyRoute />}>
          <Route
            exact
            path="/subscription"
            name="subscription"
            element={<Subscription />}
          />
          <Route
            exact
            path="/employees"
            name="employees"
            element={<Employees />}
          />
          <Route
            exact
            path="/user-role"
            name="user-role"
            element={<UserRole />}
          />
        </Route>

        {/* Employee-specific Routes */}
        <Route element={<EmployeeRoute />}>
          <Route
            exact
            path="/employe/:slug"
            name="StaticPages"
            element={<StaticPages />}
          />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default App;
