import { <PERSON><PERSON>, Tooltip } from "antd";
import React, { useState } from "react";
import { DateTime } from "luxon";
import moment from "moment";
import CustomModal from "../modal";
const ProjectDetailCard = ({
  title,
  address,
  members,
  start_at,
  project_status,
  completion_at,
  user,
  type,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <div>
      <div className="row">
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail mb-2">
            <h2 className="color-black font-16">Project Name</h2>
            <p className="color-black font-16">{title}</p>
          </div>
        </div>
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail mb-2">
            <h2 className="color-black font-16">Location</h2>
            <p className="color-dark-blue font-16">{address}</p>
          </div>
        </div>
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail mb-2">
            <h2 className="color-black font-16">Type</h2>
            <p className="color-dark-blue font-16">{type}</p>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail mb-2">
            <h2 className="color-black font-16 mb-1">Assignee Members</h2>
            <div className="d-flex align-items-center">
              {members.slice(0, 4).map((member) => (
                <Tooltip
                  title={
                    member._id === window.user.user._id ? "You" : member.name
                  }
                  key={member._id}
                >
                  <Avatar
                    src={member.image_url}
                    title={
                      member._id === window.user.user._id ? "You" : member.name
                    }
                  >
                    {!member.image && member.name[0]}
                  </Avatar>
                </Tooltip>
              ))}
              {members.length > 4 && (
                <span
                  className="text-blue-500 cursor-pointer ms-2 font-12"
                  onClick={() => setIsModalOpen(true)}
                >
                  View All Members
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail">
            <h2 className="color-black font-16">Date</h2>
            <p className="color-dark-blue font-16">
              {moment(start_at).format("D MMM, YYYY")}
            </p>
          </div>
        </div>
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail">
            <h2 className="color-black font-16">Created by</h2>

            <p className="color-dark-blue font-16">
              {user?.name} {user?._id === window.user.user._id ? "(me)" : ""}
            </p>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail">
            <h2 className="color-black font-16">Status</h2>
            <p
              className={`font-16 ${
                project_status === "in-process"
                  ? "color-yellow "
                  : project_status === "completed"
                  ? "color-dark-blue"
                  : ""
              }`}
            >
              {project_status === "in-process"
                ? "In Process"
                : project_status === "completed"
                ? "Completed"
                : ""}
            </p>
          </div>
        </div>
        {/* <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail">
            <h2 className="color-black font-16">Type</h2>
            <p className="color-dark-blue font-16"></p>
          </div>
        </div> */}
        <div className="col-12 col-md-6 col-lg-4">
          <div className="body-detail">
            <h2 className="color-black font-16">Completion Date</h2>
            <p className="color-dark-blue font-16">
              {moment(completion_at).format("D MMM, YYYY")}
            </p>
          </div>
        </div>
      </div>
      <CustomModal
        title="All Members"
        onCancel={() => setIsModalOpen(false)}
        open={isModalOpen}
        className="custom-modal member-modal"
        footer={false}
      >
        <div className="">
          {members.map((member) => (
            <div key={member._id} className="d-flex align-items-center mb-2">
              <Avatar size={36} src={member.image_url} alt={member.name} />
              <span className="text-lg font-medium ms-2">
                {" "}
                {member.name}{" "}
                {member?._id === window.user.user._id ? "(me)" : ""}
              </span>
            </div>
          ))}
        </div>
      </CustomModal>
    </div>
  );
};

export default ProjectDetailCard;
