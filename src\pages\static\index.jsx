import React from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import StaticContent from "../../components/shared/content";
import { useParams } from "react-router-dom";
import PageTitle from "../../components/shared/pagetitle";

const StaticPages = () => {
  const { slug } = useParams();

  return (
    <InnerLayout>
      <PageTitle
        title={
          slug === "aboutapp"
            ? "About App"
            : slug === "terms-conditions"
            ? "Terms & Conditions"
            : "Privacy Policy"
        }
      />
      <div className="row ">
        <div className="col-12 mt-4">
          <StaticContent />
        </div>
      </div>
    </InnerLayout>
  );
};

export default StaticPages;
