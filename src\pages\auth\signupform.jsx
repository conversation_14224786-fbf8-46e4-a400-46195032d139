import React, { memo, useState } from "react";
import { useFetch } from "../../hooks";
import BaseInput from "../../components/shared/inputs/index";
import FlatButton from "../../components/shared/button/flatbutton";
import { Form } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { create_company } from "../../config/rules";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
const signUpForm = () => {
  const [value, setValue] = useState();
  const navigate = useNavigate();
  const { loading, postData } = useFetch("create_company", { type: "submit" });
  const companySizeOptions = window.helper.generateRanges(50, 1000);
  const handlePhoneChange = (phoneNumber) => {
    // Ensure phoneNumber is defined and has a value
    if (!phoneNumber) {
      setValue(""); // Clear the value if no phone number is provided
      return;
    }

    // Extract only the digits after the country code (+1) and ensure a max length of 10
    const numericPhone = phoneNumber.replace(/\D/g, "").slice(1); // Remove non-digit characters, then slice off the country code (+1)

    if (numericPhone.length === 10) {
      setValue(
        `+1 ${numericPhone.slice(0, 3)} ${numericPhone.slice(
          3,
          6
        )} ${numericPhone.slice(6)}`
      );
    } else if (numericPhone.length < 10) {
      // Handle cases where the phone number has less than 10 digits
      setValue(phoneNumber);
    }
  };
  const onFinish = (values) => {
    const fd = new FormData();
    fd.append("role", "company");
    for (const key in values) {
      fd.append(key, values[key]);
    }
    postData(fd, cbSuccess);
  };
  const cbSuccess = (res) => {
    if (res.statusCode == 200) {
      navigate("/login/company");
    }
  };
  return (
    <Form
      name="login"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <BaseInput
        name="email"
        placeholder="Email Address"
        label="Email Address"
        rules={create_company.email}
      />
      <BaseInput
        name="company_name"
        placeholder="Company Name"
        label="Company Name"
        rules={create_company.company_name}
      />
      <BaseInput
        type="textarea"
        name="company_details"
        placeholder="Company Details"
        label="Company Details"
        rules={create_company.company_details}
      />
      <BaseInput
        type="select"
        name="company_size"
        placeholder="Company Size"
        label="Company Size"
        options={companySizeOptions?.map((item) => ({
          value: item.key,
          label: item.value,
        }))}
        rules={create_company.company_size}
      />
      <div className="phone-input-container">
        <Form.Item
          label="Phone Number"
          name="mobile_no"
          validateTrigger="onBlur"
          rules={[
            {
              required: true,
              message: "Phone Number is required!",
            },
            {
              validator: (_, value) => {
                // Check if the value is not empty and valid (10 digits excluding country code)
                if (value && value.replace(/\D/g, "").slice(1).length !== 10) {
                  return Promise.reject("Enter a valid Phone number");
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <PhoneInput
            id="mobile_no"
            placeholder="Enter phone number"
            value={value}
            onChange={handlePhoneChange}
            className="base-input"
            international={false}
            defaultCountry="US"
          />
        </Form.Item>
      </div>

      <BaseInput
        type="password"
        name="password"
        id="password"
        placeholder="Password"
        label="Password"
        rules={create_company.password}
      />
      <BaseInput
        type="password"
        name="confirmPassword"
        id="confirmPassword"
        placeholder="Confirm Password"
        label="Confirm Password"
        rules={create_company.confirmpassword}
      />

      <div>
        <FlatButton
          title="Sign up"
          className="mx-auto mt-4 signin-btn mt-5"
          htmlType="submit"
          loading={loading}
        />
      </div>
      <div>
        <p className="signup-text">
          Don’t have an account?
          <Link
            to="/login/company"
            className="color-blue font-600 font-16 ms-1"
          >
            Sign in
          </Link>
        </p>
      </div>
    </Form>
  );
};

export default memo(signUpForm);
