import{r,j as s}from"./index-DS_hjASx.js";import{I as n,P as i}from"./index-B0HTmHEd.js";import{B as c,a as m}from"./rules-_ZUHH97D.js";const l=({children:a,title:e,handleSearch:t,handleAddNew:o})=>s.jsxs(n,{children:[s.jsx(i,{title:e,buttons:s.jsxs(s.Fragment,{children:[s.jsx("div",{children:s.jsx(c,{name:"search",placeholder:"Search",icon:s.jsx("img",{src:"/admin/assets/img/search-icon.png"}),onChange:t})}),s.jsx("div",{children:s.jsx(m,{title:"+ Add New",className:"mx-auto add-new-btn",onClick:o})})]})}),a]}),h=r.memo(l);export{h as U};
