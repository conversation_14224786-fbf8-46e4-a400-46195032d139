// hooks/useLoadConversations.js
import { useEffect, useState } from "react";

const useLoadConversations = (socket, taskId) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!socket || !taskId) return;

    setLoading(true);

    socket.emit("load_conversations", { task_id: taskId }, (res) => {
      if (res?.statusCode === 200) {
        setMessages(res.data);
      } else {
        setMessages([]);
      }
      setLoading(false);
    });

    return () => {
      setMessages([]);
      setLoading(true);
    };
  }, [socket, taskId]);

  return { messages, loading };
};

export default useLoadConversations;
