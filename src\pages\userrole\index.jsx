import React, { memo, useState, useCallback, useEffect } from "react";
import { useFetch } from "../../hooks";
import { Link } from "react-router-dom";
import CustomDropdown from "../../components/shared/dropdown";
import useSweetAlert from "../../hooks/useSweetAlert";
import { debounce } from "lodash";
import UserRoleLayout from "../../components/shared/userlayout";
import UserRoleModal from "../../components/shared/userrolemodal";
import CustomTable from "../../components/shared/table/customtable";

const UserRole = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [searchValue, setSearchValue] = useState(""); // Search state

  const { loading, data, fetchApi, pagination, setQueryParams } = useFetch(
    "user_type",
    {
      enablePagination: true,
    }
  );

  const { postData: deleteUserRole } = useFetch("delete_user_type", {
    type: "submit",
  });
  const { showAlert } = useSweetAlert();

  // Handle server-side search & query updates
  const debouncedSearch = useCallback(
    debounce((value) => {
      // Update query parameters for search & trigger server-side re-fetch
      setQueryParams({ page: 1, limit: 10, keyword: value });
    }, 500),
    []
  );

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handlePageChange = (page, pageSize) => {
    // Update query parameters with pagination only
    setQueryParams({ page, limit: pageSize, keyword: searchValue });
  };

  const ColumnsUserRole = [
    {
      title: "User Type",
      dataIndex: "title",
      render: (text) => <Link to="#">{text}</Link>,
      sorter: (a, b) => a.title.length - b.title.length,
      width: "90%",
    },
    {
      title: "Action",
      dataIndex: "action",
      width: "",
      align: "",
      render: (text, data) => (
        <CustomDropdown
          title="Action"
          icon="true"
          className="table-dropdown"
          items={[
            {
              label: (
                <p className="color-blue" onClick={() => handleEdit(data)}>
                  Edit
                </p>
              ),
              key: "0",
            },
            {
              label: (
                <p
                  className="color-red"
                  onClick={(e) => handleDelete(data._id, e)}
                >
                  Delete
                </p>
              ),
              key: "1",
            },
          ]}
        />
      ),
    },
  ];

  const handleEdit = (data) => {
    setEditData(data);
    setIsModalOpen(true);
  };

  const handleDelete = async (id, e) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteUserRole("", cbSuccess, id);
    }
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) fetchApi();
  };

  return (
    <UserRoleLayout
      handleSearch={handleSearch}
      handleAddNew={() => {
        setEditData(null);
        setIsModalOpen(true);
      }}
      title="User Types"
    >
      <div className="detail-table mt-4 mb-4">
        <CustomTable
          columns={ColumnsUserRole}
          data={data} // Server paginated data, not locally filtered
          loading={loading}
          pagination={{
            current: pagination?.currentPage,
            total: pagination?.count,
            pageSize: pagination?.perPage,
          }}
          onChange={handlePageChange}
          showPagination={true}
          rowKey={"_id"}
          scroll={{ x: 1000 }}
        />
      </div>
      <UserRoleModal
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        editData={editData}
        refreshDataTable={fetchApi}
      />
    </UserRoleLayout>
  );
};

export default memo(UserRole);
