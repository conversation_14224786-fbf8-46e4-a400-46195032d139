import React, { memo, useState, useEffect } from "react";
import { Form, Skeleton } from "antd";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { create_company } from "../../../config/rules";
import PhoneInput from "react-phone-number-input";
import CustomUpload from "../../shared/upload";

const EditProfileForm = ({ onCancel }) => {
  let user = window.user.user;
  const [uploadedImages, setUploadedImages] = useState([]);
  const [resetImages, setResetImages] = useState(false);
  const { loading: loader, data } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });
  const companySizeOptions = window.helper.generateRanges(50, 1000);
  const { loading, postData } = useFetch("update_profile", { type: "submit" });
  const [form] = Form.useForm();

  useEffect(() => {
    if (data?.image_url && data?.image_url.length > 0) {
      setUploadedImages([data?.image_url]); // Ensure uploadedImages is an array
    } else {
      setUploadedImages([]); // Reset to empty array if no image
    }
  }, [data]);

  const onFinish = (values) => {
    const fd = new FormData();
    setResetImages(true);
    if (uploadedImages?.length > 0) {
      const file = uploadedImages[0];
      fd.append("image", file);
    }
    // Append other form values to FormData
    for (const key in values) {
      fd.append(key, values[key]);
    }

    postData(fd, cbSuccess, user._id);
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();

      window.user.user = res.data;
      form.resetFields();
      setTimeout(() => setResetImages(false), 0);
      window.location.reload();
    }
  };

  return loader || !data ? (
    <Skeleton active paragraph={{ rows: 10 }} />
  ) : (
    <Form
      name="edit"
      layout="vertical"
      onFinish={onFinish}
      form={form}
      initialValues={{
        name: user?.role === "user" ? data && data?.name : undefined,
        company_name: user.role === "company" ? data?.company_name : undefined,
        company_size: user.role === "company" ? data?.company_size : undefined,
        mobile_no: data?.mobile_no || "",
        company_details:
          user.role === "company" ? data?.company_details : undefined,
        remember: true,
      }}
      autoComplete="off"
    >
      <CustomUpload
        maxFiles={1}
        value={uploadedImages}
        resetImages={resetImages}
        onChange={(images) => setUploadedImages(images)}
        allowedTypes={["image/jpeg", "image/jpg", "image/png"]}
      />
      {user.role === "user" ? (
        <BaseInput
          name="name"
          placeholder="Enter Name"
          label="Name"
          rules={create_company.name}
        />
      ) : (
        <>
          <BaseInput
            name="company_name"
            placeholder="Enter company name"
            label="Company Name"
            rules={create_company.company_name}
          />
          <BaseInput
            type="select"
            name="company_size"
            placeholder="Company Size"
            label="Company Size"
            options={companySizeOptions?.map((item) => ({
              value: item.key,
              label: item.value,
            }))}
            rules={create_company.company_size}
          />
          <div className="phone-input-container">
            <Form.Item
              label="Phone Number"
              name="mobile_no"
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: "Phone Number is required!",
                },
                {
                  validator: (_, value) =>
                    value && value.replace(/\D/g, "").slice(1).length !== 10
                      ? Promise.reject("Phone number must be 10 digits")
                      : Promise.resolve(),
                },
              ]}
            >
              <PhoneInput
                id="mobile_no"
                placeholder="Enter phone number"
                className="base-input"
                international={false}
                defaultCountry="US"
                disabled
              />
            </Form.Item>
          </div>
          <BaseInput
            type="textarea"
            name="company_details"
            placeholder="Enter company details"
            label="Company Details"
            rules={create_company.company_details}
          />
        </>
      )}

      <div className="text-end mt-4">
        <FlatButton
          title="Update"
          className="add-new-btn"
          htmlType="submit"
          loading={loading}
        />
      </div>
    </Form>
  );
};

export default memo(EditProfileForm);
