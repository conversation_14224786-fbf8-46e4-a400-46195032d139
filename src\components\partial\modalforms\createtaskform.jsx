import React, { memo, useState, useEffect } from "react";
import BaseInput from "../../shared/inputs";
import { Form, Skeleton, Empty } from "antd";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { create_task } from "../../../config/rules";
import dayjs from "dayjs";
import PdfToImage from "../../shared/pdftoimage";
import PdfFirstPageThumbnail from "../../shared/pdftoimage";
const CreateTaskForm = ({ onCancel, fetchTask }) => {
  const currentUserId = window.user?.user?._id;
  const [form] = Form.useForm();
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [selectedSubFolder, setSelectedSubFolder] = useState(null);
  const [checkedItems, setCheckedItems] = useState([]);
  const [projectAssignees, setProjectAssignees] = useState([]);
  const { postData, loading } = useFetch("create_task", { type: "submit" });
  const { loading: projectLoading, data: projectData } = useFetch("project", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 1000 },
  });

  const baseDirectoriesParams = { type: "delay" };
  const {
    loading: folderLoading,
    data: folderData,
    fetchApi: fetchFolders,
  } = useFetch("directories", baseDirectoriesParams);
  const {
    loading: subFolderLoading,
    data: subFolderData,
    fetchApi: fetchSubFolders,
  } = useFetch("directories", baseDirectoriesParams);
  const {
    loading: pdfLoading,
    data: pdfData,
    fetchApi: fetchPdf,
  } = useFetch("directories", baseDirectoriesParams);

  const resetFormAndFetch = (field, value, additionalFields = {}) => {
    const resetFields = { [field]: value, ...additionalFields };
    form.setFieldsValue(resetFields);
  };

  useEffect(() => {
    if (selectedProject) {
      fetchFolders(`/?project_id=${selectedProject}&page=1&limit=1000`);
      setSelectedFolder(null);
      resetFormAndFetch("root_directory", null, { sub_directory: null });
      fetchPdf("");
    }
  }, [selectedProject]);

  useEffect(() => {
    if (selectedProject && selectedFolder) {
      fetchSubFolders(
        `/?project_id=${selectedProject}&parent_id=${selectedFolder}&page=1&limit=1000`
      );
      resetFormAndFetch("sub_directory", null);
    }
  }, [selectedFolder, selectedProject]);

  useEffect(() => {
    if (selectedProject && selectedFolder && selectedSubFolder) {
      fetchPdf(
        `/?project_id=${selectedProject}&parent_id=${selectedSubFolder}&page=1&limit=1000`
      );
    }
  }, [selectedFolder, selectedProject, selectedSubFolder]);

  const handleProjectChange = (val) => {
    setSelectedProject(val);
    setSelectedFolder(null);
    resetFormAndFetch("root_directory", null, { sub_directory: null });
    // Reset assignees when project changes
    form.setFieldsValue({ assignees: [] });

    // Find selected project and set its assignees
    const selectedProject = projectData?.find((p) => p._id === val);
    if (selectedProject?.members) {
      setProjectAssignees(
        selectedProject.members
          .filter((member) => member._id !== currentUserId) // Filter out current user
          .map((member) => ({
            value: member._id,
            label: member.name,
          }))
      );
    } else {
      setProjectAssignees([]);
    }
  };

  const handleFolderChange = (val) => {
    setSelectedFolder(val);
    resetFormAndFetch("sub_directory", null);
  };

  const handleCheckboxChange = (id) => (e) => {
    setCheckedItems((prev) => {
      if (e.target.checked) {
        return [...prev, id];
      } else {
        return prev.filter((itemId) => itemId !== id);
      }
    });
  };

  const onFinish = (values) => {
    const fd = new FormData();
    const dateFields = ["start_at", "end_at"];
    const arrayFields = ["assignees", "directories"];

    // Handle dates
    dateFields.forEach((field) => {
      if (values[field]) {
        fd.append(field, dayjs(values[field]).format("YYYY-MM-DD"));
      }
    });

    // Handle assignees
    if (values.assignees?.length) {
      values.assignees.forEach((id) => fd.append("assignees[]", id));
    }

    // Handle directories from selected PDF
    if (checkedItems?.length) {
      checkedItems.forEach((id) => {
        const selectedPdf = pdfData?.find((item) => item._id === id);
        if (selectedPdf?.parent_id) {
          fd.append("directories[]", selectedPdf._id);
        }
      });
    }

    // Handle remaining fields, excluding those already handled
    Object.entries(values).forEach(([key, value]) => {
      if (!dateFields.includes(key) && !arrayFields.includes(key)) {
        fd.append(key, value);
      }
    });

    postData(fd, cbSuccess);
  };
  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      form.resetFields();
      setSelectedProject(null);
      setSelectedFolder(null);
      fetchTask();
      fetchPdf("");
      setCheckedItems([]);
    }
  };
  const renderPdfSection = () => {
    if (!selectedSubFolder) return null;

    return (
      <div className="col-12">
        <label className="color-black font-600 mt-3 mb-3">
          Select Drawing PDF
        </label>
        {pdfLoading ? (
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {[...Array(5)].map((_, index) => (
              <div key={index} style={{ margin: "10px" }}>
                <Skeleton.Image active style={{ width: 100, height: 100 }} />
              </div>
            ))}
          </div>
        ) : pdfData?.length ? (
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {pdfData?.map((item) => (
              <PdfFirstPageThumbnail
                key={item._id}
                pdfUrl={item.file}
                checked={checkedItems.includes(item._id)}
                onChange={handleCheckboxChange(item._id)}
              />
            ))}
          </div>
        ) : (
          <Empty description="No drawings found" />
        )}
      </div>
    );
  };

  return (
    <Form
      name="create-task"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{ remember: true }}
      form={form}
      autoComplete="off"
    >
      <div className="row">
        {/* Basic Info */}
        <div className="col-12 col-md-6">
          <BaseInput name="title" label="Title" rules={create_task.title} />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="project_id"
            type="select"
            label="Project"
            options={projectData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            onChange={handleProjectChange}
            loading={projectLoading}
            rules={create_task.project_id}
            showSearch={true}
          />
        </div>

        {/* Folder Selection */}
        <div className="col-12 col-md-6">
          <BaseInput
            name="root_directory"
            type="select"
            label="Select Folder"
            options={folderData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            rules={create_task.root_directory}
            loading={folderLoading}
            onChange={handleFolderChange}
            disabled={!selectedProject}
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="sub_directory"
            type="select"
            label="Select Sub Folder"
            options={subFolderData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            rules={create_task.sub_directory}
            loading={subFolderLoading}
            disabled={!selectedFolder}
            onChange={(val) => setSelectedSubFolder(val)}
          />
        </div>

        {/* Assignee */}
        <div className="col-12 col-md-6">
          <BaseInput
            name="assignees"
            type="select"
            label="Assignee"
            options={projectAssignees}
            mode="multiple"
            loading={projectLoading}
            rules={create_task.assignees}
            disabled={!selectedProject}
          />
        </div>

        {/* Dates */}
        <div className="col-12 col-md-6">
          <div className="row">
            <div className="col-12 col-md-6">
              <BaseInput
                name="start_at"
                type="datepiker"
                label="Start Date"
                rules={create_task.start_at}
                disablePastDates={true}
                format="YYYY-MM-DD"
              />
            </div>
            <div className="col-12 col-md-6">
              <BaseInput
                name="end_at"
                type="datepiker"
                label="End Date"
                rules={create_task.end_at}
                disablePastDates={true}
                format="YYYY-MM-DD"
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="col-12">
          <BaseInput
            name="description"
            type="textarea"
            label="Description"
            rows="5"
            rules={create_task.description}
          />
        </div>

        {/* PDF Section */}
        {renderPdfSection()}
      </div>
      <div className="text-end mt-4">
        <FlatButton
          title="Save"
          className="add-new-btn"
          htmlType="submit"
          loading={loading}
        />
      </div>
    </Form>
  );
};

export default memo(CreateTaskForm);
