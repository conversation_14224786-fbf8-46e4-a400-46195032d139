import React, { memo } from "react";
import AuthLayout from "../../components/shared/layout/authlayout";
import BaseInput from "../../components/shared/inputs/index";
import FlatButton from "../../components/shared/button/flatbutton";
import { Form } from "antd";
import { Link, useNavigate } from "react-router-dom";
import "./auth.css";
import { useFetch } from "../../hooks";
import { create_company } from "../../config/rules";

const ForgetPassword = () => {
  const navigate = useNavigate();
  const { loading, postData } = useFetch("forgot_password", { type: "submit" });
  const onFinish = (values) => {
    const fd = new FormData();
    for (const key in values) {
      fd.append(key, values[key]);
    }
    postData(fd, cbSuccess);
  };
  const cbSuccess = (res) => {
    if (res.statusCode == 200) {
      navigate("/");
    }
  };

  return (
    <AuthLayout
      title="Forgot Password"
      detail="Please enter your email address associated with your account"
    >
      <Form
        name="login"
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          remember: true,
        }}
        autoComplete="off"
      >
        <BaseInput
          name="email"
          placeholder="Email"
          label="Email"
          rules={create_company.email}
        />
        <div>
          <FlatButton
            title="Send"
            className="mx-auto mt-4 signin-btn mt-5"
            htmlType="submit"
          />
        </div>
        <div>
          <p className="signup-text">
            Don’t have an account?
            <Link to="/" className="color-blue font-600 font-16 ms-1">
              Sign in
            </Link>
          </p>
        </div>
      </Form>
    </AuthLayout>
  );
};

export default memo(ForgetPassword);
