import React, { memo } from "react";
import CustomTable from "../table/customtable";
const UserRoleTable = memo((props) => (
  <CustomTable
    {...props} // Spread all props into the CustomTable component
    rowKey={"_id"}
    data={Array.isArray(props.data) ? props.data : []} // Ensure data is always an array
    pagination={{
      current: props?.pagination?.currentPage,
      total: props?.pagination?.count,
      pageSize: props?.pagination?.perPage,
    }}
    scroll={{ x: 1000 }}
    onChange={props.handlePageChange}
    showPagination={true}
  />
));

export default UserRoleTable;
