@font-face {
    font-family: "EuclidSquare-Bold";
    src: url("/assets/font/EuclidSquare/EuclidSquare-Bold.ttf");
}
@font-face {
    font-family: "EuclidSquare-Regular";
    src: url("/assets/font/EuclidSquare/EuclidSquare-Regular.ttf");
}
@font-face {
    font-family: "EuclidSquare-Medium";
    src: url("/assets/font/EuclidSquare/EuclidSquare-Medium.ttf");
}



/* @import url('https://fonts.cdnfonts.com/css/gilroy-bold'); */

body {
    margin: 0;
    color: #000126;
    font-size: 13px;
    position: relative;
    font-family: "EuclidSquare-Regular" !important;
    overflow-x: hidden;
    background-color: #f7f7fa;
}




h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
    margin: 0;
    padding: 0;
}



ol,
ul {
    padding: 0 !important;
    list-style: none !important;
}

p,
ul,
ol,
div,
footer,
header,
main {
    font-family: "EuclidSquare-Regular" !important;
}

/* total width */
::-webkit-scrollbar {
    background-color: #fff;
    width: 16px;
}

/* background of the scrollbar except button or resizer */
::-webkit-scrollbar-track {
    background-color: #fff;
}

::-webkit-scrollbar-track:hover {
    background-color: #f4f4f4;
}

/* scrollbar itself */
::-webkit-scrollbar-thumb {
    background-color: #babac0;
    border-radius: 16px;
    border: 5px solid #fff;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a0a0a5;
    border: 4px solid #f4f4f4;
}

/* set button(top and bottom of the scrollbar) */
::-webkit-scrollbar-button {
    display: none;
}



a {
    padding: 0;
    margin: 0;
    transition: all 0.5s ease;
    text-decoration: none !important;
    color: #000126 !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700 !important;
}

address,
dl,
ol,
p,
ul {
    margin: 0 !important;
}

a:hover,
a:focus,
a:active {
    outline: none;
    box-shadow: none;
    text-decoration: none;
}

button {
    transition: all 0.5s ease;
}

.btn-theme {
    border-radius: 12px;
    background-color: #1182f1;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;

}

.btn-theme2 {
    border-radius: 12px;
    background-color: #08ae22;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
}

.btn-theme3 {
    border-radius: 12px;
    background-color: #ae0808;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
}

.btn-theme::after,
.btn-theme2::after,
.btn-theme3::after {
    background-color: #111114;
    height: 100%;
    left: -35%;
    top: 0;
    transform: skew(40deg);
    transition-duration: 0.6s;
    transform-origin: top left;
    width: 0;
    position: absolute;
    content: "";
    z-index: -1;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.btn-theme:hover::after,
.btn-theme2:hover::after,
.btn-theme3:hover::after {
    height: 100%;
    width: 135%;
}

.btn-theme:hover,
.btn-theme2:hover,
.btn-theme3:hover {
    color: #fff !important;
}

.auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff !important;
}
.edit-btn{
    border-radius: 10px;
    border: solid 1px rgba(255, 255, 255, 0.55);
    background-color: rgba(255, 255, 255, 0.8);
  padding: 10px 20px;
  color: #000;
}
.container {
    width: 100% !important;
    max-width: 100%;
    padding: 0 100px;
    margin: 0 auto;
}

/* Font  */



/* Banner Section  */

/* Feature Section */


/* Work Sec */



/* Why Chosse Us sec */



/* Footer */


/* Auth  */
* {
    padding: 0;
    margin: 0;
}
.auth {
    display: flex;
    min-height: 100vh;
    align-items: stretch;
    flex-direction: row; /* Default for larger screens */
  }
  
  .auth-sidebar {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa; /* Optional background color */
  }
  
  .auth-sidebar img {
    height: 100%;
    width: auto;
    object-fit: cover; /* Ensures the image covers the area */
  }

.add-new-btn{
    background-color: #28569f;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
}
.reset-btn{
    background-color: #aab6cc;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
}



.signin-btn {
    width: 100%;
    height: 45px;
    border: 0;
    background-color: #28569f;
    border-radius: 12px;
    color: #fff;
    font-size: 16px;
    margin-top: 80px;
}
:where(.css-dev-only-do-not-override-1c0na6j).ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover{
    background-color: #1182f1;
    border-radius: 12px; 
    color: #fff;
}
/* .col-12 {
    padding: 0 !important;
} */

.auth-box {
    width: 85%;
    margin: 0 auto;
    position: relative;
   margin-top: 100px;
   min-height: 80vh;

}




.auth-logo {
    padding-left: 44px;
    margin-top: 55px;
}

.auth-box h1 {
    color: #1b1b1f;
}

.auth-box p {
    color: #717171;
}


.sigup-text {
    text-align: center;
    position: absolute;
    bottom: 20px;
    right: 12%;
}

.auth .container {
    padding: 0;
}

.ant-form-item-label >label{
    color:#1b1b1f   !important;
}

.ant-form-item {
    margin-bottom: 0;
    margin-top: 22px;
}
.signup-text {
    text-align: center;
    margin-top: 90px !important;
    color: #000126 !important;
    margin-bottom: 15px !important;
 }
 .ant-modal-body label {
    color: #000 !important;
    font-weight: 600 !important;
}

* {
    box-sizing: border-box;
}

/***********************
 ** Start Sidebar Menu **
 ***********************/




