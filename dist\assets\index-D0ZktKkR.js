import{r,a as I,c as F,l as k,j as e,S as E}from"./index-DS_hjASx.js";import{I as D,P as N,a as T}from"./index-B0HTmHEd.js";import{u as p,B as M,a as $,e as z}from"./rules-_ZUHH97D.js";import{C as L}from"./customtable-GeATQPOH.js";import{B as Q}from"./backbutton-C1zuWfk9.js";import{A as R}from"./addfolder-BLm3JZSn.js";import{a as K}from"./tabledata-s2KXI6uj.js";import"./index-B37y91Rn.js";import"./notification-7LrlFXRF.js";import"./index-diLtVJ_H.js";import"./Pagination-DGfRHkE5.js";const Y=()=>{var f,x;const g=(f=window.user)==null?void 0:f.user,{data:a}=p("get_profile",{type:"mount",slug:`/${g._id}`,enablePagination:!1}),{showAlert:j}=z();let{project_id:c,parent_id:w}=I();const y=F(),[i,C]=r.useState(""),{record:o}=y.state||{},{loading:h,data:S,fetchApi:l,pagination:s,setQueryParams:d}=p("directories",{slug:`/?project_id=${c}&parent_id=${w}&`,enablePagination:!0,defaultQueryParams:{page:1,limit:10}}),{postData:b}=p("delete_directories",{type:"submit"}),[P,u]=r.useState(!1),_=(t,n)=>{d(m=>({...m,page:t,limit:n,keyword:i}))};r.useEffect(()=>{l()},[c]);const B=k.debounce(t=>{const n=t.target.value;C(n),d(m=>({...m,keyword:n,page:1}))},300);r.useEffect(()=>{d(t=>({...t,keyword:i}))},[i]);const v=async t=>{(await j({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&b("",A,t)},A=t=>{t.statusCode===200&&l()};return h&&window.lodash.isEmpty(o)?e.jsx("div",{style:{display:"flex",justifyContent:"center",height:"100vh",alignItems:"center"},children:e.jsx(E,{size:"large"})}):e.jsxs(e.Fragment,{children:[e.jsxs(D,{children:[e.jsx(N,{title:e.jsx(Q,{title:o==null?void 0:o.title}),buttons:e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx(M,{name:"search",placeholder:"Search",icon:e.jsx("img",{src:"/admin/assets/img/search-icon.png"}),value:i,onChange:B})}),((a==null?void 0:a.role)==="company"||((x=a==null?void 0:a.policies)==null?void 0:x.some(t=>t.module==="project"&&t.can_create)))&&e.jsx("div",{children:e.jsx($,{title:"+ Add Sub Folder",className:"mx-auto add-new-btn me-3",onClick:()=>u(!0)})})]})}),e.jsx("div",{className:"detail-table mt-4 mb-5",children:e.jsx(L,{columns:K(v,a),data:S,loading:h,rowKey:"_id",pagination:{current:s==null?void 0:s.currentPage,total:s==null?void 0:s.count,pageSize:s==null?void 0:s.perPage},showPagination:!0,onChange:_})})]}),e.jsx(T,{title:"Add Sub Folder",open:P,onCancel:()=>u(!1),className:"custom-modal",footer:!1,children:e.jsx(R,{onCancel:()=>u(!1),onSuccess:l,projectId:c,title:"Sub Folder Name",parentId:o==null?void 0:o._id})})]})},te=r.memo(Y);export{te as default};
