import React, { useMemo } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import NotificationCard from "../../components/shared/card/notificationcard";
import PageTitle from "../../components/shared/pagetitle";
import { Skeleton, Empty, Pagination } from "antd";
import moment from "moment";
import "./notification.css";
import { useFetch } from "../../hooks";

const Notification = () => {
  const {
    data: notifications,
    fetchApi,
    pagination,
    loading,
    setQueryParams,
  } = useFetch("notification", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 10 },
  });

  // Sort notifications into recent and previous
  const handlePageChange = (page, pageSize) => {
    setQueryParams({ page, limit: pageSize });
  };

  const handleNotificationStatusChange = (id, isChecked) => {
    // Update local state to reflect changes
    const updatedNotifications = notifications?.map((notification) =>
      notification._id === id
        ? { ...notification, is_redirected: isChecked }
        : notification
    );

    // Refresh the notifications list
    fetchApi();
  };
  const sortedNotifications = useMemo(() => {
    if (!notifications) return { today: [], yesterday: [], older: [] };

    return notifications.reduce(
      (acc, notification) => {
        const notificationDate = moment(notification.created_at);
        const today = moment().startOf("day");
        const yesterday = moment().subtract(1, "day").startOf("day");

        const formattedNotification = {
          id: notification._id,
          title: notification.title,
          description: notification.body,
          time: moment(notification.created_at).format("hh:mm A"),
          date: notificationDate.format("MMM DD, YYYY"),
          module: notification.module,
          reference_id: notification.reference_id,
          actor: notification.actor,
          target: notification.target,
          custom_data: notification.custom_data,
          is_redirected: notification.is_redirected,
        };

        if (notificationDate.isSame(today, "day")) {
          acc.today.push(formattedNotification);
        } else if (notificationDate.isSame(yesterday, "day")) {
          acc.yesterday.push(formattedNotification);
        } else {
          acc.older.push(formattedNotification);
        }

        return acc;
      },
      { today: [], yesterday: [], older: [] }
    );
  }, [notifications]);
  console.log("sortedNotifications", sortedNotifications);
  if (loading) {
    return (
      <InnerLayout>
        <PageTitle title="Notifications" />
        <div className="row">
          <div className="col-12 col-md-10 col-lg-8 col-xl-7 col-xxl-6 mb-4">
            <div className="mt-4">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="mb-4">
                  <Skeleton
                    active
                    avatar
                    paragraph={{ rows: 2 }}
                    className="notification-skeleton"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </InnerLayout>
    );
  }

  return (
    <InnerLayout>
      <PageTitle title="Notifications" />
      <div className="row">
        <div className="col-12 col-md-10 col-lg-8 col-xl-7 col-xxl-6 mb-4">
          {sortedNotifications.today.length > 0 && (
            <>
              <p className="noti-title mt-4">Today</p>
              {sortedNotifications.today.map((notification) => (
                <NotificationCard
                  key={notification.id}
                  {...notification}
                  onStatusChange={handleNotificationStatusChange}
                />
              ))}
            </>
          )}

          {sortedNotifications.yesterday.length > 0 && (
            <>
              <p className="noti-title mt-4">Yesterday</p>
              {sortedNotifications.yesterday.map((notification) => (
                <NotificationCard
                  key={notification.id}
                  {...notification}
                  onStatusChange={handleNotificationStatusChange}
                />
              ))}
            </>
          )}
          {sortedNotifications.older.length > 0 && (
            <>
              <p className="noti-title mt-4">Previous</p>
              {sortedNotifications.older.map((notification) => (
                <NotificationCard
                  key={notification.id}
                  {...notification}
                  onStatusChange={handleNotificationStatusChange}
                  showDate={true}
                />
              ))}
            </>
          )}
          {notifications?.length > 0 && pagination?.count > 10 && (
            <div className="custom-pagination d-flex justify-content-end mt-4">
              <Pagination
                current={pagination.currentPage || 1}
                total={pagination.count || 0}
                pageSize={pagination.perPage || 10}
                onChange={handlePageChange}
              />
            </div>
          )}
        </div>
        {!notifications?.length && (
          <div className="text-center mt-4">
            <Empty
              description="No notifications found"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </div>
    </InnerLayout>
  );
};

export default Notification;
