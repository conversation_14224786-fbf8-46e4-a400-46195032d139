import React, { memo } from "react";
import { Checkbox, message } from "antd";
import { useNavigate } from "react-router-dom";
import { useFetch } from "../../../hooks";

const NotificationCard = ({
  id,
  title,
  time,
  is_redirected,
  onStatusChange,
  module,
  reference_id,
  custom_data,
  description,
  date,
  showDate = false,
}) => {
  const navigate = useNavigate();
  const { postData } = useFetch("update_notification", {
    type: "submit",
    skipNotification: true,
  });

  // Combined handler for both status update and navigation
  const handleCardClick = async () => {
    let redirectUrl;
    if (custom_data?.identifer === "delete") {
      if (!is_redirected) {
        const fd = new FormData();
        fd.append("is_redirected", true);

        await postData(
          fd,
          (res) => {
            if (res.statusCode === 200) {
              onStatusChange && onStatusChange(id, true);
            }
          },
          id
        );
      }
      message.error("This content is no longer available.");
      return; // Exit early to prevent redirection
    }
    // Determine redirect URL
    switch (module) {
      case "projects":
        redirectUrl = `/projects/${reference_id}`;
        break;
      case "main_directories":
        redirectUrl = `/projects/${custom_data.project_id}`;
        break;
      case "sub_directories":
        redirectUrl = `/directories/${custom_data.project_id}/${custom_data.parent_id}`;
        break;
      case "directories_detail":
        redirectUrl = `/directories-detail/${custom_data.project_id}/${custom_data.parent_id}`;
        break;
      case "tasks":
        redirectUrl = `/assign-task/${custom_data.reference_id}`;
        break;
      default:
        redirectUrl = null;
    }

    // Update read status if not already read
    if (!is_redirected) {
      const fd = new FormData();
      fd.append("is_redirected", true);

      await postData(
        fd,
        (res) => {
          if (res.statusCode === 200) {
            onStatusChange && onStatusChange(id, true);
          }
        },
        id
      );
    }

    // Navigate after status update
    if (redirectUrl) {
      navigate(redirectUrl);
    }
  };

  // Separate handler for checkbox to prevent event bubbling
  const handleCheckboxClick = (e) => {
    e.stopPropagation();
    if (!is_redirected) {
      handleCardClick();
    }
  };

  return (
    <div
      className="notification-card d-flex align-items-start justify-content-between p-3"
      onClick={handleCardClick}
      style={{ cursor: "pointer" }}
    >
      <div className="d-flex flex-column">
        <div className="notification-content">
          <span
            className="notification-dot-wrapper"
            style={{ width: "8px", marginRight: "8px" }}
          >
            {!is_redirected && <span className="notification-dot" />}
          </span>
          <span className="notification-text">{title}</span>
        </div>
        {description && (
          <p className="mt-2 mb-0 text-muted" style={{ paddingLeft: "24px" }}>
            {description}
          </p>
        )}
      </div>
      <div>
        <p className="font-16 color-light">
          {showDate ? `${date} at ${time}` : time}
        </p>
      </div>
    </div>
  );
};

export default memo(NotificationCard);
