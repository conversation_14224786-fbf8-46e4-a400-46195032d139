import React from "react";
import Input<PERSON>ield from "./input";
import Pass<PERSON><PERSON>ield from "./passwordfield";
import SelectInput from "./select";
import CheckBoxInput from "./checkbox";
import DatePickerInput from "./datepicker";
import TimePikerInput from "./timepiker";
import TextAreaInput from "./textarea";

const BaseInput = (props) => {
  if (props.type == "select") return <SelectInput {...props} />;
  else if (props.type == "password") return <PasswordField {...props} />;
  else if (props.type == "checkbox") return <CheckBoxInput {...props} />;
  else if (props.type == "datepiker") return <DatePickerInput {...props} />;
  else if (props.type == "timepiker") return <TimePikerInput {...props} />;
  else if (props.type == "textarea") return <TextAreaInput {...props} />;
  else return <InputField {...props} />;
};

export default BaseInput;
