import React, { memo } from "react";
import ChatMessage from "./chatmessage";
import ChatHeader from "./chatheader";
import ChatFooter from "./chatfooter";
import useJoinRoom from "../../../hooks/useJoinRoom";

const ChatBox = ({ data, fetchApi }) => {
  return (
    <div className="chat-box">
      <ChatHeader data={data} fetchApi={fetchApi} />
      <div className="chat-body">
        <ChatMessage data={data} />
      </div>
      <ChatFooter taskId={data?._id} />
    </div>
  );
};

export default memo(ChatBox);
