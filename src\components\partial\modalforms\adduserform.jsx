import React, { useEffect, memo, useState } from "react";
import { Form } from "antd";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { create_company, create_employee } from "../../../config/rules";
import { Switch } from "antd";
const AddUserForm = ({ onCancel, refreshDataTable, editData }) => {
  const [createProjects, setCreateProjects] = useState(false);
  const [createTask, setCreateTask] = useState(false);
  const { data: getDefaultPassword, fetchApi } = useFetch("get_password", {
    defaultQueryParams: { page: 1, limit: 1000 },
  });
  const [form] = Form.useForm();
  const { loading, data } = useFetch("user_type", {
    defaultQueryParams: { page: 1, limit: 1000 },
  });
  const { loading: createLoading, postData } = useFetch("create_employee", {
    type: "submit",
  });
  const { loading: editLoading, postData: updateData } = useFetch(
    "edit_employee",
    { type: "submit" }
  );

  const onFinish = (values) => {
    const fd = new FormData();
    Object.entries(values).forEach(([key, value]) => fd.append(key, value));
    const existingPolicies = editData?.policies || [];
    const updatedPolicies = [
      {
        module: "project",
        can_create: createProjects,
        can_read: createProjects,
        can_update: createProjects,
        can_delete: createProjects,
      },
      {
        module: "task",
        can_create: createTask,
        can_read: createTask,
        can_update: createTask,
        can_delete: createTask,
      },
    ];

    const mergedPolicies = [...existingPolicies, ...updatedPolicies].reduce(
      (acc, policy) => {
        const existing = acc.find((p) => p.module === policy.module);
        if (existing) {
          Object.assign(existing, policy);
        } else {
          acc.push(policy);
        }
        return acc;
      },
      []
    );

    mergedPolicies.forEach((policy) => {
      fd.append("policies[]", JSON.stringify(policy));
    });

    editData
      ? updateData(fd, cbSuccess, editData._id)
      : postData(fd, cbSuccess);
  };

  useEffect(() => {
    if (editData) {
      form.setFieldsValue({
        name: editData.name,
        email: editData.email,
        user_type_id: editData.user_type?._id,
        password: getDefaultPassword?.default_password || "",
      });
    } else {
      form.resetFields();
      if (getDefaultPassword) {
        form.setFieldsValue({
          password: getDefaultPassword.default_password,
        });
      }
    }
  }, [editData, getDefaultPassword, form]);

  useEffect(() => {
    if (editData?.policies?.length) {
      editData.policies.forEach((policy) => {
        if (policy.module === "project") setCreateProjects(policy.can_create);
        if (policy.module === "task") setCreateTask(policy.can_create);
      });
    } else {
      setCreateProjects(false);
      setCreateTask(false);
    }
  }, [editData]);
  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      form.resetFields();
      setCreateProjects(false);
      setCreateTask(false);
      refreshDataTable();
      fetchApi();
    }
  };

  const handleSwitchChange = (setter) => (checked) => setter(checked);
  return (
    <Form
      name="createemployee"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
      autoComplete="off"
    >
      <div className="row">
        <div className="col-12 col-md-6">
          <BaseInput
            name="name"
            placeholder="Enter name"
            label="Employee Name"
            rules={create_employee.name}
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="email"
            label="Email Address"
            disabled={!!editData}
            placeholder="Enter email"
            rules={create_company.email}
          />
        </div>
        {!editData && (
          <div className="col-12 col-md-6">
            <BaseInput
              type="password"
              name="password"
              label="Password"
              disabled={!!editData}
              placeholder="Enter Password"
              value={getDefaultPassword?.default_password}
            />
            <span className="font-12">
              This password is general for all employees. Although, employees
              can change their password later.
            </span>
          </div>
        )}

        <div className="col-12 col-md-6">
          <BaseInput
            type="select"
            name="user_type_id"
            placeholder="Select user type"
            label="User Type"
            options={data?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            loading={loading}
            rules={create_employee.user_type}
          />
        </div>
        <>
          {[
            {
              label: "Create Projects",
              setter: setCreateProjects,
              value: createProjects,
            },
            { label: "Create Task", setter: setCreateTask, value: createTask },
          ].map(({ label, setter, value }, index) => (
            <div className="col-12 mt-4" key={index}>
              <div className="d-flex align-items-center">
                <Switch checked={value} onChange={handleSwitchChange(setter)} />
                <span className="ms-2">{label}</span>
              </div>
            </div>
          ))}
        </>
        <div className="text-end mt-4">
          <FlatButton
            title={editData ? "Update" : "Save"}
            className="add-new-btn"
            htmlType="submit"
            loading={editData ? editLoading : createLoading}
          />
        </div>
      </div>
    </Form>
  );
};

export default memo(AddUserForm);
