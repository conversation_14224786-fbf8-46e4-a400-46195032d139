.auth {
  display: flex;
  min-height: 100vh;
  align-items: stretch;
  flex-direction: row; /* Default for larger screens */
}

.auth-sidebar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa; /* Optional background color */
  position: fixed;
  width: 59%;
}

.auth-sidebar img {
  height: 100%;
  width: 100%;
  object-fit: cover; /* Ensures the image covers the area */
}

.auth-logo {
  padding-left: 100px;
  margin-top: 55px;
}

.auth-box h1 {
  color: #1b1b1f;
}

.auth-box p {
  color: #717171;
}


.sigup-text {
  text-align: center;
  position: absolute;
  bottom: 20px;
  right: 12%;
}

.auth .container {
  padding: 0;
}
.auth-box .form-items label {
  color: #8d96a7!important;
  margin-top: 16px;
  margin-bottom: 4px;
}

.auth-box {
  width: 72%;
  margin: 0 auto;
  position: relative;
  margin-top: 100px;
  min-height: 80vh;

}

