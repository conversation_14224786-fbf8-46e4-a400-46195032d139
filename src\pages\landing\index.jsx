import React, { memo } from "react";
import { Link } from "react-router-dom";
import "./home.css";
const index = () => {
  return (
    <div className="user-role-page">
      <header className="header-area py-3">
        <div className="container text-center">
          <Link to={`/`}>
            <img
              src={`/admin/assets/img/auth-logo.svg`}
              alt="CONSTRUCTIFIED"
              className="brand-logo"
            />
          </Link>
        </div>
      </header>

      <main>
        <section className="user-role-sec">
          <div className="container text-center">
            <h2 className="display-5 mb-3">Choose your Role!</h2>
            <p className="lead">
              Please select your role before moving further
            </p>
            <div className="d-flex align-items-center justify-content-center gap-4 mt-4">
              <Link to={`/login/company`} className="role-box text-center">
                <div className="icon-box">
                  <img
                    src={`/admin/assets/img/company.png`}
                    alt="Company"
                    className="img-fluid"
                  />
                </div>
                <h3 className="h4 mt-2">Company</h3>
              </Link>
              <Link to={`/login/user`} className="role-box text-center">
                <div className="icon-box">
                  <img
                    src={`/admin/assets/img/employee.png`}
                    alt="Employee"
                    className="img-fluid"
                  />
                </div>
                <h3 className="h4 mt-2">Employee</h3>
              </Link>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default memo(index);
