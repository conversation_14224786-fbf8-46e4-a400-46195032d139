import React from "react";
import AuthSidebar from "./authsidebar";

const AuthLayout = ({
  children,
  logoClass = "auth-logo",
  title,
  detail,
  src = "/admin/assets/img/auth-img.png",
}) => {
  return (
    <div className="auth">
      <div className="container">
        <div className="row gx-0">
          <div className="col-12 col-sm-12 col-md-6 col-lg-7">
            <AuthSidebar src={src} />
          </div>
          <div className="col-12 col-sm-12 col-md-6 col-lg-5">
            <div className={logoClass}>
              <img
                src={`/admin/assets/img/auth-logo.svg`}
                alt="CONSTRUCTIFIED"
                className="brand-logo"
              />
            </div>
            <div className="auth-box">
              <div className="col-12">
                <p className="font-36 color-black">{title}</p>
                <p>{detail}</p>
              </div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
