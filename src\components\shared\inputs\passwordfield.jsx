import React from "react";
import { Form, Input } from "antd";
const PasswordField = ({
  name,
  rules,
  placeholder,
  icon,
  label,
  defaultValue,
  disabled,
}) => {
  return (
    <Form.Item label={label} name={name} rules={rules} validateTrigger="onBlur">
      <Input.Password
        size="large"
        placeholder={placeholder}
        prefix={icon}
        defaultValue={defaultValue}
        disabled={disabled}
      />
    </Form.Item>
  );
};

export default PasswordField;
