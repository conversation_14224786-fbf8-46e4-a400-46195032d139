import React, { useState } from "react";
import Slider from "react-slick";

const CustomSlider = ({ children, imageCount }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  function SampleNextArrow(props) {
    const { className, onClick } = props;
    return (
      <div className={className} onClick={onClick}>
        <img src="../assets/img/right-arrow-slider.png" alt="Next" />
      </div>
    );
  }

  function SamplePrevArrow(props) {
    const { className, onClick } = props;
    return (
      <div className={className} onClick={onClick}>
        <img src="../assets/img/left-arrow-slider.png" alt="Previous" />
      </div>
    );
  }

  const settings = {
    dots: true,
    infinite: false, // Disable infinite scrolling if there's only one image
    speed: 500,
    slidesToShow: 1, // Always show one slide at a time
    slidesToScroll: 1,
    beforeChange: (oldIndex, newIndex) => setCurrentSlide(newIndex),
    nextArrow:
      imageCount > 1 && currentSlide < imageCount - 1 ? (
        <SampleNextArrow />
      ) : null,
    prevArrow: imageCount > 1 && currentSlide > 0 ? <SamplePrevArrow /> : null,
  };

  return <Slider {...settings}>{children}</Slider>;
};

export default CustomSlider;
