import React from "react";
import ReactDOM from "react-dom/client";
import Routes from "./routes";
import "bootstrap/dist/css/bootstrap.min.css";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import bootstrap from "./bootstrap.js";
import { BrowserRouter } from "react-router-dom";
import "react-phone-number-input/style.css";
import "./css/style.css";
import { ViewProvider } from "./store/viewContext.jsx";

ReactDOM.createRoot(document.getElementById("root")).render(
  <ViewProvider>
    <BrowserRouter>
      <Routes />
    </BrowserRouter>
  </ViewProvider>
);
bootstrap();
