const create_company = {
  name: [
    { required: true, message: "Name is required!" },
    { min: 3, message: "Name must be more than 2 characters" },
    { max: 30, message: "Name must not be more than 30 characters" },
  ],
  email: [
    { required: true, message: "Email is required!" },
    { type: "email", message: "Enter a valid Email address" },
  ],
  company_name: [
    { required: true, message: "Company name is required!" },
    { min: 3, message: "Company name must be more than 2 characters" },
    { max: 40, message: "Company name must not be more than 40 characters" },
  ],
  company_details: [
    { required: true, message: "Company details is required!" },
    { min: 3, message: "Company details must be more than 2 characters" },
    {
      max: 100,
      message: "Company details must not be more than 40 characters",
    },
  ],
  company_size: [
    {
      required: true,
      message: "Company size is required!",
    },
  ],
  password: [
    { required: true, message: "Password is required!" },
    {
      min: 8,
      message: "Password must be at least 8 characters long",
    },
    {
      pattern: /^(?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])(?=.*[a-z]).{8,150}$/,
      message:
        "Password should contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    },
  ],
  confirmpassword: [
    { required: true, message: "Confirm Password is required!" },
    {
      min: 8,
      message: "Password must be at least 8 characters long",
    },

    // Custom validator to match password
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue("password") === value) {
          return Promise.resolve();
        }
        return Promise.reject(
          new Error("Confirm Password does not match with Password")
        );
      },
    }),
  ],
};
const create_user_type = {
  title: [
    { required: true, message: "Title is required!" },
    { min: 3, message: "Title must be more than 2 characters" },
    { max: 40, message: "Title must not be more than 40 characters" },
  ],
};
const create_employee = {
  name: [
    { required: true, message: "Employee Name is required!" },
    { min: 2, message: "Employee Name must be more than 2 characters" },
    { max: 40, message: "Employee Name must not be more than 40 characters" },
  ],
  user_type: [{ required: true, message: "User Type is required!" }],
};
const create_project = {
  title: [
    { required: true, message: "Project Name is required!" },
    { min: 2, message: "Project Name must be more than 2 characters" },
    { max: 40, message: "Project Name must not be more than 40 characters" },
  ],
  members: [{ required: true, message: "Assignee Members is required!" }],
  address: [
    { required: true, message: "Location is required!" },
    // { min: 2, message: "Location Name must be more than 2 characters" },
    // { max: 40, message: "Location Name must not be more than 40 characters" },
  ],
  completion_at: [{ required: true, message: "Completion Date is required!" }],
};
const create_directories = {
  title: [
    { required: true, message: "Folder Name is required!" },
    { min: 2, message: "Folder Name must be more than 2 characters" },
    { max: 40, message: "Folder Name must not be more than 40 characters" },
  ],
};
const create_task = {
  title: [
    { required: true, message: "Title is required!" },
    { min: 3, message: "Title must be more than 2 characters" },
    { max: 30, message: "Title must not be more than 30 characters" },
  ],
  project_id: [{ required: true, message: "Project Title is required!" }],
  root_directory: [{ required: true, message: "Folder is required!" }],
  sub_directory: [{ required: true, message: "Sub Folder is required!" }],
  assignees: [{ required: true, message: "Assignee is required!" }],
  start_at: [{ required: true, message: "Start Date is required!" }],
  end_at: [{ required: true, message: "End Date is required!" }],
  description: [{ required: true, message: "Description is required!" }],
};
export {
  create_company,
  create_user_type,
  create_employee,
  create_project,
  create_directories,
  create_task,
};
