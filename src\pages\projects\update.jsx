import React, { memo, useEffect, useState } from "react";
import { Form, notification } from "antd";
import BaseInput from "../../components/shared/inputs";
import { create_project } from "../../config/rules";
import FlatButton from "../../components/shared/button/flatbutton";
import { useFetch } from "../../hooks";
import dayjs from "dayjs";
import CustomUpload from "../../components/shared/upload";

const UpdateProjectForm = ({ project, onSuccess, onCancel }) => {
  const currentUserId = window.user?.user._id;
  const { data } = useFetch("employee", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 2000 },
  });
  const { loading, postData } = useFetch("update_project", { type: "submit" });
  const [form] = Form.useForm();
  const [uploadedImages, setUploadedImages] = useState([]);
  const [completionDate, setCompletionDate] = useState(
    project.completion_at || null
  );
  const [initialCompletionDate] = useState(project.completion_at || null);
  useEffect(() => {
    if (project) {
      const visibleMembers =
        project.members
          ?.filter((member) => member._id !== window.user?.user._id)
          .map((member) => member._id) || [];
      form.setFieldsValue({
        title: project.title || "",
        members: visibleMembers,
        address: project.address || "",
        type: project.type || "",
        completion_at: project.completion_at
          ? dayjs(project.completion_at)
          : null,
      });
      setUploadedImages(project.image_url || []);
    } else {
      form.resetFields();
      setUploadedImages([]);
    }
  }, [project, form]);

  const onFinish = (values) => {
    if (!uploadedImages || uploadedImages.length === 0) {
      notification.error({
        message: "Images Required",
        description: "Please add at least one image for the project.",
        placement: "topRight",
      });
      return;
    }
    const fd = new FormData();
    const wasCurrentUserMember = project.members?.some(
      (member) => member._id === currentUserId
    );
    const selectedMembers = wasCurrentUserMember
      ? [...(values.members || []), currentUserId]
      : values.members || [];
    const initialMembers = project.members?.map((member) => member._id) || [];

    const removedMembers = initialMembers.filter(
      (id) => !selectedMembers.includes(id)
    );
    const newMembers = selectedMembers.filter(
      (id) => !initialMembers.includes(id)
    );

    removedMembers.forEach((memberId) => {
      fd.append("_members[]", memberId);
    });
    selectedMembers.forEach((memberId) => {
      fd.append("members[]", memberId);
    });

    const removedImages =
      project.image_url?.filter((url) => !uploadedImages.includes(url)) || [];

    const newImages = uploadedImages.filter(
      (url) => !project.image_url?.includes(url)
    );

    removedImages.forEach((image) => fd.append("_image[]", image));
    newImages.forEach((image) => fd.append("image", image));

    if (completionDate && completionDate !== initialCompletionDate) {
      fd.append("completion_at", completionDate);
    }

    Object.entries(values).forEach(([key, value]) => {
      if (!["members", "image", "completion_at"].includes(key)) {
        fd.append(key, value);
      }
    });

    postData(fd, cbSuccess, project._id);
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      form.resetFields();
      onSuccess();
    }
  };
  return (
    <Form
      name="update-project"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{ remember: true }}
      form={form}
      autoComplete="off"
    >
      <BaseInput
        name="title"
        placeholder=""
        label="Project Name"
        rules={create_project.title}
      />
      <BaseInput
        name="members"
        mode="multiple"
        type="select"
        placeholder=""
        label="Assignee Members"
        options={data
          ?.filter((item) => item._id !== window.user?.user._id) // Exclude current user
          .map((item) => ({
            value: item._id,
            label: item.name,
          }))}
        rules={create_project.members}
      />
      <BaseInput
        name="address"
        placeholder=""
        label="Location"
        rules={create_project.address}
      />
      <BaseInput
        name="completion_at"
        type="datepiker"
        placeholder=""
        label="Completion Date"
        rules={create_project.completion_at}
        disablePastDates={true}
        onChange={(date) => {
          const formattedDate = dayjs(date).format("YYYY-MM-DD");
          setCompletionDate(formattedDate);
        }}
      />
      <BaseInput
        name="type"
        placeholder=""
        label="Type (Ex: Pre-Construction)"
        rules={[{ required: true, message: "Type is required!" }]}
      />
      <label className="color-black font-600 mt-3 mb-1">Add Images</label>
      <CustomUpload
        value={uploadedImages}
        maxFiles={10}
        onChange={(images) => setUploadedImages(images)}
        allowedTypes={["image/jpeg", "image/jpg", "image/png"]}
      />
      <div className="text-end mt-4">
        <FlatButton
          title="Update"
          className="add-new-btn"
          htmlType="submit"
          loading={loading}
        />
      </div>
    </Form>
  );
};

export default memo(UpdateProjectForm);
