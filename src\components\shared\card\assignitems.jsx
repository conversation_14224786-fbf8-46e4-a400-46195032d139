import React from "react";
import { Link } from "react-router-dom";

const AssignItems = ({
  short_id,
  _id,
  title,
  className = "assign-user-avatar",
  project,
}) => {
  return (
    <div className="assign-item d-flex align-items-center">
      <Link
        to={`/assign-task/${_id}`}
        className="d-flex align-items-center w-100"
      >
        <div className={className}>
          <p>{window.helper.getInitials(title)}</p>
        </div>
        <div>
          <p className="font-12 color-light">
            ID: {short_id} | {project.title}
          </p>
          <p className="font-16 color-black">{title}</p>
        </div>
      </Link>
    </div>
  );
};

export default AssignItems;
