import{r,a as k,c as A,l as D,j as e,S as E}from"./index-C6D6r1Zc.js";import{I as F,P as N,a as T}from"./index-D5NY69qa.js";import{u as p,B as M,a as $,e as z}from"./rules-CIBIafmf.js";import{C as L}from"./customtable-DkQ9Gs_O.js";import{B as Q}from"./backbutton-CVjBhEnB.js";import{b as R}from"./tabledata-D45VJVix.js";import{A as U}from"./addfolder-DqrRFHq5.js";import"./index-BiW9UDU7.js";import"./notification-C5lHSl-d.js";import"./index-DDGMAcG0.js";import"./Pagination-COdCWbqj.js";const K=()=>{var f,x;const g=(f=window.user)==null?void 0:f.user,{data:a}=p("get_profile",{type:"mount",slug:`/${g._id}`,enablePagination:!1}),{showAlert:j}=z();let{project_id:l,parent_id:w}=k();const y=A(),{record:o}=y.state||{},[C,c]=r.useState(!1),[i,P]=r.useState(""),{loading:h,data:S,fetchApi:d,pagination:s,setQueryParams:u}=p("directories",{slug:`/?project_id=${l}&parent_id=${w}&`,enablePagination:!0,defaultQueryParams:{page:1,limit:10}}),{postData:_}=p("delete_directories",{type:"submit"});r.useEffect(()=>{d()},[l]),r.useEffect(()=>{u(t=>({...t,keyword:i}))},[i]);const b=async t=>{(await j({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&_("",B,t)},B=t=>{t.statusCode===200&&d()},v=(t,n)=>{u(m=>({...m,page:t,limit:n,keyword:i}))},I=D.debounce(t=>{const n=t.target.value;P(n),u(m=>({...m,keyword:n,page:1}))},300);return h&&window.lodash.isEmpty(o)?e.jsxs("div",{style:{display:"flex",justifyContent:"center",height:"100vh",alignItems:"center"},children:[e.jsx(E,{size:"large"})," "]}):e.jsxs(e.Fragment,{children:[e.jsxs(F,{children:[e.jsx(N,{title:e.jsx(Q,{title:o==null?void 0:o.title}),buttons:e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx(M,{name:"search",placeholder:"Search",icon:e.jsx("img",{src:"/admin/assets/img/search-icon.png"}),value:i,onChange:I})}),((a==null?void 0:a.role)==="company"||((x=a==null?void 0:a.policies)==null?void 0:x.some(t=>t.module==="project"&&t.can_create)))&&e.jsx("div",{children:e.jsx($,{title:"Upload File",className:"mx-auto add-new-btn me-3",onClick:()=>c(!0)})})]})}),e.jsx("div",{className:"detail-table mt-4 mb-5",children:e.jsx(L,{columns:R(b,a),data:S,loading:h,rowKey:"_id",pagination:{current:s==null?void 0:s.currentPage,total:s==null?void 0:s.count,pageSize:s==null?void 0:s.perPage},showPagination:!0,onChange:v})})]}),e.jsx(T,{title:"Upload PDF",open:C,onCancel:()=>c(!1),className:"custom-modal",footer:!1,children:e.jsx(U,{onCancel:()=>c(!1),onSuccess:d,projectId:l,parentId:o==null?void 0:o._id,type:"upload-pdf"})})]})},te=r.memo(K);export{te as default};
