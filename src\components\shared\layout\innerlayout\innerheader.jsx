import React, { useState } from "react";
import { Layout, Avatar, theme } from "antd";
import CustomDropdown from "../../dropdown";
import CustomModal from "../../modal";
import NotificationSettingForm from "../../../partial/modalforms/notificationsettingform";
import ChangePasswordForm from "../../../partial/modalforms/changepasswordform";
import EditProfileForm from "../../../partial/modalforms/editprofileform";
import { useNavigate } from "react-router-dom";
import useSweetAlret from "../../../../hooks/useSweetAlert";
import { useFetch } from "../../../../hooks";
import { requestPermissionAndGetToken } from "../../../../helpers/notification";
const { Header } = Layout;

const InnerHeader = () => {
  let user = window.user.user;
  const navigate = useNavigate();
  const { showAlert } = useSweetAlret();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isChangePassword, setIsChangePassword] = useState(false);
  const [isEditProfile, setIsEditProfile] = useState(false);
  const { data } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });
  const { postData } = useFetch("remove_device_token", {
    type: "submit",
    skipNotification: true,
  });
  const handleLogout = async () => {
    const fd = new FormData();
    let deviceToken = await requestPermissionAndGetToken();
    deviceToken = `web|${deviceToken}`;
    fd.append("device_token", deviceToken);
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6", // Light background
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      postData(fd, cbSuccess);
    }
  };
  const cbSuccess = (res) => {
    if (res.statusCode == 200) {
      window.helper.removeStorageData();
      return navigate("/");
    }
  };
  const companyItems = [
    {
      label: <p className="color-blue font-16">{data?.company_name}</p>,
      key: "0",
    },
    {
      label: <p>{data?.mobile_no}</p>,
      key: "1",
    },
    {
      label: <p>{data?.company_size} Employees</p>,
      key: "2",
    },
    {
      type: "divider",
    },
    {
      label: <p className="color-light">{data?.company_details}</p>,
      key: "3",
    },
    {
      label: (
        <p
          onClick={() => setIsEditProfile(true)}
          className="color-blue font-14"
        >
          Edit Profile
        </p>
      ),
      key: "4",
    },
    {
      label: (
        <p
          onClick={() => setIsChangePassword(true)}
          className="color-blue font-14"
        >
          Change Password
        </p>
      ),
      key: "5",
    },
    {
      label: (
        <p onClick={() => setIsModalOpen(true)} className="color-blue font-14">
          {" "}
          Notification Settings
        </p>
      ),
      key: "6",
    },
    {
      type: "divider",
    },
    {
      label: (
        <p onClick={handleLogout} className="color-red">
          Log Out
        </p>
      ), // Use handleLogout for the Log Out action
      key: "7",
    },
  ];
  const employeeItems = [
    {
      label: <p className="color-blue font-16">{data?.name}</p>,
      key: "0",
    },
    {
      label: <p>{data?.mobile_no ? data?.mobile_no : ""}</p>,
      key: "1",
    },
    {
      label: <p>{data?.email}</p>,
      key: "2",
    },
    {
      type: "divider",
    },
    {
      label: <p className="color-light">{data?.company_details}</p>,
      key: "3",
    },
    {
      label: (
        <p
          onClick={() => setIsEditProfile(true)}
          className="color-blue font-14"
        >
          Edit Profile
        </p>
      ),
      key: "4",
    },
    {
      label: (
        <p
          onClick={() => setIsChangePassword(true)}
          className="color-blue font-14"
        >
          Change Password
        </p>
      ),
      key: "5",
    },
    {
      label: (
        <p onClick={() => setIsModalOpen(true)} className="color-blue font-14">
          {" "}
          Notification Settings
        </p>
      ),
      key: "6",
    },
    {
      type: "divider",
    },
    {
      label: (
        <p onClick={handleLogout} className="color-red">
          Log Out
        </p>
      ), // Use handleLogout for the Log Out action
      key: "7",
    },
  ];
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  return (
    <>
      <Header
        style={{
          padding: 0,
          background: colorBgContainer,
        }}
      >
        <div>
          <CustomDropdown
            className="profile-dropdown"
            items={data?.role == "user" ? employeeItems : companyItems}
            title={<Avatar size={50} src={data?.image_url} />}
          />
        </div>
      </Header>
      <CustomModal
        title="Notification Settings"
        onCancel={() => setIsModalOpen(false)}
        open={isModalOpen}
        className="custom-modal notification-setting-modal"
        footer={false}
      >
        <NotificationSettingForm onCancel={() => setIsModalOpen(false)} />
      </CustomModal>
      <CustomModal
        title="Change Password"
        onCancel={() => setIsChangePassword(false)}
        open={isChangePassword}
        className="custom-modal"
        footer={false}
      >
        <ChangePasswordForm onCancel={() => setIsChangePassword(false)} />
      </CustomModal>
      <CustomModal
        title="Edit Profile"
        onCancel={() => setIsEditProfile(false)}
        open={isEditProfile}
        className="custom-modal"
        footer={false}
      >
        <EditProfileForm
          onCancel={() => setIsEditProfile(false)}
          visible={isEditProfile}
        />
      </CustomModal>
    </>
  );
};

export default InnerHeader;
