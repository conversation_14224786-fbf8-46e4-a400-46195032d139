aside.ant-layout-sider{
    overflow: auto;
    height: 100vh;
    position: fixed;
    inset-inline-start: 0px;
    top: 0px;
    bottom: 0px;
    scrollbar-width: thin;
    scrollbar-color: unset;
    flex: 0 0 240px !important;
    max-width: 240px !important;
    min-width: 240px !important;
    width: 240px !important;

}
.ant-layout {
    margin-inline-start: 120px  !important;
} 
.header-profile{
    width: 65px;
    height: 65px;
    overflow: hidden;
    border-radius: 50%;
    margin: 0 auto;
}
.header-profile img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}
.ant-layout-header {
    text-align: end !important;
    padding-right: 20px;
}
.demo-logo-vertical{
    margin-top: 15px;
    padding: 10px;
    margin-bottom: 20px;
}
.ant-layout-sider{
    background-color: #fff !important;
}
.ant-menu{
    background-color: #fff !important;
   
}
.ant-menu-item {
    color: #3a3a3c !important;
    font-size: 16px !important;
    padding: 4px 12px !important;
    margin-bottom: 20px !important;
}
.ant-menu-item-selected{
    background-color: transparent !important;
}
.ant-menu-item-selected a{
    color: #4893ca !important;
}

.ant-layout-header>div {
    position: relative;
}

.ant-layout-header>div::before{
    content: "";
    background-color: rgba(141, 150, 167, 0.4);
    position: absolute;
    top: 20%;
    width: 1px;
    height: 31px;
    right: 4%;
}
.content-header{
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.content-header h2{
    font-size: 26px;
    color: #000124;
}
.content-header-child{
    display: flex;
    align-items: center;
}
.content-header-child .ant-form-item{
    margin: 0 !important;
    background-color: transparent !important;
}
.content-header-child .ant-form-item input{
    background-color: transparent !important;
    border-color: #e2e6ed;
}
.detail-slider-box{
    background-color: #fff;
    padding: 2px;
}
.detail-slider-box>.row{
    border-radius: 10px;
    background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
    margin: 2px;
}
.slider-img {
    height: 280px;
    overflow: hidden;
}

.slider-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}
.slick-prev:before, .slick-next:before{
    content: none !important;
}
.slick-prev {
    left: 15px;
    z-index: 1;
}
.slick-next {
    right: 30px;
}
.slick-dots li {
    position: relative;
    display: inline-block;
    width: 9px;
    height: 16px;
    margin: 0 3px;
    padding: 0;
    cursor: pointer;
}
.slick-dots {
    position: absolute;
    bottom: 19px;
    width: 100%;
    text-align: center;
}
.ant-avatar{
    background-color: transparent;
}
.avatar-img .ant-avatar-string{
    width: 36px;
    height: 36px;
    overflow: hidden;
    border-radius: 50%;
}
.avatar-img .ant-avatar-string img{
    width: 100%;
    height: 100%;
    object-fit: cover !important;
    border-radius: 50%;
}
.ant-table-thead th{
    background-color: #4893ca !important;
    color: #fff !important;
}
.ant-table-thead th::before{
    content: none !important;
}
.ant-table-tbody td{
    color: #8d96a7 !important;
}
.ant-table-content{
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
}
.assign-header{
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
    padding: 15px ;
    border-bottom: 1px solid #c6d1e6;
}
.assign-header p{
    font-size: 18px;
    color: #3a3a3c;
    font-weight: 600;
    text-align: center;
}
.assign-body{
    background-color: #f6f9fd;
    min-height: 500px;
}
.assign-item{
    border-bottom: 1px solid #c6d1e6;
    padding: 15px 15px;
}
.assign-user-avatar{
    width: 40px;
    height: 40px;
    background-color: red;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 10px;
}
.assign-user-avatar p{
    color: #fff;
    font-size: 12px;
}
.noti-title{
    font-size: 18px; 
    color:#3a3a3c;
    font-weight: 600;
    margin-bottom: 10px !important;
}
.notification-card{
    border-radius: 8px;
    background-color: #fff;
    padding: 20px 15px;
    margin-bottom: 10px;
}
.notification-card .ant-checkbox-wrapper  span{
    color: #000126;
    font-size: 16px;
    font-weight: 500;

}
.static-box{
    border-radius: 12px;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 15px;
}
.static-box p{
    color:#1b1b1f;
    margin-bottom: 20px !important;
}

.chat-box{
    background-color: #ffff;
    padding: 20px;
}
.chat-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #c6d1e6;
    padding-bottom: 16px;
}
.chat-body{
    margin-top: 30px;
   

}
.user-first-text{
    background-color: rgba(198, 209, 230, 0.2);
    display: inline-block;
    margin-left: 50px;
    padding: 4px;
    color: #1b1b1f;
    border-radius: 4px;
}
.text-time{
    color:#c6d1e6 !important;
}
.chat-footer .ant-form-item{
    margin-top: 0 !important;
    width: 85%;
}
.chat-footer .send-btn{
    border: solid 1px #c6d1e6;
    background-color: #365cd0;
    padding: 20px 30px;
    font-size: 16px;
}
.task-attributes{
    padding: 20px;
    border-radius: 4px;
    background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
}
.attributes-body{
    position: relative;
}
.attributes-header p{
    font-size: 18px;
    padding-bottom: 10px !important;
    color:#3a3a3c;
}
.attributes-img{
    width: 100%;
    height: 260px;
}
.attributes-img img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.attributes-items{
    border-bottom: 1px solid rgba(198, 209, 230, 0.5);
    padding: 15px;

}
.attributes-btn{
    margin-top: 20px ;
    width: fit-content;
    padding: 20px 50px;
    font-size: 16px;
    color: #fff;
    background-color: #365cd0;
}
.custom-modal .ant-modal-content {
    padding: 0 !important;
    border-radius: 4px;
    background-image: linear-gradient(to bottom, rgba(248, 251, 255, 0.5), rgba(225, 233, 247, 0.5));
}
.custom-modal .ant-modal-header {
    padding: 16px !important;
    border-bottom: 1px solid rgba(198, 209, 230, 0.5);
    background-color: transparent;
}
.custom-modal .ant-modal-header  .ant-modal-title{
    color: #3a3a3c;
}
.custom-modal .ant-modal-body {
    padding: 5px 30px 15px 30px;
}

.custom-modal .ant-modal-footer {
    padding: 15px;
}
.custom-modal .ant-form-item{
    margin-top: 20px !important;
}
.custom-modal .ant-form-item .ant-form-item-label {
    padding-bottom: 2px !important;
}
.custom-modal .ant-form-item label{
    color:#1b1b1f !important;
    padding-bottom: 2px !important;
    font-weight: 600;
}

.table-dropdown .ant-space {
    border: 1px solid #8d96a7 !important;
    width: 160px;
    padding: 0 10px;
    border-radius: 6px;
    height: 30px;
    justify-content: space-between;
}
.subscription-box{
    background-color: #fff;
    padding: 22px;
    border-radius: 4px;
}


.custom-tabs .ant-tabs-tab {
    border-radius: 4px;
    padding: 8px 45px;
    margin: 0 !important;
}
.custom-tabs .ant-tabs-nav-list {
    border-radius: 4px;
    border: solid 1px #c6d1e6;
    background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
}

.custom-tabs .ant-tabs-nav-wrap {
    justify-content: center;
   
    
}

.custom-tabs .ant-tabs-nav-wrap::before {
    content: none !important;
}

.custom-tabs .ant-tabs-nav::before {
    content: none;
}

.custom-tabs .ant-tabs-tab-active {
    background-image: linear-gradient(to bottom, #5d92c5, #35569a);
    border-bottom: 0 !important;
    outline: none;
}

.custom-tabs .ant-tabs-tab-active>div {
    color: #fff !important;
    border: 0 !important;
}
.ant-tabs-ink-bar.ant-tabs-ink-bar-animated{
    display: none;
}
.suscription-card{
    border: 1px solid #dcdcdc ;
    padding: 20px ;
    text-align: center;
    border-radius: 12px;
}
.suscription-card h2,
.suscription-card  span{
    color: #28569f;
}
.suscription-card-active h2,
.suscription-card-active span{
    color: #c6d1e6;
}
.suscription-card ul{
    list-style: disc;
}
.suscription-card ul li{
    margin-bottom: 10px;
}
.suscription-card ul li:before{
    content: '';
    width: 5px;
    height: 5px;
    margin-right: 12px;
    display: inline-block;
    background:#28569f;
    background-position: 50%;
    border-radius: 50%;
  }
  .suscription-business-btn{
    border-radius: 4px;
    background-image: linear-gradient(to right, #5d92c5, #35569a);
    color: #fff !important;
    padding: 10px 40px;
    border: 0;
  }
  .suscription-business-btn span{
    color: #fff !important;
  }
  .suscription-business-btn span{
    color: #fff !important;
  }
.suscription-btn{
    border-radius: 4px;
    border: solid 1px #d0d1d6;
    background-color: #fff;
    color: #1b1b1f !important;
    padding: 10px 40px;
   
}
  .suscription-active-btn{
    border-radius: 4px;
    background-image: linear-gradient(to right, #f8fbff, #e1e9f7);
    color: #28569f;
    padding: 10px 40px;
    border: 0;
  }


.custom-modal  .form-items{
    margin-top:20px ;
}
.custom-modal  .form-items .ant-picker {
    height: 40px;
}
.custom-modal  .form-items label{
    font-weight: 600;
    margin-bottom: 5px;
}