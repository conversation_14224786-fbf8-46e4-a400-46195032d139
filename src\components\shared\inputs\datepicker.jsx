import React from "react";
import { Form, DatePicker } from "antd";
import moment from "moment";
const DatePickerInput = ({
  onChange,
  placeholder,
  label,
  name,
  rules,
  disablePastDates = false,
  format,
}) => {
  return (
    <div className="form-items">
      <Form.Item
        label={label}
        name={name}
        rules={rules}
        validateTrigger="onBlur"
      >
        <DatePicker
          placeholder={placeholder}
          onChange={onChange}
          format={format}
          disabledDate={
            disablePastDates
              ? (current) => current && current < moment().startOf("day")
              : undefined
          }
        />
      </Form.Item>
    </div>
  );
};

export default DatePickerInput;
