import React, { useEffect, useRef, useState } from "react";
import { getDocument, GlobalWorkerOptions } from "pdfjs-dist";
import workerSrc from "pdfjs-dist/build/pdf.worker.mjs?url";
import { Checkbox } from "antd";

GlobalWorkerOptions.workerSrc = workerSrc;

const PdfFirstPageThumbnail = ({ pdfUrl, onSelect, checked, onChange }) => {
  const canvasRef = useRef(null);
  const [img, setImg] = useState("");

  useEffect(() => {
    const renderFirstPage = async () => {
      const pdf = await getDocument(pdfUrl).promise;
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 0.3 });

      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      await page.render({ canvasContext: context, viewport }).promise;
      setImg(canvas.toDataURL());
    };

    renderFirstPage();
  }, [pdfUrl]);

  return (
    <div style={{ display: "flex", alignItems: "center", marginRight: 20 }}>
      <canvas ref={canvasRef} style={{ display: "none" }} />
      <div style={{ position: "relative", marginRight: 10 }}>
        <Checkbox
          checked={checked}
          onChange={onChange}
          style={{
            position: "absolute",
            top: 4,
            right: 4,
            zIndex: 1,
            backgroundColor: "white",
            borderRadius: "4px",
          }}
        />
        {img && (
          <img
            src={img}
            alt="PDF Page 1"
            style={{
              width: "90px",
              height: "90px",
              borderRadius: 12,
              border: checked ? "2px solid #4893ca" : "1px solid #ddd",
              cursor: "pointer",
            }}
            onClick={() => onChange?.({ target: { checked: !checked } })}
          />
        )}
      </div>
    </div>
  );
};

export default PdfFirstPageThumbnail;
