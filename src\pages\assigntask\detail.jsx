import React, { memo, useMemo, useEffect } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import PageTitle from "../../components/shared/pagetitle";
import BackButton from "../../components/shared/button/backbutton";
import ChatBox from "../../components/shared/chatbox";
import TaskAttributesCard from "../../components/shared/card/taskattributescard";
import AttributeItem from "../../components/shared/card/attributeitem";
import "./assigntask.css";
import { useNavigate, useParams } from "react-router-dom";
import { useFetch } from "../../hooks";
import Loader from "../../components/shared/loader";
import { message } from "antd";

const AssignTaskDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    loading,
    data: task,
    fetchApi,
  } = useFetch("task", {
    type: "mount",
    slug: `/${id}`,
  });

  const { postData } = useFetch("update_task", { type: "submit" });

  useEffect(() => {
    const checkDeletedTask = async () => {
      try {
        const result = await fetchApi();

        if (result === "Record not found") {
          message.error("This content is no longer available.");
          navigate("/assign-task");
        }
      } catch (error) {
        console.log("error", error);
        if (error?.response?.data?.message === "Record not found") {
          message.error("This content is no longer available.");
          navigate("/assign-task");
        }
      }
    };

    if (!loading) {
      checkDeletedTask();
    }
  }, [id, loading, navigate, fetchApi]);
  const dynamicAttributes = useMemo(
    () => [
      {
        iconSrc: "/admin/assets/img/status-icon.png",
        label: "Status",
        value: window.helper.capitalizeFirstLetter(task?.status),
      },

      {
        iconSrc: "/admin/assets/img/assignee-icon.png",
        label: "Assignees",
        value: task?.assignees?.map((a) => a._id.name).join(", ") || "-",
      },
      {
        iconSrc: "/admin/assets/img/date-icon.png",
        label: "Start Date",
        value: window.helper.formatDate(task?.start_at),
      },
      {
        iconSrc: "/admin/assets/img/date-icon.png",
        label: "End Date",
        value: window.helper.formatDate(task?.end_at),
      },
      {
        iconSrc: "/admin/assets/img/category-img.png",
        label: "Description",
        value: task?.description || "-",
      },
      {
        iconSrc: "/admin/assets/img/location-icon.png",
        label: "Location",
        value: task?.project?.address || "-",
      },
    ],
    [task]
  );

  const getButtonProps = useMemo(() => {
    const statusMap = {
      pending: { title: "Start", className: "btn-primary" },
      in_process: { title: "Complete", className: "btn-warning" },
      completed: { title: "Complete", className: "btn-success" }, // Changed from "Verify"
      // verified: { title: "Verified", className: "btn-secondary" }, // Commented out verified status
    };

    const normalizedStatus = task?.status?.replace("-", "_");

    // If status is verified, return null to hide button
    if (normalizedStatus === "verified") {
      return null;
    }

    return statusMap[normalizedStatus] || statusMap.pending;
  }, [task?.status]);

  if (loading) {
    return <Loader />;
  }

  if (!task) {
    return null;
  }
  const directory = task?.directories[0]?._id;
  return (
    <InnerLayout>
      <PageTitle title={<BackButton />} />
      {task && (
        <div className="row mb-5">
          <div className="col-12 col-md-6 col-lg-7 col-xl-8 mt-4">
            <ChatBox data={task} fetchApi={fetchApi} />
          </div>
          <div className="col-12 col-md-6 col-lg-5 col-xl-4 mt-4">
            <TaskAttributesCard
              attributes_img={directory?.file}
              id={task?._id}
              btn_title={getButtonProps.title}
              btn_className={getButtonProps.className}
              postData={postData}
              status={task?.status}
              fetchApi={fetchApi}
              pdfTitle={directory?.title}
              directories={task?.directories}
            >
              {dynamicAttributes.map((attr, index) => (
                <AttributeItem key={index} {...attr} />
              ))}
            </TaskAttributesCard>
          </div>
        </div>
      )}
    </InnerLayout>
  );
};

export default memo(AssignTaskDetail);
