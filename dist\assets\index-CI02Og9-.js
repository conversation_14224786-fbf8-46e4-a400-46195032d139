import{r as i,j as e,l as U}from"./index-C6D6r1Zc.js";import{u as p,F,B as P,i as T,c as I,a as O,e as V}from"./rules-CIBIafmf.js";import{e as M,a as Q,c as R}from"./index-D5NY69qa.js";import{U as L}from"./index-DZcQC1Tj.js";import{C as Y}from"./customtable-DkQ9Gs_O.js";import"./index-BiW9UDU7.js";import"./notification-C5lHSl-d.js";import"./index-DDGMAcG0.js";import"./Pagination-COdCWbqj.js";const z=i.memo(a=>{var d,s,c;return e.jsx(Y,{...a,rowKey:"_id",data:Array.isArray(a.data)?a.data:[],pagination:{current:(d=a==null?void 0:a.pagination)==null?void 0:d.currentPage,total:(s=a==null?void 0:a.pagination)==null?void 0:s.count,pageSize:(c=a==null?void 0:a.pagination)==null?void 0:c.perPage},scroll:{x:1e3},onChange:a.handlePageChange,showPagination:!0})}),J=({onCancel:a,refreshDataTable:d,editData:s})=>{const[c,h]=i.useState(!1),[f,x]=i.useState(!1),{data:m,fetchApi:_}=p("get_password",{defaultQueryParams:{page:1,limit:1e3}}),[u]=F.useForm(),{loading:g,data:w}=p("user_type",{defaultQueryParams:{page:1,limit:1e3}}),{loading:k,postData:C}=p("create_employee",{type:"submit"}),{loading:v,postData:E}=p("edit_employee",{type:"submit"}),A=l=>{const r=new FormData;Object.entries(l).forEach(([t,n])=>r.append(t,n));const j=(s==null?void 0:s.policies)||[],b=[{module:"project",can_create:c,can_read:c,can_update:c,can_delete:c},{module:"task",can_create:f,can_read:f,can_update:f,can_delete:f}];[...j,...b].reduce((t,n)=>{const y=t.find(B=>B.module===n.module);return y?Object.assign(y,n):t.push(n),t},[]).forEach(t=>{r.append("policies[]",JSON.stringify(t))}),s?E(r,N,s._id):C(r,N)};i.useEffect(()=>{var l;s?u.setFieldsValue({name:s.name,email:s.email,user_type_id:(l=s.user_type)==null?void 0:l._id,password:(m==null?void 0:m.default_password)||""}):(u.resetFields(),m&&u.setFieldsValue({password:m.default_password}))},[s,m,u]),i.useEffect(()=>{var l;(l=s==null?void 0:s.policies)!=null&&l.length?s.policies.forEach(r=>{r.module==="project"&&h(r.can_create),r.module==="task"&&x(r.can_create)}):(h(!1),x(!1))},[s]);const N=l=>{l.statusCode===200&&(a(),u.resetFields(),h(!1),x(!1),d(),_())},S=l=>r=>l(r);return e.jsx(F,{name:"createemployee",layout:"vertical",onFinish:A,initialValues:{remember:!0},form:u,autoComplete:"off",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(P,{name:"name",placeholder:"Enter name",label:"Employee Name",rules:T.name})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(P,{name:"email",label:"Email Address",disabled:!!s,placeholder:"Enter email",rules:I.email})}),!s&&e.jsxs("div",{className:"col-12 col-md-6",children:[e.jsx(P,{type:"password",name:"password",label:"Password",disabled:!!s,placeholder:"Enter Password",value:m==null?void 0:m.default_password}),e.jsx("span",{className:"font-12",children:"This password is general for all employees. Although, employees can change their password later."})]}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(P,{type:"select",name:"user_type_id",placeholder:"Select user type",label:"User Type",options:w==null?void 0:w.map(l=>({value:l._id,label:l.title})),loading:g,rules:T.user_type})}),e.jsx(e.Fragment,{children:[{label:"Create Projects",setter:h,value:c},{label:"Create Task",setter:x,value:f}].map(({label:l,setter:r,value:j},b)=>e.jsx("div",{className:"col-12 mt-4",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx(M,{checked:j,onChange:S(r)}),e.jsx("span",{className:"ms-2",children:l})]})},b))}),e.jsx("div",{className:"text-end mt-4",children:e.jsx(O,{title:s?"Update":"Save",className:"add-new-btn",htmlType:"submit",loading:s?v:k})})]})})},K=i.memo(J),$=i.memo(({isModalOpen:a,onClose:d,editData:s,refreshDataTable:c})=>e.jsx(Q,{title:s?"Edit Employee":"Add New Employee",width:900,open:a,onCancel:d,className:"custom-modal",footer:!1,children:e.jsx(K,{onCancel:d,refreshDataTable:c,editData:s})})),q=()=>{const[a,d]=i.useState(!1),[s,c]=i.useState(null),[h,f]=i.useState(""),{loading:x,data:m=[],fetchApi:_,pagination:u,setQueryParams:g}=p("employee",{enablePagination:!0,defaultQueryParams:{page:1,limit:10}}),{postData:w}=p("delete_employee",{type:"submit"}),{postData:k}=p("reset_password",{type:"submit"}),{showAlert:C}=V(),v=async(o,t)=>{(await C({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&w("",E,o)},E=o=>{o.statusCode===200&&_()},A=o=>{c(o),d(!0)},N=()=>{c(null),d(!0)},S=async(o,t)=>{const n=new FormData;n.append("user_id",o),(await C({title:"Are you sure?",text:"Do you want to reset password?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&k(n,E)},l=[{title:"User Name",dataIndex:"name",sorter:(o,t)=>{var n,y;return((n=o.name)==null?void 0:n.length)-((y=t.name)==null?void 0:y.length)}},{title:"Email Address",dataIndex:"email"},{title:"User Type",dataIndex:"usertype",render:(o,t)=>{var n;return(n=t==null?void 0:t.user_type)==null?void 0:n.title}},{title:"Action",dataIndex:"action",render:(o,t)=>e.jsx(R,{title:"Action",icon:"true",className:"table-dropdown",items:[{label:e.jsx("p",{className:"color-blue",onClick:()=>A(t),children:"Edit"}),key:"0"},{label:e.jsx("p",{className:"color-green",onClick:()=>S(t._id),children:"Reset password"}),key:"1"},{label:e.jsx("p",{className:"color-red",onClick:n=>v(t._id),children:"Delete"}),key:"2"}]})}],r=i.useCallback(U.debounce(o=>{g({page:1,limit:10,keyword:o})},500),[g]),j=o=>{const t=o.target.value;r(t)},b=(o,t)=>{g({page:o,limit:t,keyword:h})};return e.jsxs(L,{handleSearch:j,handleAddNew:N,title:"Employees",children:[e.jsx("div",{className:"detail-table mt-4 mb-4",children:e.jsx(z,{columns:l,data:m,loading:x,pagination:u,handlePageChange:b})}),e.jsx($,{isModalOpen:a,onClose:()=>d(!1),editData:s,refreshDataTable:_})]})},ae=i.memo(q);export{ae as default};
