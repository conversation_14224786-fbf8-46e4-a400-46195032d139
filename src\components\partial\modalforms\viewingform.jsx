import React, { useState, memo } from "react";
import { Form, Radio, Space } from "antd";
import FlatButton from "../../shared/button/flatbutton";
import { useViewContext } from "../../../store/viewContext";

const ViewingForm = ({ onCancel }) => {
  const { viewType, setViewType } = useViewContext();
  const [tempViewType, setTempViewType] = useState(viewType);
  const onChange = (e) => {
    setTempViewType(e.target.value);
  };
  // Save changes on form submission
  const onSave = () => {
    setViewType(tempViewType); // Update context state
    onCancel(); // Close the modal (use provided onCancel for modal handling)
  };
  return (
    <Form
      name="filter"
      layout="vertical"
      onFinish={() => setIsViewingModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <Radio.Group onChange={onChange} value={tempViewType}>
        <Space direction="vertical">
          <Radio value={1}>List</Radio>
          {/* <Radio value={2}>Small thumbnails</Radio> */}
          <Radio value={2}>Large Thumbnails </Radio>
        </Space>
      </Radio.Group>
      <div className="text-end mt-5">
        <FlatButton title="Save" className="add-new-btn" onClick={onSave} />
      </div>
    </Form>
  );
};

export default memo(ViewingForm);
