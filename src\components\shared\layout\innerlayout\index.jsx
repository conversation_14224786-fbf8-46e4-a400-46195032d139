import React, { useEffect } from "react";
import { Layout, theme } from "antd";
const { Content } = Layout;
import InnerSideBar from "./innersidebar";
import InnerHeader from "./innerheader";
import { messaging, onMessage } from "../../../../firebase";

const InnerLayout = ({ children }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // Set up Firebase foreground message listener to prevent duplicate notifications
  useEffect(() => {
    let unsubscribe = null;

    if (messaging && !window.firebaseMessageListenerSet) {
      // Mark that we've set up the listener to prevent duplicates
      window.firebaseMessageListenerSet = true;

      unsubscribe = onMessage(messaging, (payload) => {
        console.log("Foreground message received:", payload);
        // Suppress foreground notifications completely to prevent duplicates
        // The service worker will handle all notifications
      });
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
        window.firebaseMessageListenerSet = false;
      }
    };
  }, []);

  return (
    <Layout>
      <InnerSideBar />
      <Layout
        style={{
          marginInlineStart: 200,
        }}
      >
        <InnerHeader />
        <Content
          style={{
            margin: "24px 16px 0",
            overflow: "initial",
          }}
        >
          <div
            style={{
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default InnerLayout;
