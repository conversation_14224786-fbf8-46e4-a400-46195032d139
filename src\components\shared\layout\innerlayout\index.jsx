import React, { useEffect } from "react";
import { Layout, theme } from "antd";
const { Content } = Layout;
import InnerSideBar from "./innersidebar";
import InnerHeader from "./innerheader";
import { messaging, onMessage } from "../../../../firebase";

const InnerLayout = ({ children }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // Set up Firebase foreground message listener to prevent duplicate notifications
  useEffect(() => {
    let unsubscribe = null;

    if (messaging) {
      unsubscribe = onMessage(messaging, (payload) => {
        console.log("Foreground message received:", payload);
        // Only handle foreground notifications when the app is in focus
        // Background notifications are handled by the service worker
        if (document.visibilityState === "visible") {
          // Show notification only when app is visible to prevent duplicates
          if (payload.notification) {
            // You can customize this to show in-app notifications if needed
            console.log(
              "Notification received while app is active:",
              payload.notification
            );
          }
        }
      });
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  return (
    <Layout>
      <InnerSideBar />
      <Layout
        style={{
          marginInlineStart: 200,
        }}
      >
        <InnerHeader />
        <Content
          style={{
            margin: "24px 16px 0",
            overflow: "initial",
          }}
        >
          <div
            style={{
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default InnerLayout;
