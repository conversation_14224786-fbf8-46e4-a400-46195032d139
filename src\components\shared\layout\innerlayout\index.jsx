import React from "react";
import { Layout, theme } from "antd";
const { Content } = Layout;
import InnerSideBar from "./innersidebar";
import InnerHeader from "./innerheader";

const InnerLayout = ({ children }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  return (
    <Layout>
      <InnerSideBar />
      <Layout
        style={{
          marginInlineStart: 200,
        }}
      >
        <InnerHeader />
        <Content
          style={{
            margin: "24px 16px 0",
            overflow: "initial",
          }}
        >
          <div
            style={{
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default InnerLayout;
