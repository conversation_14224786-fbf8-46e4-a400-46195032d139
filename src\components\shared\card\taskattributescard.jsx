import React, { memo, useState } from "react";
import FlatButton from "../button/flatbutton";
import Pdftopicture from "../pdftopicture";
import Slider from "react-slick";
import { message } from "antd";
import { useNavigate } from "react-router-dom";
const ArrowButton = ({ direction, onClick }) => {
  return (
    <button
      className={`custom-arrow-btn custom-arrow-${direction}`}
      onClick={onClick}
    >
      {direction === "prev" ? "<" : ">"}
    </button>
  );
};
const TaskAttributesCard = ({
  children,
  attributes_img,
  id,
  btn_title,
  btn_className,
  postData,
  fetchApi,
  status,
  directories,
  pdfTitle,
}) => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const getNextStatus = () => {
    switch (status) {
      case "pending":
        return "in-process";
      case "in-process":
        return "completed";
      // case "completed":
      //   return "verified";
      default:
        return null; // no update for completed or verified
    }
  };
  const sliderSettings = {
    dots: false,
    arrows: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    adaptiveHeight: true,
    beforeChange: (oldIndex, newIndex) => setCurrentSlide(newIndex),
    prevArrow: directories?.length > 1 && currentSlide > 0 && (
      <ArrowButton direction="prev" />
    ),
    nextArrow: directories?.length > 1 &&
      currentSlide < directories.length - 1 && <ArrowButton direction="next" />,
  };

  const handleClick = async () => {
    const nextStatus = getNextStatus();
    if (!nextStatus) return;

    const fd = new FormData();
    fd.append("status", nextStatus);

    postData(fd, cbSuccess, id);
  };

  const cbSuccess = (res) => {
    if (res?.statusCode === 200) {
      fetchApi();
    }
    if (res?.code == 404) {
      message.error("This content is no longer available.");
      fetchApi();
      navigate("/assign-task");
    }
  };

  return (
    <div className="task-attributes">
      <div className="attributes-header">
        <p>Task Attributes</p>
      </div>
      <div className="attributes-body">
        {directories?.length > 0 && (
          <div className="attributes-slider">
            <Slider {...sliderSettings}>
              {directories.map((dir, index) => (
                <div key={dir._id._id || index} className="slider-item">
                  <div className="attributes-img text-center">
                    <Pdftopicture pdfUrl={dir._id.file} />
                  </div>
                  <p className="font-16 color-light text-center">
                    {dir._id.title}
                  </p>
                  <div className="text-center mt-3">
                    <a
                      href={dir._id.file}
                      title="View PDF"
                      className="view-pdf-btn text-white"
                      target="_blank"
                    >
                      View Pdf
                    </a>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        )}

        {children}

        {getNextStatus() && (
          <div className="text-center mt-3">
            <FlatButton
              title={btn_title}
              onClick={handleClick}
              className={`attributes-btn ${btn_className}`}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(TaskAttributesCard);
