import { Form, Radio, Space } from "antd";
import React, { useState, memo } from "react";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import dayjs from "dayjs";
const AssignFilterForm = ({ projectdetail, onApplyFilter, isFilterRemove }) => {
  const [filterBy, setFilterBy] = useState("");
  const [form] = Form.useForm();
  const handleFilterChange = (e) => {
    setFilterBy(e.target.value);
  };

  const handleApply = () => {
    const values = form.getFieldsValue();
    const filters = {
      filter_by: filterBy || "",
      start_at: values.start_at
        ? dayjs(values.start_at).format("YYYY-MM-DD")
        : "",
      end_at: values.end_at ? dayjs(values.end_at).format("YYYY-MM-DD") : "",
      page: 1,
    };

    onApplyFilter(filters);
  };

  const handleReset = () => {
    form.resetFields();
    setFilterBy("");
    onApplyFilter({
      filter_by: "",
      start_at: "",
      end_at: "",
      page: 1,
    });

    isFilterRemove();
  };

  return (
    <Form
      form={form}
      name="filter"
      layout="vertical"
      onFinish={() => setIsFilterModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      {projectdetail !== "true" ? (
        <p className="font-18 color-black mb-2">Sort By Priority</p>
      ) : (
        ""
      )}
      <Radio.Group onChange={handleFilterChange} value={filterBy}>
        <Space direction="vertical">
          <Radio value="recently_opened">Most recently opened</Radio>
          <Radio value="alphabetically">Alphabetically</Radio>
          <Radio value="asc">Newest to Oldest</Radio>
        </Space>
      </Radio.Group>
      {projectdetail !== "true" ? (
        <div className="row">
          <div className="col-12 col-md-6">
            <BaseInput
              name="start_at"
              type="datepiker"
              placeholder=""
              label="Start Date"
            />
          </div>
          <div className="col-12 col-md-6">
            <BaseInput
              name="end_at"
              type="datepiker"
              placeholder=""
              label="End Date"
            />
          </div>
        </div>
      ) : (
        ""
      )}
      <div className="text-end mt-5">
        <FlatButton title="Reset" className="reset-btn" onClick={handleReset} />
        <FlatButton
          title="Apply"
          className="add-new-btn"
          onClick={handleApply}
        />
      </div>
    </Form>
  );
};

export default memo(AssignFilterForm);
