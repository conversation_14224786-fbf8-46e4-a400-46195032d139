import { Form } from "antd";
import React from "react";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";

const ChangePasswordForm = ({ onCancel }) => {
  const { loading, postData } = useFetch("change_password", { type: "submit" });
  const [form] = Form.useForm();
  const onFinish = (values) => {
    const fd = new FormData();
    for (const key in values) {
      fd.append(key, values[key]);
    }
    postData(fd, cbSuccess);
  };
  const cbSuccess = (res) => {
    if (res.statusCode == 200) {
      form.resetFields();
      onCancel();
    }
  };
  // Password validation rules
  const passwordRules = [
    {
      required: true,
      message: "New Password is required!",
    },
    {
      pattern:
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      message:
        "Password must be at least 8 characters long, contain uppercase, lowercase, a number, and a special character.",
    },
  ];
  return (
    <Form
      name="changepassword"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
      autoComplete="off"
    >
      <BaseInput
        type="password"
        name="old_password"
        id="password"
        placeholder="......"
        label="Current Password"
        rules={[{ required: true, message: "Current Password is required!" }]}
      />
      <BaseInput
        type="password"
        name="new_password"
        id="password"
        placeholder="......"
        label="New Password"
        rules={passwordRules}
      />
      <BaseInput
        type="password"
        name="confirmPassword"
        id="confirmPassword"
        placeholder="......."
        label="Confirm Password"
        dependencies={["new_password"]}
        rules={[
          {
            required: true,
            message: "Confirm Password is required!",
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue("new_password") === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error("Passwords do not match!"));
            },
          }),
        ]}
      />
      <div className="text-end mt-4">
        <FlatButton
          title="Submit"
          className="add-new-btn"
          htmlType="submit"
          loading={loading}
        />
      </div>
    </Form>
  );
};

export default ChangePasswordForm;
