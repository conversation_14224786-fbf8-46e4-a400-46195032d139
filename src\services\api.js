const api = {
  create_company: {
    method: `POST`,
    url: `user`,
  },
  login: {
    method: `POST`,
    url: `user/login`,
  },
  update_profile: {
    method: `PATCH`,
    url: `user`,
  },
  get_profile: {
    method: `GET`,
    url: `user`,
  },
  forgot_password: {
    method: `POST`,
    url: `user/forgot-password`,
  },
  change_password: {
    method: `POST`,
    url: `user/change-password`,
  },
  create_user_type: {
    method: `POST`,
    url: `user-type`,
  },
  user_type: {
    method: `GET`,
    url: `user-type`,
  },
  edit_user_type: {
    method: `PATCH`,
    url: `user-type`,
  },
  delete_user_type: {
    method: `DELETE`,
    url: `user-type`,
  },
  employee: {
    method: `GET`,
    url: `user`,
  },
  create_employee: {
    method: `POST`,
    url: `user/create-employee`,
  },
  edit_employee: {
    method: `PATCH`,
    url: `user`,
  },
  delete_employee: {
    method: `DELETE`,
    url: `user`,
  },
  create_project: {
    method: `POST`,
    url: `projects`,
  },
  project: {
    method: `GET`,
    url: `projects`,
  },
  update_project: {
    method: `PATCH`,
    url: `projects`,
  },
  directories: {
    method: `GET`,
    url: `directories`,
  },
  create_directories: {
    method: `POST`,
    url: `directories`,
  },
  delete_directories: {
    method: `DELETE`,
    url: `directories`,
  },
  get_password: {
    method: `GET`,
    url: `user/decrypt-default-password`,
  },
  reset_password: {
    method: `POST`,
    url: `user/reset-employee-password`,
  },
  create_task: {
    method: `POST`,
    url: `tasks`,
  },
  task: {
    method: `GET`,
    url: `tasks`,
  },
  update_task: {
    method: `PATCH`,
    url: `tasks`,
  },
  delete_task: {
    method: `DELETE`,
    url: `tasks`,
  },
  upload_media: {
    method: `POST`,
    url: `upload/media`,
  },
  upload_multiple_files: {
    method: `POST`,
    url: `directories/bulk-create`,
  },
  remove_device_token: {
    method: `POST`,
    url: `user/remove-device-token`,
  },
  notification: {
    method: `GET`,
    url: `notification`,
  },
  update_notification: {
    method: `PATCH`,
    url: `notification`,
  },
};

export default api;
