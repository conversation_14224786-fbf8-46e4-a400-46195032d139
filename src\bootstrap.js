import lodash from "lodash";
import constants from "./config/constants";
import helper from "./helpers";
import _ from "lodash";
import { io } from "socket.io-client";
function bootstrap() {
  window.lodash = lodash;
  window.constants = constants;
  window.helper = helper;
  window.user = window.helper.getStorageData("session");
  if (!_.isEmpty(window.user)) {
    const socket = io(window.constants.node_chat_url, {
      transports: ["websocket"],
      query: {
        token: window.user?.access_token,
      },
    });

    window.socket = socket;
  }
}

export default bootstrap;
