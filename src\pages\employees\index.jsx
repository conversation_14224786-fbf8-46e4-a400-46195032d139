import React, { memo, useState, useCallback, useEffect } from "react";
import { useFetch } from "../../hooks";
import CustomDropdown from "../../components/shared/dropdown";
import useSweetAlert from "../../hooks/useSweetAlert";
import { debounce } from "lodash";
import Userlayout from "../../components/shared/userlayout";
import UserRoleTable from "../../components/shared/userroletable";
import EmplpoyeeModal from "./employeemodal";

const Employees = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [searchValue, setSearchValue] = useState(""); // Search state
  const {
    loading,
    data = [],
    fetchApi,
    pagination,
    setQueryParams,
  } = useFetch("employee", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 10 },
  });
  const { postData: deleteEmployee } = useFetch("delete_employee", {
    type: "submit",
  });
  const { postData: resetEmployeePassword } = useFetch("reset_password", {
    type: "submit",
  });
  const { showAlert } = useSweetAlert();

  const handleDelete = async (id, e) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteEmployee("", cbSuccess, id);
    }
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) fetchApi();
  };

  const handleEdit = (data) => {
    setEditData(data);
    setIsModalOpen(true);
  };

  const handleAddNew = () => {
    setEditData(null);
    setIsModalOpen(true);
  };

  const handleResetPassword = async (id, e) => {
    const fd = new FormData();
    fd.append("user_id", id);
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to reset password?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      resetEmployeePassword(fd, cbSuccess);
    }
  };

  const ColumnsEmployees = [
    {
      title: "User Name",
      dataIndex: "name",
      sorter: (a, b) => a.name?.length - b.name?.length,
    },
    { title: "Email Address", dataIndex: "email" },
    {
      title: "User Type",
      dataIndex: "usertype",
      render: (text, data) => data?.user_type?.title,
    },
    {
      title: "Action",
      dataIndex: "action",
      render: (text, data) => (
        <CustomDropdown
          title="Action"
          icon="true"
          className="table-dropdown"
          items={[
            {
              label: (
                <p className="color-blue" onClick={() => handleEdit(data)}>
                  Edit
                </p>
              ),
              key: "0",
            },
            {
              label: (
                <p
                  className="color-green"
                  onClick={() => handleResetPassword(data._id)}
                >
                  Reset password
                </p>
              ),
              key: "1",
            },
            {
              label: (
                <p
                  className="color-red"
                  onClick={(e) => handleDelete(data._id, e)}
                >
                  Delete
                </p>
              ),
              key: "2",
            },
          ]}
        />
      ),
    },
  ];

  const debouncedSearch = useCallback(
    debounce((value) => {
      setQueryParams({
        page: 1,
        limit: 10,
        keyword: value,
      });
    }, 500),
    [setQueryParams]
  );

  const handleSearch = (e) => {
    const value = e.target.value;
    debouncedSearch(value);
  };

  const handlePageChange = (page, pageSize) => {
    setQueryParams({ page, limit: pageSize, keyword: searchValue });
  };

  return (
    <Userlayout
      handleSearch={handleSearch}
      handleAddNew={handleAddNew}
      title="Employees"
    >
      <div className="detail-table mt-4 mb-4">
        <UserRoleTable
          columns={ColumnsEmployees}
          data={data}
          loading={loading}
          pagination={pagination}
          handlePageChange={handlePageChange}
        />
      </div>
      <EmplpoyeeModal
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        editData={editData}
        refreshDataTable={fetchApi}
      />
    </Userlayout>
  );
};

export default memo(Employees);
