import React, { memo, useEffect, useState } from "react";
import { Form, Switch, Skeleton } from "antd";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";

const NOTIFICATION_SETTINGS = [
  {
    key: "notify_on_new_job_assignment",
    label: "When someone assigned you a new job",
  },
  {
    key: "notify_on_task_start",
    label: "When task started",
  },
  {
    key: "notify_on_task_end",
    label: "When task ended",
  },
  {
    key: "notify_on_project_assignment",
    label: "When someone assigned you a project",
  },
  {
    key: "notify_on_drawing_revision",
    label: "When drawing is updated/revision",
  },
  {
    key: "notify_on_task_overdue",
    label: "When a task is overdue",
  },
];

const NotificationSettingForm = ({ onCancel }) => {
  let user = window.user.user;
  const [settings, setSettings] = useState({});
  const [initialSettings, setInitialSettings] = useState({});
  const { loading: loader, data } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });

  const { loading, postData } = useFetch("update_profile", { type: "submit" });

  useEffect(() => {
    if (data) {
      const notificationSettings = NOTIFICATION_SETTINGS.reduce(
        (acc, { key }) => {
          acc[key] = data[key] || false;
          return acc;
        },
        {}
      );
      setSettings(notificationSettings);
      setInitialSettings(notificationSettings);
    }
  }, [data]);

  const handleChange = (key) => (checked) => {
    setSettings((prev) => ({
      ...prev,
      [key]: checked,
    }));
  };
  const onFinish = () => {
    const fd = new FormData();
    NOTIFICATION_SETTINGS.forEach(({ key }) => {
      fd.append(key, settings[key]);
    });
    postData(fd, cbSuccess, user._id);
  };
  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      window.user.user = res.data;
    }
  };
  const renderContent = () => {
    if (loader || !data) {
      return (
        <>
          {[...Array(6)].map((_, index) => (
            <div
              className="d-flex align-items-center justify-content-between mt-3"
              key={index}
            >
              <div style={{ width: "60%" }}>
                <Skeleton.Input active size="small" style={{ width: "100%" }} />
              </div>
              <div>
                <Skeleton.Button active size="small" style={{ width: 40 }} />
              </div>
            </div>
          ))}
        </>
      );
    }

    return (
      <>
        {NOTIFICATION_SETTINGS.map(({ key, label }) => (
          <div
            className="d-flex align-items-center justify-content-between mt-3"
            key={key}
          >
            <div>
              <p className="font-16">{label}</p>
            </div>
            <div>
              <Switch
                checked={settings[key]}
                onChange={handleChange(key)}
                loading={loading}
              />
            </div>
          </div>
        ))}
      </>
    );
  };
  return (
    <Form
      name="notification-settings"
      layout="vertical"
      initialValues={{ remember: true }}
      autoComplete="off"
      onFinish={onFinish}
    >
      {renderContent()}

      <div className="text-end mt-5">
        <FlatButton
          title="Save"
          className="add-new-btn"
          htmlType="submit"
          loading={loading || loader}
          disabled={loader || !data}
        />
      </div>
    </Form>
  );
};

export default memo(NotificationSettingForm);
