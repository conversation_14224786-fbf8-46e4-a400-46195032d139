.dot{
    width: 6px;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    margin: 0 7px !important;
}
.filter-btn{
    background-color: #8d96a7;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;

}
.project-header .project-header-img{
    position: relative;
    height: 300px;
    overflow: hidden;
    border-top-left-radius: 14px;
    border-top-right-radius: 14px;
}
.project-header .project-header-img img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-left-radius: 14px;
    border-top-right-radius: 14px;
}
.project-header-detail{
width: 100%;
position: absolute;
bottom: 0;
background-color: rgba(1, 1, 14, 0.767);
backdrop-filter: blur(2px);
padding: 9px 12px;
}
.project-body{
    background-color: #fff;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    padding: 9px 12px;
}
.project-card{
    border-radius: 14px;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
}
.ant-dropdown {
    width: 300px;
    margin-right: 20px;
    margin-top: 9px;
}

.ant-dropdown-menu-title-content {
    white-space: normal;
}