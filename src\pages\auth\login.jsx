import React, { memo } from "react";
import AuthLayout from "../../components/shared/layout/authlayout";
import BaseInput from "../../components/shared/inputs/index";
import FlatButton from "../../components/shared/button/flatbutton";
import { Form } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import "./auth.css";
import { useFetch } from "../../hooks";
import { create_company } from "../../config/rules";
import { requestPermissionAndGetToken } from "../../helpers/notification";
const Login = () => {
  const navigate = useNavigate();
  let { role } = useParams();
  const { loading, postData } = useFetch("login", { type: "submit" });
  const onFinish = async (values) => {
    let deviceToken = await requestPermissionAndGetToken();
    deviceToken = `web|${deviceToken}`;
    const fd = new FormData();
    fd.append("device", "web");
    fd.append("device_token", deviceToken);
    fd.append("role", role);
    for (const key in values) {
      fd.append(key, values[key]);
    }
    postData(fd, cbSuccess);
  };
  const cbSuccess = (res) => {
    if (res.statusCode == 200) {
      window.location.href = "/projects";
    }
  };

  return (
    <AuthLayout
      title="Welcome back"
      detail="Welcome back! Please enter your details"
    >
      <Form
        name="login"
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          remember: true,
        }}
      >
        <BaseInput
          name="email"
          placeholder="Email"
          label="Email Address"
          rules={create_company.email}
        />
        <BaseInput
          type="password"
          name="password"
          id="password"
          placeholder="Password"
          label="Password"
          rules={[{ required: true, message: "Password is required!" }]}
        />
        <div className="d-flex align-items-center justify-content-end mt-0">
          <Link
            to="/forget-password"
            className="link-color mt-1 font-600 font-14 "
          >
            Forgot Password?
          </Link>
        </div>
        <div>
          <FlatButton
            title="Sign In"
            className="mx-auto mt-4 signin-btn mt-5"
            htmlType="submit"
            loading={loading}
          />
        </div>
        {role && role == "company" && (
          <div>
            <p className="signup-text">
              Don’t have an account?
              <Link to="/sign-up" className="color-blue font-600 font-16 ms-1">
                Sign up
              </Link>
            </p>
          </div>
        )}
      </Form>
    </AuthLayout>
  );
};

export default memo(Login);
