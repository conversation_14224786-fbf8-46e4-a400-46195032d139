"use strict";
import axios from "axios";
import _ from "lodash";
import useSweetAlert from "../hooks/useSweetAlert";
import cacheService from "./cacheService";

class HttpRequest {
  // Non-cacheable endpoints (you can customize this list)
  static nonCacheableEndpoints = [
    "/api/user/login",
    "/api/user/logout",
    "/api/user/register",
  ];

  static getTimeZone() {
    return Intl.DateTimeFormat().resolvedOptions().timeZone; // Get the user's timezone dynamically
  }
  static setHeaders(customHeaders = {}) {
    const defaultHeaders = {
      timezone: this.getTimeZone(),
    };
    const authUser = window.helper.getStorageData("session");
    if (authUser) {
      defaultHeaders["authorization"] = `Bearer ${authUser.access_token}`;
    }

    return { ...defaultHeaders, ...customHeaders };
  }

  static shouldCache(method, url) {
    // Only cache GET requests
    if (method.toLowerCase() !== "get") return false;

    // Don't cache endpoints in nonCacheableEndpoints
    return !this.nonCacheableEndpoints.some((endpoint) =>
      url.includes(endpoint)
    );
  }

  static invalidateCacheAfterMutation(method, url) {
    // Extract resource type from URL
    const urlParts = url.split("/");
    const resourceIndex = urlParts.findIndex((part) => part === "api") + 1;
    if (resourceIndex < urlParts.length) {
      const resource = urlParts[resourceIndex];
      // Invalidate cache for the affected resource
      cacheService.invalidateResource(resource);
    }
  }

  static handleMutationResponse(method, url, response) {
    if (["post", "put", "patch", "delete"].includes(method.toLowerCase())) {
      const resource = cacheService.getResourceFromUrl(url);
      if (resource) {
        // Invalidate both the specific resource and its related resources
        cacheService.invalidateRelatedResources(resource);
      }
    }
    return response;
  }

  static async makeRequest(method, url, params, headers = {}, config = {}) {
    if (!_.isEmpty(headers["Content-Type"])) {
      params = this.setParams(headers["Content-Type"], params);
    }

    const shouldUseCache = this.shouldCache(method, url) && !config.noCache;

    if (shouldUseCache) {
      const cacheKey = cacheService.generateKey(method, url, params);
      const cachedResponse = cacheService.get(cacheKey);

      if (cachedResponse) {
        return { code: 200, data: cachedResponse, fromCache: true };
      }
    }

    const requestConfig = {
      method,
      url,
      headers: this.setHeaders(headers),
      data: params,
      ...config,
    };

    try {
      const response = await axios(requestConfig);

      // Handle login response
      if (url.includes("/api/user/login") && response.headers["access_token"]) {
        const accessToken = response.headers["access_token"];
        const userObject = response.data.data; // Adjust this based on your response structure
        // Combine access token and user object into a single object
        const sessionData = {
          access_token: accessToken,
          user: userObject,
        };
        // Save the session data object in local storage
        window.helper.setStorageData("session", sessionData);
      }

      // Cache the response if it's cacheable
      if (shouldUseCache && response.data) {
        const cacheKey = cacheService.generateKey(method, url, params);
        cacheService.set(cacheKey, response.data, config.cacheTTL);
      }

      // Handle cache invalidation for mutations
      return this.handleMutationResponse(method, url, {
        code: 200,
        data: response.data,
      });
    } catch (err) {
      return this.handleRequestError(err);
    }
  }

  static setParams(contentType, params) {
    if (contentType.includes("application/json")) {
      return JSON.stringify(params);
    }
    return params;
  }

  static async handleRequestError(err) {
    if (err.response?.status === 401) {
      await this.handleUnauthorizedError();
      return;
    }
    const errorResponse = {
      code: err.response?.status || 400, // Use actual status code instead of hardcoding 400
      message: err.message,
      data: err.response?.data?.message || err.response?.data,
    };

    return errorResponse;
  }

  static async handleUnauthorizedError() {
    const { showAlert } = useSweetAlert();

    const result = await showAlert({
      title: "Alert!",
      text: "Your session has expired. Kindly log in to continue.",
      icon: "error",
      background: "#232323",
      showCancelButton: false,
      confirmButtonText: "Yes",
    });

    if (result.isConfirmed) {
      localStorage.clear();
      window.location.href = "/";
    }
  }

  static makeQueryStringUrl(url, formData) {
    const queryString = new URLSearchParams(formData).toString();
    return `${url}?${queryString}`;
  }
}

export default HttpRequest;
