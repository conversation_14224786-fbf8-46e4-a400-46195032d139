
.demo-logo-vertical .logo-text{
    margin-left: 8px;
}
.demo-logo-vertical .logo-text h2{
    font-size: 18px;
    text-transform:uppercase ;
}
.demo-logo-vertical .logo-text p{
    font-size: 9px;
    white-space: nowrap;
}
.demo-logo-vertical{
    margin-top: 15px;
    padding: 10px;
    margin-bottom: 20px;
}
aside.ant-layout-sider{
    overflow: auto;
    height: 100vh;
    position: fixed;
    inset-inline-start: 0px;
    top: 0px;
    bottom: 0px;
    scrollbar-width: thin;
    scrollbar-color: unset;
    flex: 0 0 240px !important;
    max-width: 240px !important;
    min-width: 240px !important;
    width: 240px !important;

}
.ant-layout {
    margin-inline-start: 120px  !important;
} 
.header-profile{
    width: 65px;
    height: 65px;
    overflow: hidden;
    border-radius: 50%;
    margin: 0 auto;
}
.header-profile img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}
.ant-layout-header {
    text-align: end !important;
    padding-right: 20px;
}

.ant-layout-sider{
    background-color: #fff !important;
}
.ant-menu{
    background-color: #fff !important;
   
}
.ant-menu-item {
    color: #3a3a3c !important;
    font-size: 16px !important;
    padding: 4px 12px !important;
    margin-bottom: 20px !important;
}
.ant-menu-item-selected{
    background-color: transparent !important;
}
.ant-menu-item-selected a{
    color: #4893ca !important;
}

.ant-layout-header>div {
    position: relative;
}

.ant-layout-header>div::before{
    content: "";
    background-color: rgba(141, 150, 167, 0.4);
    position: absolute;
    top: 20%;
    width: 1px;
    height: 31px;
    right: 4%;
}
.content-header{
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.content-header h2{
    font-size: 26px;
    color: #000124;
}
.content-header-child{
    display: flex;
    align-items: center;
}
.content-header-child .ant-form-item{
    margin: 0 !important;
    background-color: transparent !important;
}
.content-header-child .ant-form-item input{
    background-color: transparent !important;
    border-color: #e2e6ed;
}

.ant-avatar{
    background-color: transparent;
}
.avatar-img .ant-avatar-string{
    width: 36px;
    height: 36px;
    overflow: hidden;
    border-radius: 50%;
}
.avatar-img .ant-avatar-string img{
    width: 100%;
    height: 100%;
    object-fit: cover !important;
    border-radius: 50%;
}
.ant-table-thead th{
    background-color: #4893ca !important;
    color: #fff !important;
}
.ant-table-thead th::before{
    content: none !important;
}
.ant-table-tbody td{
    color: #8d96a7 !important;
}
.ant-table-content{
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
}


.static-box{
  border-radius: 12px;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 15px;
}
.static-box p{
    color:#1b1b1f;
    margin-bottom: 20px !important;
}




.ant-layout-header>div::before {
    right: 7%;
    top: 30%;
}


.ant-switch-checked {
    background-color: #4893ca !important;
 }
 .ant-picker-suffix {
    color: grey !important;
}

.anticon-down.ant-select-suffix {
    color: grey;
}
/* .assign-box{
    margin-bottom: 40px !important;
} */
.ant-upload.ant-upload-select {
    order: 1;
}

.ant-upload-list-item-container {
    order: 2;
}