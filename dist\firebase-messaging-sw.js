// Scripts for firebase and firebase messaging
importScripts(
  "https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"
);

// Initialize the Firebase app in the service worker by passing the generated config
const firebaseConfig = {
  apiKey: "AIzaSyAmfpLYUfR_BMmfnvHWyLcuok1l91dKjSs",
  authDomain: "constructified-36ca7.firebaseapp.com",
  projectId: "constructified-36ca7",
  storageBucket: "constructified-36ca7.firebasestorage.app",
  messagingSenderId: "849059059930",
  appId: "1:849059059930:web:746c353ba23b6c46032de3",
  measurementId: "G-BRDNB20VR2",
};

firebase.initializeApp(firebaseConfig);

// Retrieve firebase messaging
const messaging = firebase.messaging();
let notification_data = {};
messaging.onBackgroundMessage(function (payload) {
  notification_data = payload;
  let notificationTitle = payload.notification.title;
  let notificationOptions = {
    body: payload.notification.body,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

////Code for adding event on click of notification
self.addEventListener("notificationclick", function (event) {
  let custom_data = JSON.parse(notification_data.data.custom_data);
  const routes = {
    projects: `/projects/${custom_data.reference_id}`,
    main_directories: `/projects/${custom_data.project_id}`,
    sub_directories: `/directories/${custom_data.project_id}/${custom_data.parent_id}`,
    directories_detail: `/directories-detail/${custom_data.project_id}/${custom_data.parent_id}`,
    tasks: `/assign-task/${custom_data.reference_id}`,
    chat: `/assign-task/${custom_data.reference_id}`,
  };

  const urlToRedirect = routes[custom_data.module] || "/projects";
  event.notification.close();
  event.waitUntil(self.clients.openWindow(urlToRedirect));
});
