import React, { useState, useCallback, memo } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import BaseInput from "../../components/shared/inputs";
import FlatButton from "../../components/shared/button/flatbutton";
import ProjectCard from "../../components/shared/card/projectcard";
import CustomModal from "../../components/shared/modal";
import PageTitle from "../../components/shared/pagetitle";
import CreateProjectForm from "../../components/partial/modalforms/createprojectform";
import ProjectFilterForm from "../../components/partial/modalforms/projectfilterform";
import { Skeleton, Empty, Pagination } from "antd";
import { useFetch } from "../../hooks";
import Update from "./update";
import "./projects.css";

const MemoizedProjectCard = memo(ProjectCard);

const Projects = () => {
  const user = window.user?.user;
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({});
  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const { loading, data, fetchApi, pagination, setQueryParams } = useFetch(
    "project",
    {
      enablePagination: true,
      defaultQueryParams: { page: 1, limit: 9 },
    }
  );
  const handleFilterApply = (formData) => {
    const queryParams = {
      address: formData.address || "",
      start_at: formData.start_at || "",
      members: formData.members || [],
      page: 1,
      limit: pagination?.perPage || 9,
    };

    setFilters(queryParams);
    setQueryParams(queryParams);
    setIsFilterApplied(true);
    setIsFilterModalOpen(false);
  };
  const openCreateModal = useCallback(() => {
    setIsEditMode(false);
    setSelectedProject(null);
    setIsModalOpen(true);
  }, []);

  const openEditModal = useCallback((project) => {
    setIsEditMode(true);
    setSelectedProject(project);
    setIsModalOpen(true);
  }, []);

  const handlePageChange = (page, pageSize) => {
    setQueryParams({
      ...filters,
      page,
      limit: pageSize,
      keyword: searchTerm,
    });
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    setQueryParams({
      ...filters,
      page: 1,
      limit: pagination?.perPage || 9,
      keyword: value,
    });
  };
  return (
    <>
      <InnerLayout>
        <PageTitle
          title="Projects"
          buttons={
            <>
              <div>
                <BaseInput
                  name="search"
                  placeholder="Search projects"
                  icon={<img src="/admin/assets/img/search-icon.png" />}
                  value={searchTerm}
                  onChange={handleSearchChange}
                  style={{
                    borderRadius: "8px",
                    padding: "10px 20px",
                    width: "300px",
                    border: "1px solid #ddd",
                  }}
                />
              </div>

              <FlatButton
                title={
                  <>
                    Filter
                    {isFilterApplied && <span className="filter-dot"></span>}
                  </>
                }
                className="mx-auto filter-btn"
                onClick={() => setIsFilterModalOpen(true)}
              />

              {(userObj?.role === "company" ||
                userObj?.policies?.some(
                  (policy) => policy.module === "project" && policy.can_create
                )) && (
                <FlatButton
                  title="+ Add New"
                  className="mx-auto add-new-btn"
                  onClick={openCreateModal}
                />
              )}
            </>
          }
        />

        <div className="row mb-5">
          {loading ? (
            Array.from({ length: 6 }).map((_, index) => (
              <div
                className="col-12 col-md-6 col-lg-6 col-xl-4 mt-4"
                key={index}
              >
                <Skeleton active />
              </div>
            ))
          ) : data?.length ? (
            data.map((item, index) => (
              <div
                className="col-12 col-md-6 col-lg-6 col-xl-4 mt-4"
                key={index}
              >
                <MemoizedProjectCard
                  {...item}
                  onEdit={() => openEditModal(item)}
                />
              </div>
            ))
          ) : (
            <div className="col-12 mt-5">
              <Empty description="No Projects Found" />
            </div>
          )}
        </div>

        <div className="custom-pagination d-flex justify-content-end my-5">
          <Pagination
            current={pagination?.currentPage || 1}
            total={pagination?.count || 0}
            pageSize={pagination?.perPage || 9}
            pageSizeOptions={["9", "10", "20", "50", "100"]}
            onChange={handlePageChange}
          />
        </div>
      </InnerLayout>
      {(userObj?.role === "company" ||
        userObj?.policies?.some(
          (policy) => policy.module === "project" && policy.can_create
        )) && (
        <CustomModal
          title={isEditMode ? "Edit Project" : "Create Project"}
          onCancel={() => setIsModalOpen(false)}
          open={isModalOpen}
          className="custom-modal"
          footer={false}
        >
          {isEditMode ? (
            <Update
              project={selectedProject}
              onCancel={() => setIsModalOpen(false)}
              onSuccess={fetchApi}
            />
          ) : (
            <CreateProjectForm
              onCancel={() => setIsModalOpen(false)}
              onSuccess={fetchApi}
            />
          )}
        </CustomModal>
      )}

      <CustomModal
        title="Filters"
        onCancel={() => setIsFilterModalOpen(false)}
        open={isFilterModalOpen}
        className="custom-modal"
        footer={false}
      >
        <ProjectFilterForm
          onFilterApply={handleFilterApply}
          onCancel={() => setIsFilterModalOpen(false)}
          isFilterRemove={() => setIsFilterApplied(false)}
          setFilters={setFilters}
        />
      </CustomModal>
    </>
  );
};

export default memo(Projects);
