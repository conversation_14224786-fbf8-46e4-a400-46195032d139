import React, { useState } from "react";
import useSweetAlert from "../../../hooks/useSweetAlert";
import { useFetch } from "../../../hooks";
import { useNavigate } from "react-router-dom";
import CustomModal from "../modal";
import UpdateTask from "../../../pages/assigntask/updatetask";

const ChatHeader = ({ data, fetchApi }) => {
  const navigate = useNavigate();
  const user = window.user?.user;
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { postData: deleteTask } = useFetch("delete_task", {
    type: "submit",
  });
  const { showAlert } = useSweetAlert();
  const handleDelete = async (id, e) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteTask("", cbSuccess, id);
    }
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      navigate("/assign-task");
    }
  };

  return (
    <React.Fragment>
      <div className="chat-header d-flex justify-content-between">
        <div className="d-flex align-items-center">
          <div
            className={
              `assign-user-avatar ` +
              (data?.status === "in-process"
                ? "avatar-process"
                : data?.status === "completed"
                ? "avatar-complete"
                : data?.status === "verified"
                ? "avatar-verified"
                : "")
            }
          >
            <p>{window.helper.getInitials(data?.title)}</p>
          </div>
          <div>
            <p className="font-12 color-light">
              ID: {data?.short_id} | {data?.project?.title}
            </p>
            <p className="font-14 color-black">{data?.title}</p>
          </div>
        </div>
        {(userObj?.role === "company" ||
          userObj?.policies?.some(
            (policy) => policy.module === "task" && policy.can_create
          )) && (
          <div className="d-flex align-items-center">
            <img
              className="me-4 cursor-pointer"
              src="/admin/assets/img/chat-delete-icon.png"
              alt="Delete"
              onClick={(e) => handleDelete(data._id, e)}
            />
            <img
              className="cursor-pointer"
              src="/admin/assets/img/chat-edit-icon.png"
              alt="Edit"
              onClick={(e) => setIsEditModalOpen(true)}
            />
          </div>
        )}
        {/* {canEditAndDelete && (
          <div className="d-flex align-items-center">
            <img
              className="me-4 cursor-pointer"
              src="/admin/assets/img/chat-delete-icon.png"
              alt="Delete"
              onClick={(e) => handleDelete(data._id, e)}
            />
            <img
              className="cursor-pointer"
              src="/admin/assets/img/chat-edit-icon.png"
              alt="Edit"
              onClick={(e) => setIsEditModalOpen(true)}
            />
          </div>
        )} */}
      </div>
      <CustomModal
        width={900}
        title="Edit Task"
        onCancel={(e) => setIsEditModalOpen(false)}
        open={isEditModalOpen}
        className="custom-modal"
        footer={false}
      >
        <UpdateTask
          onClose={(e) => setIsEditModalOpen(false)}
          taskData={data}
          fetchApi={fetchApi}
        />
      </CustomModal>
    </React.Fragment>
  );
};

export default ChatHeader;
