{"name": "constructify", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@compdfkit_pdf_sdk/webviewer": "^2.5.2", "antd": "^5.20.1", "antd-img-crop": "^4.23.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "crypto-js": "^4.2.0", "firebase": "^11.8.1", "lodash": "^4.17.21", "luxon": "^3.5.0", "moment": "^2.30.1", "pdfjs-dist": "^4.9.155", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-phone-number-input": "^3.4.8", "react-router-dom": "^6.26.0", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.0", "sweetalert2": "^11.14.1"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.8.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.0"}}