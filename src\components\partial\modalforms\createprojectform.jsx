import { Form } from "antd";
import React, { useState, memo } from "react";
import BaseInput from "../../shared/inputs";
import CustomUpload from "../../shared/upload";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { create_project } from "../../../config/rules";
import moment from "moment";
import dayjs from "dayjs";
const CreateProjectForm = ({ onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const { loading, postData } = useFetch("create_project", { type: "submit" });
  const { data } = useFetch("employee", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 2000 },
  });
  const [uploadedImages, setUploadedImages] = useState([]);
  const [completionDate, setCompletionDate] = useState("");

  const [resetImages, setResetImages] = useState(false);
  const onFinish = (values) => {
    const now = moment();
    const fd = new FormData();
    setResetImages(true);
    fd.append("country", "United States");
    fd.append("city", "Central Manchester");
    fd.append("state", "Connecticut");
    fd.append("latitude", "41.782211");
    fd.append("longitude", "-72.532566");
    fd.append("start_at", now.format("YYYY-MM-DD"));
    fd.append("start_time_at", now.format("HH:mm"));
    fd.append("completion_at", completionDate);

    if (values.members?.length) {
      values.members.forEach((memberId) => {
        fd.append("members[]", memberId);
      });
    }

    uploadedImages.forEach((image, index) => {
      fd.append(`image`, image);
    });

    Object.entries(values).forEach(([key, value]) => {
      if (key !== "members" && key !== "image" && key !== "completion_at") {
        fd.append(key, value);
      }
    });

    postData(fd, cbSuccess);
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      form.resetFields();
      onSuccess();
      setTimeout(() => setResetImages(false), 0);
    }
  };

  return (
    <Form
      name="create-project"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
      autoComplete="off"
    >
      <BaseInput
        name="title"
        placeholder=""
        label="Project Name"
        rules={create_project.title}
      />
      <BaseInput
        name="members"
        mode="multiple"
        type="select"
        placeholder=""
        label="Assignee Members"
        options={data?.map((item) => ({
          value: item._id,
          label: item.name,
        }))}
        rules={create_project.members}
      />
      <BaseInput
        name="address"
        placeholder=""
        label="Location"
        rules={create_project.address}
      />
      <BaseInput
        name="completion_at"
        type="datepiker"
        placeholder=""
        label="Completion Date"
        rules={create_project.completion_at}
        disablePastDates={true}
        format="YYYY-MM-DD"
        onChange={(date) => {
          const formattedDate = dayjs(date).format("YYYY-MM-DD");
          setCompletionDate(formattedDate);
        }}
      />
      <BaseInput
        name="type"
        placeholder=""
        label="Type (Ex: Pre-Construction)"
        rules={[{ required: true, message: "Type is required!" }]}
      />
      <label className="color-black font-600 mt-3 mb-1">Add Images</label>
      <CustomUpload
        maxFiles={10}
        onChange={(images) => setUploadedImages(images)}
        value={uploadedImages}
        resetImages={resetImages}
        allowedTypes={["image/jpeg", "image/jpg", "image/png"]}
      />
      <div className="text-end mt-4">
        <FlatButton
          title="Save"
          className="add-new-btn"
          loading={loading}
          htmlType="submit"
        />
      </div>
    </Form>
  );
};

export default memo(CreateProjectForm);
