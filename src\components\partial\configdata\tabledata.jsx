// src/components/shared/table/tableData.js

import { Link } from "react-router-dom";
import CustomDropdown from "../../shared/dropdown";
import { DateTime } from "luxon";
import moment from "moment";
// Dropdown items
const userRole = window.user?.user?.role;
export const items = [
  {
    label: (
      <a href="#" className="color-blue">
        Edit
      </a>
    ),
    key: "0",
  },
  {
    label: (
      <a href="#" className="color-red">
        Delete
      </a>
    ),
    key: "1",
  },
];

// Common image
const folderIcon = <img src="/admin/assets/img/folder-icon.png" alt="" />;
const deleteIcon = <img src="/admin/assets/img/table-delete-icon.png" alt="" />;
const pdfIcon = <img src="/admin/assets/img/pdf-icon.png" alt="" />;
const employeeImg = <img src="/admin/assets/img/table-img.png" alt="" />;

// Common render for names with images
const renderName = (text, icon) => (
  <div className="d-flex align-items-center">
    <div>{icon}</div>
    <div className="ms-2 color-dark-blue ">{text}</div>
  </div>
);

// ];
export const ColumnsProjectDetail = (deleteRow, viewType, userObj) => [
  {
    title: "Name",
    dataIndex: "title",
    render: (text, record) => (
      <div
        className={`d-flex align-items-center ${
          viewType === 1 ? `list-folder` : `large-folder`
        }`}
      >
        <img
          src="/admin/assets/img/folder-icon.png"
          alt={record.title}
          style={{ marginRight: 8 }}
        />
        <Link
          to={`/directories/${record.project._id}/${record._id}`}
          state={{ record }}
        >
          {record.title}
        </Link>
      </div>
    ),
    sorter: (a, b) => a.title.length - b.title.length,
    width: "45%",
  },
  {
    title: "Last Updated",
    dataIndex: "created_at",
    render: (text, record) => moment(text).format("MMM D, YYYY, h:mm A"),
    width: "30%",
  },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  {
    title: "",
    dataIndex: "icon",
    render: (text, record) =>
      (userObj?.role === "company" ||
        (userObj?._id === record?.project?.user?._id &&
          userObj?.policies?.some(
            (policy) => policy.module === "project" && policy.can_create
          ))) && (
        <div className="cursor-pointer" onClick={() => deleteRow(record._id)}>
          <img src="/admin/assets/img/table-delete-icon.png" alt="" />
        </div>
      ),
    align: "center",
    width: "5%",
  },
];

// Columns and Data for Categories
export const ColumnsDirectoriesDetail = (deleteRow, userObj) => [
  {
    title: "Name",
    dataIndex: "title",
    render: (text, record) => (
      <div className="d-flex align-items-center">
        <img
          src="/admin/assets/img/folder-icon.png"
          alt={record.title}
          style={{ marginRight: 8 }}
        />
        <Link
          to={`/directories-detail/${record.project._id}/${record._id}`}
          state={{ record }}
        >
          {record.title}
        </Link>
      </div>
    ),
    sorter: (a, b) => a.title.length - b.title.length,
    width: "45%",
  },
  {
    title: "Last Updated",
    dataIndex: "created_at",
    render: (text, record) => moment(text).format("MMM D, YYYY, h:mm A"),
    width: "30%",
  },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  {
    title: "",
    dataIndex: "icon",
    render: (text, record) =>
      (userObj?.role === "company" ||
        (userObj?._id === record?.project?.user?._id &&
          userObj?.policies?.some(
            (policy) => policy.module === "project" && policy.can_create
          ))) && (
        <div className="cursor-pointer" onClick={() => deleteRow(record._id)}>
          <img src="/admin/assets/img/table-delete-icon.png" alt="" />
        </div>
      ),
    align: "center",
    width: "5%",
  },
];
export const DataProjectCategory = Array.from({ length: 8 }, (_, i) => ({
  key: i + 1,
  name: renderName(
    [
      "Main Room",
      "Mechanical Room",
      "Electrical Room",
      "First Floor",
      "Basement",
      "Parking",
      "Stairs",
      "Roof",
    ][i],
    folderIcon
  ),
  lastupdated: "July 7, 2024. 08:45 AM",
  updatedby: "Justin",
  icon: deleteIcon,
}));

export const ColumnsDirectoriesChild = (deleteRow, userObj) => [
  {
    title: "Name",
    dataIndex: "title",
    render: (text, record) => (
      <div className="d-flex align-items-center">
        <img
          src="/admin/assets/img/pdf-icon.png"
          alt={record.title}
          style={{ marginRight: 8 }}
        />
        <Link
          to={`/view-pdf/${record.project._id}/${record.parent_id}`}
          state={{ record }}
        >
          {record.title}
        </Link>
      </div>
    ),
    sorter: (a, b) => a.title.length - b.title.length,
    width: "30%",
  },
  { title: "Revision", dataIndex: "revision", width: "20%" },
  {
    title: "Last Updated",
    dataIndex: "created_at",
    render: (text, record) => moment(text).format("MMM D, YYYY, h:mm A"),
    width: "30%",
  },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  {
    title: "",
    dataIndex: "icon",
    render: (text, record) =>
      (userObj?.role === "company" ||
        (userObj?._id === record?.project?.user?._id &&
          userObj?.policies?.some(
            (policy) => policy.module === "project" && policy.can_create
          ))) && (
        <div className="cursor-pointer" onClick={() => deleteRow(record._id)}>
          <img src="/admin/assets/img/table-delete-icon.png" alt="" />
        </div>
      ),
    align: "center",
    width: "5%",
  },
];
export const ColumnsPdfList = ({ onViewPdf }) => [
  {
    title: "Name",
    dataIndex: "title",
    render: (text, record) => (
      <div className="d-flex align-items-center">
        <img
          src="/admin/assets/img/pdf-icon.png"
          alt={record.title}
          style={{ marginRight: 8 }}
        />
        <span
          style={{ cursor: "pointer", color: "#000126" }}
          onClick={() => onViewPdf(record)}
        >
          {record.title}
        </span>
      </div>
    ),
    width: "100%",
  },
];
export const DataProjectSubCategory = Array.from({ length: 9 }, (_, i) => ({
  key: i + 1,
  name: renderName("ASF Trench block 1", pdfIcon),
  revision: ["Revision "] + [i + 1],
  lastupdated: "July 7, 2024. 08:45 AM",
  updatedby: "Justin",
  icon: deleteIcon,
}));

export const DataEmployees = Array.from({ length: 5 }, (_, i) => ({
  key: i + 1,
  name: renderName("Paul Harrisons", employeeImg),
  status: "Active",
  emailaddress: "<EMAIL>",
  company: "BluePrintFix",
  usertype: "Design & Office",
  action: (
    <CustomDropdown
      title="Action"
      className="table-dropdown"
      icon="true"
      items={items}
    />
  ),
}));

export const DataUserRole = Array.from({ length: 8 }, (_, i) => ({
  key: i + 1,
  usertype: ["Sales Representative", "Electrical Engineer"][i % 2],
  action: (
    <CustomDropdown
      title="Action"
      icon="true"
      className="table-dropdown"
      items={items}
    />
  ),
}));
