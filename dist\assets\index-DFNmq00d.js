import{r as _,u as P,j as e,m as w,h}from"./index-DS_hjASx.js";import{I as N,P as v,S as C}from"./index-B0HTmHEd.js";import{u as k,E as b}from"./rules-_ZUHH97D.js";import{P as $}from"./Pagination-DGfRHkE5.js";import"./index-B37y91Rn.js";import"./notification-7LrlFXRF.js";const E=({id:a,title:u,time:i,is_redirected:n,onStatusChange:o,module:j,reference_id:c,custom_data:r,description:t,date:s,showDate:d=!1})=>{const f=P(),{postData:x}=k("update_notification",{type:"submit",skipNotification:!0}),m=async()=>{let l;if((r==null?void 0:r.identifer)==="delete"){if(!n){const p=new FormData;p.append("is_redirected",!0),await x(p,y=>{y.statusCode===200&&o&&o(a,!0)},a)}w.error("This content is no longer available.");return}switch(j){case"projects":l=`/projects/${c}`;break;case"main_directories":l=`/projects/${r.project_id}`;break;case"sub_directories":l=`/directories/${r.project_id}/${r.parent_id}`;break;case"directories_detail":l=`/directories-detail/${r.project_id}/${r.parent_id}`;break;case"tasks":l=`/assign-task/${r.reference_id}`;break;default:l=null}if(!n){const p=new FormData;p.append("is_redirected",!0),await x(p,y=>{y.statusCode===200&&o&&o(a,!0)},a)}l&&f(l)};return e.jsxs("div",{className:"notification-card d-flex align-items-start justify-content-between p-3",onClick:m,style:{cursor:"pointer"},children:[e.jsxs("div",{className:"d-flex flex-column",children:[e.jsxs("div",{className:"notification-content",children:[e.jsx("span",{className:"notification-dot-wrapper",style:{width:"8px",marginRight:"8px"},children:!n&&e.jsx("span",{className:"notification-dot"})}),e.jsx("span",{className:"notification-text",children:u})]}),t&&e.jsx("p",{className:"mt-2 mb-0 text-muted",style:{paddingLeft:"24px"},children:t})]}),e.jsx("div",{children:e.jsx("p",{className:"font-16 color-light",children:d?`${s} at ${i}`:i})})]})},g=_.memo(E),I=()=>{const{data:a,fetchApi:u,pagination:i,loading:n,setQueryParams:o}=k("notification",{enablePagination:!0,defaultQueryParams:{page:1,limit:10}}),j=(t,s)=>{o({page:t,limit:s})},c=(t,s)=>{a==null||a.map(d=>d._id===t?{...d,is_redirected:s}:d),u()},r=_.useMemo(()=>a?a.reduce((t,s)=>{const d=h(s.created_at),f=h().startOf("day"),x=h().subtract(1,"day").startOf("day"),m={id:s._id,title:s.title,description:s.body,time:h(s.created_at).format("hh:mm A"),date:d.format("MMM DD, YYYY"),module:s.module,reference_id:s.reference_id,actor:s.actor,target:s.target,custom_data:s.custom_data,is_redirected:s.is_redirected};return d.isSame(f,"day")?t.today.push(m):d.isSame(x,"day")?t.yesterday.push(m):t.older.push(m),t},{today:[],yesterday:[],older:[]}):{today:[],yesterday:[],older:[]},[a]);return console.log("sortedNotifications",r),n?e.jsxs(N,{children:[e.jsx(v,{title:"Notifications"}),e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 col-md-10 col-lg-8 col-xl-7 col-xxl-6 mb-4",children:e.jsx("div",{className:"mt-4",children:[...Array(3)].map((t,s)=>e.jsx("div",{className:"mb-4",children:e.jsx(C,{active:!0,avatar:!0,paragraph:{rows:2},className:"notification-skeleton"})},s))})})})]}):e.jsxs(N,{children:[e.jsx(v,{title:"Notifications"}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 col-md-10 col-lg-8 col-xl-7 col-xxl-6 mb-4",children:[r.today.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"noti-title mt-4",children:"Today"}),r.today.map(t=>e.jsx(g,{...t,onStatusChange:c},t.id))]}),r.yesterday.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"noti-title mt-4",children:"Yesterday"}),r.yesterday.map(t=>e.jsx(g,{...t,onStatusChange:c},t.id))]}),r.older.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"noti-title mt-4",children:"Previous"}),r.older.map(t=>e.jsx(g,{...t,onStatusChange:c,showDate:!0},t.id))]}),(a==null?void 0:a.length)>0&&(i==null?void 0:i.count)>10&&e.jsx("div",{className:"custom-pagination d-flex justify-content-end mt-4",children:e.jsx($,{current:i.currentPage||1,total:i.count||0,pageSize:i.perPage||10,onChange:j})})]}),!(a!=null&&a.length)&&e.jsx("div",{className:"text-center mt-4",children:e.jsx(b,{description:"No notifications found",image:b.PRESENTED_IMAGE_SIMPLE})})]})]})};export{I as default};
