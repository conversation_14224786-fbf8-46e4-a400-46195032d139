import React, { useEffect, useRef, useState, memo } from "react";
import { getDocument, GlobalWorkerOptions } from "pdfjs-dist";
import workerSrc from "pdfjs-dist/build/pdf.worker.mjs?url";
import { Image } from "antd";

GlobalWorkerOptions.workerSrc = workerSrc;

const PdfImage = ({ pdfUrl }) => {
  const canvasRef = useRef(null);
  const [img, setImg] = useState("");
  const [previewVisible, setPreviewVisible] = useState(false); // State to control preview visibility

  useEffect(() => {
    const renderFirstPage = async () => {
      const pdf = await getDocument(pdfUrl).promise;
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 0.3 });

      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      await page.render({ canvasContext: context, viewport }).promise;
      setImg(canvas.toDataURL());
    };

    renderFirstPage();
  }, [pdfUrl]);

  return (
    <div>
      <canvas ref={canvasRef} style={{ display: "none" }} />
      {img && (
        <Image
          preview={{
            visible: previewVisible, // Control the visibility of the preview modal
            src: img,
            alt: "PDF Page 1",
            onVisibleChange: (visible) => setPreviewVisible(visible), // Toggle preview visibility on modal change
          }}
          src={img} // This is the image thumbnail that shows before clicking preview
          alt="PDF Page 1"
        />
      )}
    </div>
  );
};

export default memo(PdfImage);
