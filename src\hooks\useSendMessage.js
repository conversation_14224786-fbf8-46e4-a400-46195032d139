import { useCallback } from "react";

const useSendMessage = (socket, roomId, taskId) => {
  const sendMessage = useCallback(
    ({ message, type = "text", url = "" }) => {
      if (!socket || !roomId || !taskId) {
        return;
      }

      const payload = {
        room_id: roomId,
        message,
        task_id: taskId,
        type,
        url,
      };

      socket.emit("message", payload, (res) => {
        console.log("✅ Server response:", res);
      });
    },
    [socket, roomId, taskId]
  );

  return sendMessage;
};

export default useSendMessage;
