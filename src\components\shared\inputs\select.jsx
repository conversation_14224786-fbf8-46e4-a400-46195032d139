import React from "react";
import { Form, Select } from "antd";
const SelectInput = ({
  name,
  rules,
  placeholder,
  onChange,
  options,
  style,
  loading,
  label,
  defaultValue,
  mode,
  tokenSeparators,
  disabled,
  onSearch,
}) => {
  return (
    <Form.Item
      name={name}
      rules={rules}
      label={label}
      validateTrigger="onBlur"
      initialValue={defaultValue}
    >
      <Select
        size="large"
        defaultValue={defaultValue}
        placeholder={placeholder}
        onChange={onChange}
        style={style}
        loading={loading}
        options={options}
        mode={mode}
        disabled={disabled}
        filterOption={(input, option) =>
          option.label.toLowerCase().includes(input.toLowerCase())
        }
        tokenSeparators={tokenSeparators}
      />
    </Form.Item>
  );
};

export default SelectInput;
