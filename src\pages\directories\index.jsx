import React, { useState, memo, useEffect } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import BaseInput from "../../components/shared/inputs";
import CustomTable from "../../components/shared/table/customtable";
import FlatButton from "../../components/shared/button/flatbutton";
import CustomModal from "../../components/shared/modal";
import PageTitle from "../../components/shared/pagetitle";
import BackButton from "../../components/shared/button/backbutton";
import AddFolder from "../../components/partial/modalforms/addfolder";
import { ColumnsDirectoriesDetail } from "../../components/partial/configdata/tabledata";
import { useParams, useLocation } from "react-router-dom";
import { useFetch } from "../../hooks";
import { Spin } from "antd";
import useSweetAlert from "../../hooks/useSweetAlert";
import { debounce } from "lodash";
const Directories = () => {
  const user = window.user?.user;
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });
  const { showAlert } = useSweetAlert();
  let { project_id, parent_id } = useParams();
  const location = useLocation();
  const [search, setSearch] = useState("");
  const { record } = location.state || {};
  const { loading, data, fetchApi, pagination, setQueryParams } = useFetch(
    "directories",
    {
      slug: `/?project_id=${project_id}&parent_id=${parent_id}&`,
      enablePagination: true,
      defaultQueryParams: { page: 1, limit: 10 },
    }
  );
  const { postData: deleteItem } = useFetch("delete_directories", {
    type: "submit",
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handlePageChange = (page, pageSize) => {
    setQueryParams((prev) => ({
      ...prev,
      page,
      limit: pageSize,
      keyword: search,
    }));
  };
  useEffect(() => {
    fetchApi();
  }, [project_id]);

  const handleSearchChange = debounce((e) => {
    const value = e.target.value;
    setSearch(value);
    setQueryParams((prev) => ({
      ...prev,
      keyword: value,
      page: 1,
    }));
  }, 300);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      keyword: search,
    }));
  }, [search]);

  const deleteRow = async (id) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteItem("", cbSuccess, id);
    }
  };
  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      fetchApi();
    }
  };
  if (loading && window.lodash.isEmpty(record)) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          height: "100vh",
          alignItems: "center",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }
  return (
    <>
      <InnerLayout>
        <PageTitle
          title={<BackButton title={record?.title} />}
          buttons={
            <>
              <div>
                <BaseInput
                  name="search"
                  placeholder="Search"
                  icon={<img src="/admin/assets/img/search-icon.png" />}
                  value={search}
                  onChange={handleSearchChange}
                />
              </div>
              {(userObj?.role === "company" ||
                userObj?.policies?.some(
                  (policy) => policy.module === "project" && policy.can_create
                )) && (
                <div>
                  <FlatButton
                    title="+ Add Sub Folder"
                    className="mx-auto add-new-btn me-3"
                    onClick={() => setIsModalOpen(true)}
                  />
                </div>
              )}
            </>
          }
        />
        <div className="detail-table mt-4 mb-5">
          <CustomTable
            columns={ColumnsDirectoriesDetail(deleteRow, userObj)}
            data={data}
            loading={loading}
            rowKey={"_id"}
            pagination={{
              current: pagination?.currentPage,
              total: pagination?.count,
              pageSize: pagination?.perPage,
            }}
            showPagination={true}
            onChange={handlePageChange}
          />
        </div>
      </InnerLayout>
      <CustomModal
        title="Add Sub Folder"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        className="custom-modal"
        footer={false}
      >
        <AddFolder
          onCancel={() => setIsModalOpen(false)}
          onSuccess={fetchApi}
          projectId={project_id}
          title="Sub Folder Name"
          parentId={record?._id}
        />
      </CustomModal>
    </>
  );
};

export default memo(Directories);
