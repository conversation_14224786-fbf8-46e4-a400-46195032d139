* {
    padding: 0;
    margin: 0;
}
button {
    transition: all 0.5s ease;
  }
  .cursor-pointer{
  cursor: pointer;
  }
  .btn-theme {
    border-radius: 12px;
    background-color: #1182f1;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
  
  }
  
  .btn-theme2 {
    border-radius: 12px;
    background-color: #08ae22;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
  }
  
  .btn-theme3 {
    border-radius: 12px;
    background-color: #ae0808;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
  }
  
  .btn-theme::after,
  .btn-theme2::after,
  .btn-theme3::after {
    background-color: #111114;
    height: 100%;
    left: -35%;
    top: 0;
    transform: skew(40deg);
    transition-duration: 0.6s;
    transform-origin: top left;
    width: 0;
    position: absolute;
    content: "";
    z-index: -1;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
  }
  
  .btn-theme:hover::after,
  .btn-theme2:hover::after,
  .btn-theme3:hover::after {
    height: 100%;
    width: 135%;
  }
  
  .btn-theme:hover,
  .btn-theme2:hover,
  .btn-theme3:hover {
    color: #fff !important;
  }
  
  .auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff !important;
  }
  .edit-btn{
    border-radius: 10px;
    border: solid 1px rgba(255, 255, 255, 0.55);
    background-color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    color: #000;
  }
  .share-btn{
    border-radius: 4px;
    border: solid 1px #c6d1e6;
    background-image: linear-gradient(to bottom, #f8fbff, #e1e9f7);
    color: #3a3a3c;
    font-weight: 600;
    padding: 20px 20px;
    font-size: 15px;
  }
.add-new-btn{
    background-color: #28569f;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
    border-radius: 12px;
}
.reset-btn{
    background-color: #aab6cc;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
}



.signin-btn {
    width: 100%;
    height: 45px;
    border: 0;
    background-color: #28569f;
    border-radius: 12px;
    color: #fff;
    font-size: 16px;
    margin-top: 80px;
}
:where(.css-dev-only-do-not-override-1c0na6j).ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover{
    background-color: #1182f1;
    border-radius: 12px;
    color: #fff;
}
.filter-btn{
  background-color: #8d96a7;
  font-size: 16px;
  color: #fff;
  width: fit-content;
  padding: 19px 40px;
  border: 1px solid transparent;
  margin-left: 20px !important;

}

.ant-btn{
    border-radius: 12px !important;
}


.ant-form-item-label >label{
    color:#8d96a7!important;
}

.ant-form-item {
    margin-bottom: 0;
    margin-top: 22px;
}
.signup-text {
    text-align: center;
    margin-top: 90px !important;
    color: #000126 !important;
    margin-bottom: 30px !important;
 }
 .ant-modal-body label {
    color: #000 !important;
    font-weight: 600 !important;
}
.ant-picker {
  width: 100%;
}
.ant-btn:hover {
  background: transparent !important;
  border: 1px solid #4096ff !important;
 
}

.ant-btn:hover span {
  color:  #000  !important;
}
* {
    box-sizing: border-box;
}


/***********************
 ** Start Sidebar Menu **
 ***********************/




