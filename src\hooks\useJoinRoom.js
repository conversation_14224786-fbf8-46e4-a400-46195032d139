import { useEffect, useRef } from "react";

const useJoinRoom = (socket, taskId) => {
  const currentRoomRef = useRef(null);

  useEffect(() => {
    const roomId = `task_${taskId}`;
    if (socket && taskId && currentRoomRef.current !== roomId) {
      if (currentRoomRef.current) {
        socket.emit("leave_room", { room_id: currentRoomRef.current });
      }
      socket.emit("join_room", { room_id: roomId }, (res) => {
        currentRoomRef.current = roomId;
      });
    }

    return () => {
      if (socket && currentRoomRef.current) {
        socket.emit("leave_room", { room_id: currentRoomRef.current });
        currentRoomRef.current = null;
      }
    };
  }, [socket, taskId]);
};

export default useJoinRoom;
