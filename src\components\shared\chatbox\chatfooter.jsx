import React, { useState, useEffect, useRef, memo } from "react";
import { useFetch } from "../../../hooks";
import useSendMessage from "../../../hooks/useSendMessage";
import { Input } from "antd";
import FlatButton from "../button/flatbutton";
const { TextArea } = Input;
const ChatFooter = ({ taskId }) => {
  const fileInputRef = useRef();
  const { loading, postData } = useFetch("upload_media", {
    type: "submit",
    skipNotification: true,
  });
  const socket = window.socket;
  const roomId = `task_${taskId}`;
  const sendMessage = useSendMessage(socket, roomId, taskId);

  const [text, setText] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);

  const handleSend = async () => {
    if (text.trim()) {
      const messageText = text.trim();
      sendMessage({ message: messageText, type: "text" });
      setText("");
    }

    if (selectedFile) {
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("type", "image");

      postData(formData, cbSuccess);
    }
  };

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Only accept image files
      if (file.type.startsWith("image/")) {
        setSelectedFile(file);

        // Create preview for the image
        const reader = new FileReader();
        reader.onloadend = () => setPreviewUrl(reader.result);
        reader.readAsDataURL(file);
      } else {
        // Alert user if they try to upload a non-image file
        alert("Only image files are accepted");
      }
    }
    e.target.value = "";
  };

  const cbSuccess = (res) => {
    if (res?.statusCode === 200 && res.data?.url) {
      sendMessage({
        type: "image",
        url: res.data.url,
        message: "", // No message text, only the image will be shown
      });
    }
    setSelectedFile(null);
    setPreviewUrl(null);
  };
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.shiftKey) {
        // Allow new line with Shift+Enter
        return;
      } else {
        // Prevent default enter behavior and send message
        e.preventDefault();
        handleSend();
      }
    }
  };
  const removeFilePreview = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  return (
    <div className="chat-footer d-flex flex-column w-100">
      {previewUrl && (
        <div
          className="preview-box d-flex align-items-center justify-content-center mb-2 p-2"
          style={{
            position: "relative",
            width: 200,
            height: 200,
          }}
        >
          <button
            onClick={removeFilePreview}
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              background: "#fff",
              border: "1px solid #ccc",
              borderRadius: "50%",
              width: 20,
              height: 20,
              fontSize: 12,
              lineHeight: "16px",
              textAlign: "center",
              cursor: "pointer",
              zIndex: 10,
            }}
          >
            ✕
          </button>

          <img
            src={previewUrl}
            alt="preview"
            style={{
              maxHeight: "100%",
              maxWidth: "100%",
              objectFit: "contain",
            }}
          />
        </div>
      )}

      <input
        type="file"
        accept="image/*"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={handleFileChange}
      />

      <div className="d-flex align-items-center w-100">
        <div style={{ position: "relative", width: "100%" }}>
          <TextArea
            placeholder={
              selectedFile && !previewUrl
                ? selectedFile.name
                : "Enter message here...."
            }
            value={selectedFile ? "" : text}
            onChange={(e) => setText(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading}
            autoSize={{ minRows: 3, maxRows: 3 }}
            style={{
              paddingRight: "30px", // Space for the image suffix
            }}
          />
          <img
            src="/admin/assets/img/input-icon.png"
            alt="input icon"
            style={{
              position: "absolute",
              right: "8px", // Adjust the position of the image as needed
              top: "50%",
              transform: "translateY(-50%)", // Vertically center the image
              cursor: "pointer",
            }}
            onClick={handleFileClick}
          />
        </div>
        <FlatButton
          title={loading ? "Sending..." : "Send"}
          className="add-new-btn ms-2"
          onClick={handleSend}
          disabled={loading}
        />
      </div>
    </div>
  );
};

export default memo(ChatFooter);
