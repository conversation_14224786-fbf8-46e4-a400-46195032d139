import{j as s}from"./index-C6D6r1Zc.js";const d=a=>s.jsx("div",{className:"auth-sidebar",children:s.jsx("img",{src:a.src,alt:"",className:"img-fluid"})}),r=({children:a,logoClass:l="auth-logo",title:c,detail:i,src:e="/admin/assets/img/auth-img.png"})=>s.jsx("div",{className:"auth",children:s.jsx("div",{className:"container",children:s.jsxs("div",{className:"row gx-0",children:[s.jsx("div",{className:"col-12 col-sm-12 col-md-6 col-lg-7",children:s.jsx(d,{src:e})}),s.jsxs("div",{className:"col-12 col-sm-12 col-md-6 col-lg-5",children:[s.jsx("div",{className:l,children:s.jsx("img",{src:"/admin/assets/img/auth-logo.svg",alt:"CONSTRUCTIFIED",className:"brand-logo"})}),s.jsxs("div",{className:"auth-box",children:[s.jsxs("div",{className:"col-12",children:[s.jsx("p",{className:"font-36 color-black",children:c}),s.jsx("p",{children:i})]}),a]})]})]})})});export{r as A};
