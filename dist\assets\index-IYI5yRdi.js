import{j as I,L as Qa,r as S,_ as Za,d as jt,R as k,g as _a,e as sr,f as A,i as ei,k as ri}from"./index-C6D6r1Zc.js";import{S as Bn,I as ti,P as ni,a as kt}from"./index-D5NY69qa.js";import{F as Ut,u as me,B as ge,f as be,a as Nn,d as ai,E as tt}from"./rules-CIBIafmf.js";import{P as ii}from"./assigntask-ByVP1_vo.js";import{A as oi}from"./assignfilterform-BZQHWtiN.js";import{I as li}from"./iconbutton-CVpwvidu.js";import"./index-BiW9UDU7.js";import"./notification-C5lHSl-d.js";import"./index-DDGMAcG0.js";const ui=({title:e,img:r,_id:t,taskname:n,children:a})=>I.jsxs("div",{className:"assign-box",children:[I.jsx("div",{className:"assign-header",children:I.jsx("p",{children:e})}),I.jsx("div",{className:"assign-body",children:a})]}),si=({short_id:e,_id:r,title:t,className:n="assign-user-avatar",project:a})=>I.jsx("div",{className:"assign-item d-flex align-items-center",children:I.jsxs(Qa,{to:`/assign-task/${r}`,className:"d-flex align-items-center w-100",children:[I.jsx("div",{className:n,children:I.jsx("p",{children:window.helper.getInitials(t)})}),I.jsxs("div",{children:[I.jsxs("p",{className:"font-12 color-light",children:["ID: ",e," | ",a.title]}),I.jsx("p",{className:"font-16 color-black",children:t})]})]})}),ci=({onCancel:e,fetchTask:r})=>{var z,U;const t=(U=(z=window.user)==null?void 0:z.user)==null?void 0:U._id,[n]=Ut.useForm(),[a,i]=S.useState(null),[o,l]=S.useState(null),[u,d]=S.useState(null),[p,s]=S.useState([]),[c,f]=S.useState([]),{postData:v,loading:g}=me("create_task",{type:"submit"}),{loading:m,data:b}=me("project",{enablePagination:!0,defaultQueryParams:{page:1,limit:1e3}}),h={type:"delay"},{loading:D,data:y,fetchApi:w}=me("directories",h),{loading:E,data:T,fetchApi:$}=me("directories",h),{loading:O,data:M,fetchApi:F}=me("directories",h),Y=(P,N,V={})=>{const ve={[P]:N,...V};n.setFieldsValue(ve)};S.useEffect(()=>{a&&(w(`/?project_id=${a}&page=1&limit=1000`),l(null),Y("root_directory",null,{sub_directory:null}),F(""))},[a]),S.useEffect(()=>{a&&o&&($(`/?project_id=${a}&parent_id=${o}&page=1&limit=1000`),Y("sub_directory",null))},[o,a]),S.useEffect(()=>{a&&o&&u&&F(`/?project_id=${a}&parent_id=${u}&page=1&limit=1000`)},[o,a,u]);const _=P=>{i(P),l(null),Y("root_directory",null,{sub_directory:null}),n.setFieldsValue({assignees:[]});const N=b==null?void 0:b.find(V=>V._id===P);N!=null&&N.members?f(N.members.filter(V=>V._id!==t).map(V=>({value:V._id,label:V.name}))):f([])},j=P=>{l(P),Y("sub_directory",null)},R=P=>N=>{s(V=>N.target.checked?[...V,P]:V.filter(ve=>ve!==P))},B=P=>{var ce;const N=new FormData,V=["start_at","end_at"],ve=["assignees","directories"];V.forEach(ee=>{P[ee]&&N.append(ee,ai(P[ee]).format("YYYY-MM-DD"))}),(ce=P.assignees)!=null&&ce.length&&P.assignees.forEach(ee=>N.append("assignees[]",ee)),p!=null&&p.length&&p.forEach(ee=>{const de=M==null?void 0:M.find(Ee=>Ee._id===ee);de!=null&&de.parent_id&&N.append("directories[]",de._id)}),Object.entries(P).forEach(([ee,de])=>{!V.includes(ee)&&!ve.includes(ee)&&N.append(ee,de)}),v(N,q)},q=P=>{P.statusCode===200&&(e(),n.resetFields(),i(null),l(null),r(),F(""),s([]))},H=()=>u?I.jsxs("div",{className:"col-12",children:[I.jsx("label",{className:"color-black font-600 mt-3 mb-3",children:"Select Drawing PDF"}),O?I.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:[...Array(5)].map((P,N)=>I.jsx("div",{style:{margin:"10px"},children:I.jsx(Bn.Image,{active:!0,style:{width:100,height:100}})},N))}):M!=null&&M.length?I.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:M==null?void 0:M.map(P=>I.jsx(ii,{pdfUrl:P.file,checked:p.includes(P._id),onChange:R(P._id)},P._id))}):I.jsx(tt,{description:"No drawings found"})]}):null;return I.jsxs(Ut,{name:"create-task",layout:"vertical",onFinish:B,initialValues:{remember:!0},form:n,autoComplete:"off",children:[I.jsxs("div",{className:"row",children:[I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"title",label:"Title",rules:be.title})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"project_id",type:"select",label:"Project",options:b==null?void 0:b.map(P=>({value:P._id,label:P.title})),onChange:_,loading:m,rules:be.project_id,showSearch:!0})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"root_directory",type:"select",label:"Select Folder",options:y==null?void 0:y.map(P=>({value:P._id,label:P.title})),rules:be.root_directory,loading:D,onChange:j,disabled:!a})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"sub_directory",type:"select",label:"Select Sub Folder",options:T==null?void 0:T.map(P=>({value:P._id,label:P.title})),rules:be.sub_directory,loading:E,disabled:!o,onChange:P=>d(P)})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"assignees",type:"select",label:"Assignee",options:c,mode:"multiple",loading:m,rules:be.assignees,disabled:!a})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsxs("div",{className:"row",children:[I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"start_at",type:"datepiker",label:"Start Date",rules:be.start_at,disablePastDates:!0,format:"YYYY-MM-DD"})}),I.jsx("div",{className:"col-12 col-md-6",children:I.jsx(ge,{name:"end_at",type:"datepiker",label:"End Date",rules:be.end_at,disablePastDates:!0,format:"YYYY-MM-DD"})})]})}),I.jsx("div",{className:"col-12",children:I.jsx(ge,{name:"description",type:"textarea",label:"Description",rows:"5",rules:be.description})}),H()]}),I.jsx("div",{className:"text-end mt-4",children:I.jsx(Nn,{title:"Save",className:"add-new-btn",htmlType:"submit",loading:g})})]})},di=S.memo(ci);function On(e,r){e.prototype=Object.create(r.prototype),e.prototype.constructor=e,Za(e,r)}function te(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ht=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),Hr=function(){return Math.random().toString(36).substring(7).split("").join(".")},Vt={INIT:"@@redux/INIT"+Hr(),REPLACE:"@@redux/REPLACE"+Hr(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Hr()}};function pi(e){if(typeof e!="object"||e===null)return!1;for(var r=e;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}function Tn(e,r,t){var n;if(typeof r=="function"&&typeof t=="function"||typeof t=="function"&&typeof arguments[3]=="function")throw new Error(te(0));if(typeof r=="function"&&typeof t>"u"&&(t=r,r=void 0),typeof t<"u"){if(typeof t!="function")throw new Error(te(1));return t(Tn)(e,r)}if(typeof e!="function")throw new Error(te(2));var a=e,i=r,o=[],l=o,u=!1;function d(){l===o&&(l=o.slice())}function p(){if(u)throw new Error(te(3));return i}function s(g){if(typeof g!="function")throw new Error(te(4));if(u)throw new Error(te(5));var m=!0;return d(),l.push(g),function(){if(m){if(u)throw new Error(te(6));m=!1,d();var h=l.indexOf(g);l.splice(h,1),o=null}}}function c(g){if(!pi(g))throw new Error(te(7));if(typeof g.type>"u")throw new Error(te(8));if(u)throw new Error(te(9));try{u=!0,i=a(i,g)}finally{u=!1}for(var m=o=l,b=0;b<m.length;b++){var h=m[b];h()}return g}function f(g){if(typeof g!="function")throw new Error(te(10));a=g,c({type:Vt.REPLACE})}function v(){var g,m=s;return g={subscribe:function(h){if(typeof h!="object"||h===null)throw new Error(te(11));function D(){h.next&&h.next(p())}D();var y=m(D);return{unsubscribe:y}}},g[Ht]=function(){return this},g}return c({type:Vt.INIT}),n={dispatch:c,subscribe:s,getState:p,replaceReducer:f},n[Ht]=v,n}function qt(e,r){return function(){return r(e.apply(this,arguments))}}function zt(e,r){if(typeof e=="function")return qt(e,r);if(typeof e!="object"||e===null)throw new Error(te(16));var t={};for(var n in e){var a=e[n];typeof a=="function"&&(t[n]=qt(a,r))}return t}function Mn(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.length===0?function(n){return n}:r.length===1?r[0]:r.reduce(function(n,a){return function(){return n(a.apply(void 0,arguments))}})}function fi(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(n){return function(){var a=n.apply(void 0,arguments),i=function(){throw new Error(te(15))},o={getState:a.getState,dispatch:function(){return i.apply(void 0,arguments)}},l=r.map(function(u){return u(o)});return i=Mn.apply(void 0,l)(a.dispatch),jt(jt({},a),{},{dispatch:i})}}}var Fn=k.createContext(null);function vi(e){e()}var Ln=vi,gi=function(r){return Ln=r},mi=function(){return Ln};function bi(){var e=mi(),r=null,t=null;return{clear:function(){r=null,t=null},notify:function(){e(function(){for(var a=r;a;)a.callback(),a=a.next})},get:function(){for(var a=[],i=r;i;)a.push(i),i=i.next;return a},subscribe:function(a){var i=!0,o=t={callback:a,next:null,prev:t};return o.prev?o.prev.next=o:r=o,function(){!i||r===null||(i=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:r=o.next)}}}}var Yt={notify:function(){},get:function(){return[]}};function Gn(e,r){var t,n=Yt;function a(s){return u(),n.subscribe(s)}function i(){n.notify()}function o(){p.onStateChange&&p.onStateChange()}function l(){return!!t}function u(){t||(t=r?r.addNestedSub(o):e.subscribe(o),n=bi())}function d(){t&&(t(),t=void 0,n.clear(),n=Yt)}var p={addNestedSub:a,notifyNestedSubs:i,handleChangeWrapper:o,isSubscribed:l,trySubscribe:u,tryUnsubscribe:d,getListeners:function(){return n}};return p}var $n=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?S.useLayoutEffect:S.useEffect;function hi(e){var r=e.store,t=e.context,n=e.children,a=S.useMemo(function(){var l=Gn(r);return{store:r,subscription:l}},[r]),i=S.useMemo(function(){return r.getState()},[r]);$n(function(){var l=a.subscription;return l.onStateChange=l.notifyNestedSubs,l.trySubscribe(),i!==r.getState()&&l.notifyNestedSubs(),function(){l.tryUnsubscribe(),l.onStateChange=null}},[a,i]);var o=t||Fn;return k.createElement(o.Provider,{value:a},n)}var Wn={exports:{}},G={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X=typeof Symbol=="function"&&Symbol.for,st=X?Symbol.for("react.element"):60103,ct=X?Symbol.for("react.portal"):60106,hr=X?Symbol.for("react.fragment"):60107,yr=X?Symbol.for("react.strict_mode"):60108,xr=X?Symbol.for("react.profiler"):60114,Dr=X?Symbol.for("react.provider"):60109,Ir=X?Symbol.for("react.context"):60110,dt=X?Symbol.for("react.async_mode"):60111,Sr=X?Symbol.for("react.concurrent_mode"):60111,Cr=X?Symbol.for("react.forward_ref"):60112,wr=X?Symbol.for("react.suspense"):60113,yi=X?Symbol.for("react.suspense_list"):60120,Er=X?Symbol.for("react.memo"):60115,Pr=X?Symbol.for("react.lazy"):60116,xi=X?Symbol.for("react.block"):60121,Di=X?Symbol.for("react.fundamental"):60117,Ii=X?Symbol.for("react.responder"):60118,Si=X?Symbol.for("react.scope"):60119;function oe(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case st:switch(e=e.type,e){case dt:case Sr:case hr:case xr:case yr:case wr:return e;default:switch(e=e&&e.$$typeof,e){case Ir:case Cr:case Pr:case Er:case Dr:return e;default:return r}}case ct:return r}}}function jn(e){return oe(e)===Sr}G.AsyncMode=dt;G.ConcurrentMode=Sr;G.ContextConsumer=Ir;G.ContextProvider=Dr;G.Element=st;G.ForwardRef=Cr;G.Fragment=hr;G.Lazy=Pr;G.Memo=Er;G.Portal=ct;G.Profiler=xr;G.StrictMode=yr;G.Suspense=wr;G.isAsyncMode=function(e){return jn(e)||oe(e)===dt};G.isConcurrentMode=jn;G.isContextConsumer=function(e){return oe(e)===Ir};G.isContextProvider=function(e){return oe(e)===Dr};G.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===st};G.isForwardRef=function(e){return oe(e)===Cr};G.isFragment=function(e){return oe(e)===hr};G.isLazy=function(e){return oe(e)===Pr};G.isMemo=function(e){return oe(e)===Er};G.isPortal=function(e){return oe(e)===ct};G.isProfiler=function(e){return oe(e)===xr};G.isStrictMode=function(e){return oe(e)===yr};G.isSuspense=function(e){return oe(e)===wr};G.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===hr||e===Sr||e===xr||e===yr||e===wr||e===yi||typeof e=="object"&&e!==null&&(e.$$typeof===Pr||e.$$typeof===Er||e.$$typeof===Dr||e.$$typeof===Ir||e.$$typeof===Cr||e.$$typeof===Di||e.$$typeof===Ii||e.$$typeof===Si||e.$$typeof===xi)};G.typeOf=oe;Wn.exports=G;var Ci=Wn.exports,pt=Ci,wi={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ei={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Pi={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},kn={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},ft={};ft[pt.ForwardRef]=Pi;ft[pt.Memo]=kn;function Kt(e){return pt.isMemo(e)?kn:ft[e.$$typeof]||wi}var Ai=Object.defineProperty,Ri=Object.getOwnPropertyNames,Jt=Object.getOwnPropertySymbols,Bi=Object.getOwnPropertyDescriptor,Ni=Object.getPrototypeOf,Xt=Object.prototype;function Un(e,r,t){if(typeof r!="string"){if(Xt){var n=Ni(r);n&&n!==Xt&&Un(e,n,t)}var a=Ri(r);Jt&&(a=a.concat(Jt(r)));for(var i=Kt(e),o=Kt(r),l=0;l<a.length;++l){var u=a[l];if(!Ei[u]&&!(t&&t[u])&&!(o&&o[u])&&!(i&&i[u])){var d=Bi(r,u);try{Ai(e,u,d)}catch{}}}}return e}var Oi=Un;const Qt=_a(Oi);var Hn={exports:{}},W={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ar=60103,Rr=60106,ze=60107,Ye=60108,Ke=60114,Je=60109,Xe=60110,Qe=60112,Ze=60113,vt=60120,_e=60115,er=60116,Vn=60121,qn=60122,zn=60117,Yn=60129,Kn=60131;if(typeof Symbol=="function"&&Symbol.for){var Q=Symbol.for;Ar=Q("react.element"),Rr=Q("react.portal"),ze=Q("react.fragment"),Ye=Q("react.strict_mode"),Ke=Q("react.profiler"),Je=Q("react.provider"),Xe=Q("react.context"),Qe=Q("react.forward_ref"),Ze=Q("react.suspense"),vt=Q("react.suspense_list"),_e=Q("react.memo"),er=Q("react.lazy"),Vn=Q("react.block"),qn=Q("react.server.block"),zn=Q("react.fundamental"),Yn=Q("react.debug_trace_mode"),Kn=Q("react.legacy_hidden")}function fe(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case Ar:switch(e=e.type,e){case ze:case Ke:case Ye:case Ze:case vt:return e;default:switch(e=e&&e.$$typeof,e){case Xe:case Qe:case er:case _e:case Je:return e;default:return r}}case Rr:return r}}}var Ti=Je,Mi=Ar,Fi=Qe,Li=ze,Gi=er,$i=_e,Wi=Rr,ji=Ke,ki=Ye,Ui=Ze;W.ContextConsumer=Xe;W.ContextProvider=Ti;W.Element=Mi;W.ForwardRef=Fi;W.Fragment=Li;W.Lazy=Gi;W.Memo=$i;W.Portal=Wi;W.Profiler=ji;W.StrictMode=ki;W.Suspense=Ui;W.isAsyncMode=function(){return!1};W.isConcurrentMode=function(){return!1};W.isContextConsumer=function(e){return fe(e)===Xe};W.isContextProvider=function(e){return fe(e)===Je};W.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ar};W.isForwardRef=function(e){return fe(e)===Qe};W.isFragment=function(e){return fe(e)===ze};W.isLazy=function(e){return fe(e)===er};W.isMemo=function(e){return fe(e)===_e};W.isPortal=function(e){return fe(e)===Rr};W.isProfiler=function(e){return fe(e)===Ke};W.isStrictMode=function(e){return fe(e)===Ye};W.isSuspense=function(e){return fe(e)===Ze};W.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ze||e===Ke||e===Yn||e===Ye||e===Ze||e===vt||e===Kn||typeof e=="object"&&e!==null&&(e.$$typeof===er||e.$$typeof===_e||e.$$typeof===Je||e.$$typeof===Xe||e.$$typeof===Qe||e.$$typeof===zn||e.$$typeof===Vn||e[0]===qn)};W.typeOf=fe;Hn.exports=W;var Hi=Hn.exports,Vi=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],qi=["reactReduxForwardedRef"],zi=[],Yi=[null,null];function Ki(e,r){var t=e[1];return[r.payload,t+1]}function Zt(e,r,t){$n(function(){return e.apply(void 0,r)},t)}function Ji(e,r,t,n,a,i,o){e.current=n,r.current=a,t.current=!1,i.current&&(i.current=null,o())}function Xi(e,r,t,n,a,i,o,l,u,d){if(e){var p=!1,s=null,c=function(){if(!p){var g=r.getState(),m,b;try{m=n(g,a.current)}catch(h){b=h,s=h}b||(s=null),m===i.current?o.current||u():(i.current=m,l.current=m,o.current=!0,d({type:"STORE_UPDATED",payload:{error:b}}))}};t.onStateChange=c,t.trySubscribe(),c();var f=function(){if(p=!0,t.tryUnsubscribe(),t.onStateChange=null,s)throw s};return f}}var Qi=function(){return[null,0]};function Zi(e,r){r===void 0&&(r={});var t=r,n=t.getDisplayName,a=n===void 0?function(D){return"ConnectAdvanced("+D+")"}:n,i=t.methodName,o=i===void 0?"connectAdvanced":i,l=t.renderCountProp,u=l===void 0?void 0:l,d=t.shouldHandleStateChanges,p=d===void 0?!0:d,s=t.storeKey,c=s===void 0?"store":s;t.withRef;var f=t.forwardRef,v=f===void 0?!1:f,g=t.context,m=g===void 0?Fn:g,b=sr(t,Vi),h=m;return function(y){var w=y.displayName||y.name||"Component",E=a(w),T=A({},b,{getDisplayName:a,methodName:o,renderCountProp:u,shouldHandleStateChanges:p,storeKey:c,displayName:E,wrappedComponentName:w,WrappedComponent:y}),$=b.pure;function O(j){return e(j.dispatch,T)}var M=$?S.useMemo:function(j){return j()};function F(j){var R=S.useMemo(function(){var Fe=j.reactReduxForwardedRef,Ur=sr(j,qi);return[j.context,Fe,Ur]},[j]),B=R[0],q=R[1],H=R[2],z=S.useMemo(function(){return B&&B.Consumer&&Hi.isContextConsumer(k.createElement(B.Consumer,null))?B:h},[B,h]),U=S.useContext(z),P=!!j.store&&!!j.store.getState&&!!j.store.dispatch;U&&U.store;var N=P?j.store:U.store,V=S.useMemo(function(){return O(N)},[N]),ve=S.useMemo(function(){if(!p)return Yi;var Fe=Gn(N,P?null:U.subscription),Ur=Fe.notifyNestedSubs.bind(Fe);return[Fe,Ur]},[N,P,U]),ce=ve[0],ee=ve[1],de=S.useMemo(function(){return P?U:A({},U,{subscription:ce})},[P,U,ce]),Ee=S.useReducer(Ki,zi,Qi),Gr=Ee[0],Pe=Gr[0],$r=Ee[1];if(Pe&&Pe.error)throw Pe.error;var $t=S.useRef(),Wr=S.useRef(H),nr=S.useRef(),Wt=S.useRef(!1),jr=M(function(){return nr.current&&H===Wr.current?nr.current:V(N.getState(),H)},[N,Pe,H]);Zt(Ji,[Wr,$t,Wt,H,jr,nr,ee]),Zt(Xi,[p,N,ce,V,Wr,$t,Wt,nr,ee,$r],[N,ce,V]);var kr=S.useMemo(function(){return k.createElement(y,A({},jr,{ref:q}))},[q,y,jr]),Xa=S.useMemo(function(){return p?k.createElement(z.Provider,{value:de},kr):kr},[z,kr,de]);return Xa}var Y=$?k.memo(F):F;if(Y.WrappedComponent=y,Y.displayName=F.displayName=E,v){var _=k.forwardRef(function(R,B){return k.createElement(Y,A({},R,{reactReduxForwardedRef:B}))});return _.displayName=E,_.WrappedComponent=y,Qt(_,y)}return Qt(Y,y)}}function _t(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function Vr(e,r){if(_t(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var t=Object.keys(e),n=Object.keys(r);if(t.length!==n.length)return!1;for(var a=0;a<t.length;a++)if(!Object.prototype.hasOwnProperty.call(r,t[a])||!_t(e[t[a]],r[t[a]]))return!1;return!0}function _i(e,r){var t={},n=function(o){var l=e[o];typeof l=="function"&&(t[o]=function(){return r(l.apply(void 0,arguments))})};for(var a in e)n(a);return t}function gt(e){return function(t,n){var a=e(t,n);function i(){return a}return i.dependsOnOwnProps=!1,i}}function en(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?!!e.dependsOnOwnProps:e.length!==1}function Jn(e,r){return function(n,a){a.displayName;var i=function(l,u){return i.dependsOnOwnProps?i.mapToProps(l,u):i.mapToProps(l)};return i.dependsOnOwnProps=!0,i.mapToProps=function(l,u){i.mapToProps=e,i.dependsOnOwnProps=en(e);var d=i(l,u);return typeof d=="function"&&(i.mapToProps=d,i.dependsOnOwnProps=en(d),d=i(l,u)),d},i}}function eo(e){return typeof e=="function"?Jn(e):void 0}function ro(e){return e?void 0:gt(function(r){return{dispatch:r}})}function to(e){return e&&typeof e=="object"?gt(function(r){return _i(e,r)}):void 0}const no=[eo,ro,to];function ao(e){return typeof e=="function"?Jn(e):void 0}function io(e){return e?void 0:gt(function(){return{}})}const oo=[ao,io];function lo(e,r,t){return A({},t,e,r)}function uo(e){return function(t,n){n.displayName;var a=n.pure,i=n.areMergedPropsEqual,o=!1,l;return function(d,p,s){var c=e(d,p,s);return o?(!a||!i(c,l))&&(l=c):(o=!0,l=c),l}}}function so(e){return typeof e=="function"?uo(e):void 0}function co(e){return e?void 0:function(){return lo}}const po=[so,co];var fo=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function vo(e,r,t,n){return function(i,o){return t(e(i,o),r(n,o),o)}}function go(e,r,t,n,a){var i=a.areStatesEqual,o=a.areOwnPropsEqual,l=a.areStatePropsEqual,u=!1,d,p,s,c,f;function v(D,y){return d=D,p=y,s=e(d,p),c=r(n,p),f=t(s,c,p),u=!0,f}function g(){return s=e(d,p),r.dependsOnOwnProps&&(c=r(n,p)),f=t(s,c,p),f}function m(){return e.dependsOnOwnProps&&(s=e(d,p)),r.dependsOnOwnProps&&(c=r(n,p)),f=t(s,c,p),f}function b(){var D=e(d,p),y=!l(D,s);return s=D,y&&(f=t(s,c,p)),f}function h(D,y){var w=!o(y,p),E=!i(D,d,y,p);return d=D,p=y,w&&E?g():w?m():E?b():f}return function(y,w){return u?h(y,w):v(y,w)}}function mo(e,r){var t=r.initMapStateToProps,n=r.initMapDispatchToProps,a=r.initMergeProps,i=sr(r,fo),o=t(e,i),l=n(e,i),u=a(e,i),d=i.pure?go:vo;return d(o,l,u,e,i)}var bo=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function qr(e,r,t){for(var n=r.length-1;n>=0;n--){var a=r[n](e);if(a)return a}return function(i,o){throw new Error("Invalid value of type "+typeof e+" for "+t+" argument when connecting component "+o.wrappedComponentName+".")}}function ho(e,r){return e===r}function yo(e){var r={},t=r.connectHOC,n=t===void 0?Zi:t,a=r.mapStateToPropsFactories,i=a===void 0?oo:a,o=r.mapDispatchToPropsFactories,l=o===void 0?no:o,u=r.mergePropsFactories,d=u===void 0?po:u,p=r.selectorFactory,s=p===void 0?mo:p;return function(f,v,g,m){m===void 0&&(m={});var b=m,h=b.pure,D=h===void 0?!0:h,y=b.areStatesEqual,w=y===void 0?ho:y,E=b.areOwnPropsEqual,T=E===void 0?Vr:E,$=b.areStatePropsEqual,O=$===void 0?Vr:$,M=b.areMergedPropsEqual,F=M===void 0?Vr:M,Y=sr(b,bo),_=qr(f,i,"mapStateToProps"),j=qr(v,l,"mapDispatchToProps"),R=qr(g,d,"mergeProps");return n(s,A({methodName:"connect",getDisplayName:function(q){return"Connect("+q+")"},shouldHandleStateChanges:!!f,initMapStateToProps:_,initMapDispatchToProps:j,initMergeProps:R,pure:D,areStatesEqual:w,areOwnPropsEqual:T,areStatePropsEqual:O,areMergedPropsEqual:F},Y))}}const Xn=yo();gi(ei.unstable_batchedUpdates);function xo(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function Qn(e,r){var t=S.useState(function(){return{inputs:r,result:e()}})[0],n=S.useRef(!0),a=S.useRef(t),i=n.current||!!(r&&a.current.inputs&&xo(r,a.current.inputs)),o=i?a.current:{inputs:r,result:e()};return S.useEffect(function(){n.current=!1,a.current=o},[o]),o.result}function Do(e,r){return Qn(function(){return e},r)}var L=Qn,C=Do,Io="Invariant failed";function So(e,r){throw new Error(Io)}var pe=function(r){var t=r.top,n=r.right,a=r.bottom,i=r.left,o=n-i,l=a-t,u={top:t,right:n,bottom:a,left:i,width:o,height:l,x:i,y:t,center:{x:(n+i)/2,y:(a+t)/2}};return u},mt=function(r,t){return{top:r.top-t.top,left:r.left-t.left,bottom:r.bottom+t.bottom,right:r.right+t.right}},rn=function(r,t){return{top:r.top+t.top,left:r.left+t.left,bottom:r.bottom-t.bottom,right:r.right-t.right}},Co=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},zr={top:0,right:0,bottom:0,left:0},bt=function(r){var t=r.borderBox,n=r.margin,a=n===void 0?zr:n,i=r.border,o=i===void 0?zr:i,l=r.padding,u=l===void 0?zr:l,d=pe(mt(t,a)),p=pe(rn(t,o)),s=pe(rn(p,u));return{marginBox:d,borderBox:pe(t),paddingBox:p,contentBox:s,margin:a,border:o,padding:u}},le=function(r){var t=r.slice(0,-2),n=r.slice(-2);if(n!=="px")return 0;var a=Number(t);return isNaN(a)&&So(),a},wo=function(){return{x:window.pageXOffset,y:window.pageYOffset}},cr=function(r,t){var n=r.borderBox,a=r.border,i=r.margin,o=r.padding,l=Co(n,t);return bt({borderBox:l,border:a,margin:i,padding:o})},dr=function(r,t){return t===void 0&&(t=wo()),cr(r,t)},Zn=function(r,t){var n={top:le(t.marginTop),right:le(t.marginRight),bottom:le(t.marginBottom),left:le(t.marginLeft)},a={top:le(t.paddingTop),right:le(t.paddingRight),bottom:le(t.paddingBottom),left:le(t.paddingLeft)},i={top:le(t.borderTopWidth),right:le(t.borderRightWidth),bottom:le(t.borderBottomWidth),left:le(t.borderLeftWidth)};return bt({borderBox:r,margin:n,padding:a,border:i})},_n=function(r){var t=r.getBoundingClientRect(),n=window.getComputedStyle(r);return Zn(t,n)},tn=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function Eo(e,r){return!!(e===r||tn(e)&&tn(r))}function Po(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(!Eo(e[t],r[t]))return!1;return!0}function K(e,r){r===void 0&&(r=Po);var t,n=[],a,i=!1;function o(){for(var l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return i&&t===this&&r(l,n)||(a=e.apply(this,l),i=!0,t=this,n=l),a}return o}var ke=function(r){var t=[],n=null,a=function(){for(var o=arguments.length,l=new Array(o),u=0;u<o;u++)l[u]=arguments[u];t=l,!n&&(n=requestAnimationFrame(function(){n=null,r.apply(void 0,t)}))};return a.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},a};function ea(e,r){}ea.bind(null,"warn");ea.bind(null,"error");function he(){}function Ao(e,r){return A({},e,{},r)}function ue(e,r,t){var n=r.map(function(a){var i=Ao(t,a.options);return e.addEventListener(a.eventName,a.fn,i),function(){e.removeEventListener(a.eventName,a.fn,i)}});return function(){n.forEach(function(i){i()})}}var Ro="Invariant failed";function pr(e){this.message=e}pr.prototype.toString=function(){return this.message};function x(e,r){throw new pr(Ro)}var Bo=function(e){On(r,e);function r(){for(var n,a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return n=e.call.apply(e,[this].concat(i))||this,n.callbacks=null,n.unbind=he,n.onWindowError=function(l){var u=n.getCallbacks();u.isDragging()&&u.tryAbort();var d=l.error;d instanceof pr&&l.preventDefault()},n.getCallbacks=function(){if(!n.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return n.callbacks},n.setCallbacks=function(l){n.callbacks=l},n}var t=r.prototype;return t.componentDidMount=function(){this.unbind=ue(window,[{eventName:"error",fn:this.onWindowError}])},t.componentDidCatch=function(a){if(a instanceof pr){this.setState({});return}throw a},t.componentWillUnmount=function(){this.unbind()},t.render=function(){return this.props.children(this.setCallbacks)},r}(k.Component),No=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,fr=function(r){return r+1},Oo=function(r){return`
  You have lifted an item in position `+fr(r.source.index)+`
`},ra=function(r,t){var n=r.droppableId===t.droppableId,a=fr(r.index),i=fr(t.index);return n?`
      You have moved the item from position `+a+`
      to position `+i+`
    `:`
    You have moved the item from position `+a+`
    in list `+r.droppableId+`
    to list `+t.droppableId+`
    in position `+i+`
  `},ta=function(r,t,n){var a=t.droppableId===n.droppableId;return a?`
      The item `+r+`
      has been combined with `+n.draggableId:`
      The item `+r+`
      in list `+t.droppableId+`
      has been combined with `+n.draggableId+`
      in list `+n.droppableId+`
    `},To=function(r){var t=r.destination;if(t)return ra(r.source,t);var n=r.combine;return n?ta(r.draggableId,r.source,n):"You are over an area that cannot be dropped on"},nn=function(r){return`
  The item has returned to its starting position
  of `+fr(r.index)+`
`},Mo=function(r){if(r.reason==="CANCEL")return`
      Movement cancelled.
      `+nn(r.source)+`
    `;var t=r.destination,n=r.combine;return t?`
      You have dropped the item.
      `+ra(r.source,t)+`
    `:n?`
      You have dropped the item.
      `+ta(r.draggableId,r.source,n)+`
    `:`
    The item has been dropped while not over a drop area.
    `+nn(r.source)+`
  `},ur={dragHandleUsageInstructions:No,onDragStart:Oo,onDragUpdate:To,onDragEnd:Mo},J={x:0,y:0},Z=function(r,t){return{x:r.x+t.x,y:r.y+t.y}},ne=function(r,t){return{x:r.x-t.x,y:r.y-t.y}},ye=function(r,t){return r.x===t.x&&r.y===t.y},Oe=function(r){return{x:r.x!==0?-r.x:0,y:r.y!==0?-r.y:0}},we=function(r,t,n){var a;return n===void 0&&(n=0),a={},a[r]=t,a[r==="x"?"y":"x"]=n,a},Ue=function(r,t){return Math.sqrt(Math.pow(t.x-r.x,2)+Math.pow(t.y-r.y,2))},an=function(r,t){return Math.min.apply(Math,t.map(function(n){return Ue(r,n)}))},na=function(r){return function(t){return{x:r(t.x),y:r(t.y)}}},Fo=function(e,r){var t=pe({top:Math.max(r.top,e.top),right:Math.min(r.right,e.right),bottom:Math.min(r.bottom,e.bottom),left:Math.max(r.left,e.left)});return t.width<=0||t.height<=0?null:t},rr=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},on=function(r){return[{x:r.left,y:r.top},{x:r.right,y:r.top},{x:r.left,y:r.bottom},{x:r.right,y:r.bottom}]},Lo={top:0,right:0,bottom:0,left:0},Go=function(r,t){return t?rr(r,t.scroll.diff.displacement):r},$o=function(r,t,n){if(n&&n.increasedBy){var a;return A({},r,(a={},a[t.end]=r[t.end]+n.increasedBy[t.line],a))}return r},Wo=function(r,t){return t&&t.shouldClipSubject?Fo(t.pageMarginBox,r):pe(r)},Re=function(e){var r=e.page,t=e.withPlaceholder,n=e.axis,a=e.frame,i=Go(r.marginBox,a),o=$o(i,n,t),l=Wo(o,a);return{page:r,withPlaceholder:t,active:l}},ht=function(e,r){e.frame||x();var t=e.frame,n=ne(r,t.scroll.initial),a=Oe(n),i=A({},t,{scroll:{initial:t.scroll.initial,current:r,diff:{value:n,displacement:a},max:t.scroll.max}}),o=Re({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i}),l=A({},e,{frame:i,subject:o});return l};function vr(e){return Object.values?Object.values(e):Object.keys(e).map(function(r){return e[r]})}function yt(e,r){if(e.findIndex)return e.findIndex(r);for(var t=0;t<e.length;t++)if(r(e[t]))return t;return-1}function Ie(e,r){if(e.find)return e.find(r);var t=yt(e,r);if(t!==-1)return e[t]}function aa(e){return Array.prototype.slice.call(e)}var ia=K(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),oa=K(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),Br=K(function(e){return vr(e)}),jo=K(function(e){return vr(e)}),Te=K(function(e,r){var t=jo(r).filter(function(n){return e===n.descriptor.droppableId}).sort(function(n,a){return n.descriptor.index-a.descriptor.index});return t});function xt(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function Nr(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var Or=K(function(e,r){return r.filter(function(t){return t.descriptor.id!==e.descriptor.id})}),ko=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,a=e.insideDestination,i=e.previousImpact;if(!n.isCombineEnabled)return null;var o=xt(i);if(!o)return null;function l(g){var m={type:"COMBINE",combine:{draggableId:g,droppableId:n.descriptor.id}};return A({},i,{at:m})}var u=i.displaced.all,d=u.length?u[0]:null;if(r)return d?l(d):null;var p=Or(t,a);if(!d){if(!p.length)return null;var s=p[p.length-1];return l(s.descriptor.id)}var c=yt(p,function(g){return g.descriptor.id===d});c===-1&&x();var f=c-1;if(f<0)return null;var v=p[f];return l(v.descriptor.id)},Me=function(e,r){return e.descriptor.droppableId===r.descriptor.id},la={point:J,value:0},He={invisible:{},visible:{},all:[]},Uo={displaced:He,displacedBy:la,at:null},se=function(e,r){return function(t){return e<=t&&t<=r}},ua=function(e){var r=se(e.top,e.bottom),t=se(e.left,e.right);return function(n){var a=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);if(a)return!0;var i=r(n.top)||r(n.bottom),o=t(n.left)||t(n.right),l=i&&o;if(l)return!0;var u=n.top<e.top&&n.bottom>e.bottom,d=n.left<e.left&&n.right>e.right,p=u&&d;if(p)return!0;var s=u&&o||d&&i;return s}},Ho=function(e){var r=se(e.top,e.bottom),t=se(e.left,e.right);return function(n){var a=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);return a}},Dt={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},sa={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Vo=function(e){return function(r){var t=se(r.top,r.bottom),n=se(r.left,r.right);return function(a){return e===Dt?t(a.top)&&t(a.bottom):n(a.left)&&n(a.right)}}},qo=function(r,t){var n=t.frame?t.frame.scroll.diff.displacement:J;return rr(r,n)},zo=function(r,t,n){return t.subject.active?n(t.subject.active)(r):!1},Yo=function(r,t,n){return n(t)(r)},It=function(r){var t=r.target,n=r.destination,a=r.viewport,i=r.withDroppableDisplacement,o=r.isVisibleThroughFrameFn,l=i?qo(t,n):t;return zo(l,n,o)&&Yo(l,a,o)},Ko=function(r){return It(A({},r,{isVisibleThroughFrameFn:ua}))},ca=function(r){return It(A({},r,{isVisibleThroughFrameFn:Ho}))},Jo=function(r){return It(A({},r,{isVisibleThroughFrameFn:Vo(r.destination.axis)}))},Xo=function(r,t,n){if(typeof n=="boolean")return n;if(!t)return!0;var a=t.invisible,i=t.visible;if(a[r])return!1;var o=i[r];return o?o.shouldAnimate:!0};function Qo(e,r){var t=e.page.marginBox,n={top:r.point.y,right:0,bottom:0,left:r.point.x};return pe(mt(t,n))}function Ve(e){var r=e.afterDragging,t=e.destination,n=e.displacedBy,a=e.viewport,i=e.forceShouldAnimate,o=e.last;return r.reduce(function(u,d){var p=Qo(d,n),s=d.descriptor.id;u.all.push(s);var c=Ko({target:p,destination:t,viewport:a,withDroppableDisplacement:!0});if(!c)return u.invisible[d.descriptor.id]=!0,u;var f=Xo(s,o,i),v={draggableId:s,shouldAnimate:f};return u.visible[s]=v,u},{all:[],visible:{},invisible:{}})}function Zo(e,r){if(!e.length)return 0;var t=e[e.length-1].descriptor.index;return r.inHomeList?t:t+1}function ln(e){var r=e.insideDestination,t=e.inHomeList,n=e.displacedBy,a=e.destination,i=Zo(r,{inHomeList:t});return{displaced:He,displacedBy:n,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:i}}}}function gr(e){var r=e.draggable,t=e.insideDestination,n=e.destination,a=e.viewport,i=e.displacedBy,o=e.last,l=e.index,u=e.forceShouldAnimate,d=Me(r,n);if(l==null)return ln({insideDestination:t,inHomeList:d,displacedBy:i,destination:n});var p=Ie(t,function(g){return g.descriptor.index===l});if(!p)return ln({insideDestination:t,inHomeList:d,displacedBy:i,destination:n});var s=Or(r,t),c=t.indexOf(p),f=s.slice(c),v=Ve({afterDragging:f,destination:n,displacedBy:i,last:o,viewport:a.frame,forceShouldAnimate:u});return{displaced:v,displacedBy:i,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function De(e,r){return!!r.effected[e]}var _o=function(e){var r=e.isMovingForward,t=e.destination,n=e.draggables,a=e.combine,i=e.afterCritical;if(!t.isCombineEnabled)return null;var o=a.draggableId,l=n[o],u=l.descriptor.index,d=De(o,i);return d?r?u:u-1:r?u+1:u},el=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.insideDestination,a=e.location;if(!n.length)return null;var i=a.index,o=r?i+1:i-1,l=n[0].descriptor.index,u=n[n.length-1].descriptor.index,d=t?u:u+1;return o<l||o>d?null:o},rl=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.draggable,a=e.draggables,i=e.destination,o=e.insideDestination,l=e.previousImpact,u=e.viewport,d=e.afterCritical,p=l.at;if(p||x(),p.type==="REORDER"){var s=el({isMovingForward:r,isInHomeList:t,location:p.destination,insideDestination:o});return s==null?null:gr({draggable:n,insideDestination:o,destination:i,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:s})}var c=_o({isMovingForward:r,destination:i,displaced:l.displaced,draggables:a,combine:p.combine,afterCritical:d});return c==null?null:gr({draggable:n,insideDestination:o,destination:i,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:c})},tl=function(e){var r=e.displaced,t=e.afterCritical,n=e.combineWith,a=e.displacedBy,i=!!(r.visible[n]||r.invisible[n]);return De(n,t)?i?J:Oe(a.point):i?a.point:J},nl=function(e){var r=e.afterCritical,t=e.impact,n=e.draggables,a=Nr(t);a||x();var i=a.draggableId,o=n[i].page.borderBox.center,l=tl({displaced:t.displaced,afterCritical:r,combineWith:i,displacedBy:t.displacedBy});return Z(o,l)},da=function(r,t){return t.margin[r.start]+t.borderBox[r.size]/2},al=function(r,t){return t.margin[r.end]+t.borderBox[r.size]/2},St=function(r,t,n){return t[r.crossAxisStart]+n.margin[r.crossAxisStart]+n.borderBox[r.crossAxisSize]/2},un=function(r){var t=r.axis,n=r.moveRelativeTo,a=r.isMoving;return we(t.line,n.marginBox[t.end]+da(t,a),St(t,n.marginBox,a))},sn=function(r){var t=r.axis,n=r.moveRelativeTo,a=r.isMoving;return we(t.line,n.marginBox[t.start]-al(t,a),St(t,n.marginBox,a))},il=function(r){var t=r.axis,n=r.moveInto,a=r.isMoving;return we(t.line,n.contentBox[t.start]+da(t,a),St(t,n.contentBox,a))},ol=function(e){var r=e.impact,t=e.draggable,n=e.draggables,a=e.droppable,i=e.afterCritical,o=Te(a.descriptor.id,n),l=t.page,u=a.axis;if(!o.length)return il({axis:u,moveInto:a.page,isMoving:l});var d=r.displaced,p=r.displacedBy,s=d.all[0];if(s){var c=n[s];if(De(s,i))return sn({axis:u,moveRelativeTo:c.page,isMoving:l});var f=cr(c.page,p.point);return sn({axis:u,moveRelativeTo:f,isMoving:l})}var v=o[o.length-1];if(v.descriptor.id===t.descriptor.id)return l.borderBox.center;if(De(v.descriptor.id,i)){var g=cr(v.page,Oe(i.displacedBy.point));return un({axis:u,moveRelativeTo:g,isMoving:l})}return un({axis:u,moveRelativeTo:v.page,isMoving:l})},nt=function(e,r){var t=e.frame;return t?Z(r,t.scroll.diff.displacement):r},ll=function(r){var t=r.impact,n=r.draggable,a=r.droppable,i=r.draggables,o=r.afterCritical,l=n.page.borderBox.center,u=t.at;return!a||!u?l:u.type==="REORDER"?ol({impact:t,draggable:n,draggables:i,droppable:a,afterCritical:o}):nl({impact:t,draggables:i,afterCritical:o})},Tr=function(e){var r=ll(e),t=e.droppable,n=t?nt(t,r):r;return n},pa=function(e,r){var t=ne(r,e.scroll.initial),n=Oe(t),a=pe({top:r.y,bottom:r.y+e.frame.height,left:r.x,right:r.x+e.frame.width}),i={frame:a,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:r,diff:{value:t,displacement:n}}};return i};function cn(e,r){return e.map(function(t){return r[t]})}function ul(e,r){for(var t=0;t<r.length;t++){var n=r[t].visible[e];if(n)return n}return null}var sl=function(e){var r=e.impact,t=e.viewport,n=e.destination,a=e.draggables,i=e.maxScrollChange,o=pa(t,Z(t.scroll.current,i)),l=n.frame?ht(n,Z(n.frame.scroll.current,i)):n,u=r.displaced,d=Ve({afterDragging:cn(u.all,a),destination:n,displacedBy:r.displacedBy,viewport:o.frame,last:u,forceShouldAnimate:!1}),p=Ve({afterDragging:cn(u.all,a),destination:l,displacedBy:r.displacedBy,viewport:t.frame,last:u,forceShouldAnimate:!1}),s={},c={},f=[u,d,p];u.all.forEach(function(g){var m=ul(g,f);if(m){c[g]=m;return}s[g]=!0});var v=A({},r,{displaced:{all:u.all,invisible:s,visible:c}});return v},cl=function(e,r){return Z(e.scroll.diff.displacement,r)},Ct=function(e){var r=e.pageBorderBoxCenter,t=e.draggable,n=e.viewport,a=cl(n,r),i=ne(a,t.page.borderBox.center);return Z(t.client.borderBox.center,i)},fa=function(e){var r=e.draggable,t=e.destination,n=e.newPageBorderBoxCenter,a=e.viewport,i=e.withDroppableDisplacement,o=e.onlyOnMainAxis,l=o===void 0?!1:o,u=ne(n,r.page.borderBox.center),d=rr(r.page.borderBox,u),p={target:d,destination:t,withDroppableDisplacement:i,viewport:a};return l?Jo(p):ca(p)},dl=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,a=e.draggables,i=e.previousImpact,o=e.viewport,l=e.previousPageBorderBoxCenter,u=e.previousClientSelection,d=e.afterCritical;if(!n.isEnabled)return null;var p=Te(n.descriptor.id,a),s=Me(t,n),c=ko({isMovingForward:r,draggable:t,destination:n,insideDestination:p,previousImpact:i})||rl({isMovingForward:r,isInHomeList:s,draggable:t,draggables:a,destination:n,insideDestination:p,previousImpact:i,viewport:o,afterCritical:d});if(!c)return null;var f=Tr({impact:c,draggable:t,droppable:n,draggables:a,afterCritical:d}),v=fa({draggable:t,destination:n,newPageBorderBoxCenter:f,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(v){var g=Ct({pageBorderBoxCenter:f,draggable:t,viewport:o});return{clientSelection:g,impact:c,scrollJumpRequest:null}}var m=ne(f,l),b=sl({impact:c,viewport:o,destination:n,draggables:a,maxScrollChange:m});return{clientSelection:u,impact:b,scrollJumpRequest:m}},re=function(r){var t=r.subject.active;return t||x(),t},pl=function(e){var r=e.isMovingForward,t=e.pageBorderBoxCenter,n=e.source,a=e.droppables,i=e.viewport,o=n.subject.active;if(!o)return null;var l=n.axis,u=se(o[l.start],o[l.end]),d=Br(a).filter(function(s){return s!==n}).filter(function(s){return s.isEnabled}).filter(function(s){return!!s.subject.active}).filter(function(s){return ua(i.frame)(re(s))}).filter(function(s){var c=re(s);return r?o[l.crossAxisEnd]<c[l.crossAxisEnd]:c[l.crossAxisStart]<o[l.crossAxisStart]}).filter(function(s){var c=re(s),f=se(c[l.start],c[l.end]);return u(c[l.start])||u(c[l.end])||f(o[l.start])||f(o[l.end])}).sort(function(s,c){var f=re(s)[l.crossAxisStart],v=re(c)[l.crossAxisStart];return r?f-v:v-f}).filter(function(s,c,f){return re(s)[l.crossAxisStart]===re(f[0])[l.crossAxisStart]});if(!d.length)return null;if(d.length===1)return d[0];var p=d.filter(function(s){var c=se(re(s)[l.start],re(s)[l.end]);return c(t[l.line])});return p.length===1?p[0]:p.length>1?p.sort(function(s,c){return re(s)[l.start]-re(c)[l.start]})[0]:d.sort(function(s,c){var f=an(t,on(re(s))),v=an(t,on(re(c)));return f!==v?f-v:re(s)[l.start]-re(c)[l.start]})[0]},dn=function(r,t){var n=r.page.borderBox.center;return De(r.descriptor.id,t)?ne(n,t.displacedBy.point):n},fl=function(r,t){var n=r.page.borderBox;return De(r.descriptor.id,t)?rr(n,Oe(t.displacedBy.point)):n},vl=function(e){var r=e.pageBorderBoxCenter,t=e.viewport,n=e.destination,a=e.insideDestination,i=e.afterCritical,o=a.filter(function(l){return ca({target:fl(l,i),destination:n,viewport:t.frame,withDroppableDisplacement:!0})}).sort(function(l,u){var d=Ue(r,nt(n,dn(l,i))),p=Ue(r,nt(n,dn(u,i)));return d<p?-1:p<d?1:l.descriptor.index-u.descriptor.index});return o[0]||null},tr=K(function(r,t){var n=t[r.line];return{value:n,point:we(r.line,n)}}),gl=function(r,t,n){var a=r.axis;if(r.descriptor.mode==="virtual")return we(a.line,t[a.line]);var i=r.subject.page.contentBox[a.size],o=Te(r.descriptor.id,n),l=o.reduce(function(p,s){return p+s.client.marginBox[a.size]},0),u=l+t[a.line],d=u-i;return d<=0?null:we(a.line,d)},va=function(r,t){return A({},r,{scroll:A({},r.scroll,{max:t})})},ga=function(r,t,n){var a=r.frame;Me(t,r)&&x(),r.subject.withPlaceholder&&x();var i=tr(r.axis,t.displaceBy).point,o=gl(r,i,n),l={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:r.frame?r.frame.scroll.max:null};if(!a){var u=Re({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:r.frame});return A({},r,{subject:u})}var d=o?Z(a.scroll.max,o):a.scroll.max,p=va(a,d),s=Re({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:p});return A({},r,{subject:s,frame:p})},ml=function(r){var t=r.subject.withPlaceholder;t||x();var n=r.frame;if(!n){var a=Re({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null});return A({},r,{subject:a})}var i=t.oldFrameMaxScroll;i||x();var o=va(n,i),l=Re({page:r.subject.page,axis:r.axis,frame:o,withPlaceholder:null});return A({},r,{subject:l,frame:o})},bl=function(e){var r=e.previousPageBorderBoxCenter,t=e.moveRelativeTo,n=e.insideDestination,a=e.draggable,i=e.draggables,o=e.destination,l=e.viewport,u=e.afterCritical;if(!t){if(n.length)return null;var d={displaced:He,displacedBy:la,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},p=Tr({impact:d,draggable:a,droppable:o,draggables:i,afterCritical:u}),s=Me(a,o)?o:ga(o,a,i),c=fa({draggable:a,destination:s,newPageBorderBoxCenter:p,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return c?d:null}var f=r[o.axis.line]<=t.page.borderBox.center[o.axis.line],v=function(){var m=t.descriptor.index;return t.descriptor.id===a.descriptor.id||f?m:m+1}(),g=tr(o.axis,a.displaceBy);return gr({draggable:a,insideDestination:n,destination:o,viewport:l,displacedBy:g,last:He,index:v})},hl=function(e){var r=e.isMovingForward,t=e.previousPageBorderBoxCenter,n=e.draggable,a=e.isOver,i=e.draggables,o=e.droppables,l=e.viewport,u=e.afterCritical,d=pl({isMovingForward:r,pageBorderBoxCenter:t,source:a,droppables:o,viewport:l});if(!d)return null;var p=Te(d.descriptor.id,i),s=vl({pageBorderBoxCenter:t,viewport:l,destination:d,insideDestination:p,afterCritical:u}),c=bl({previousPageBorderBoxCenter:t,destination:d,draggable:n,draggables:i,moveRelativeTo:s,insideDestination:p,viewport:l,afterCritical:u});if(!c)return null;var f=Tr({impact:c,draggable:n,droppable:d,draggables:i,afterCritical:u}),v=Ct({pageBorderBoxCenter:f,draggable:n,viewport:l});return{clientSelection:v,impact:c,scrollJumpRequest:null}},ae=function(e){var r=e.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null},yl=function(r,t){var n=ae(r);return n?t[n]:null},xl=function(e){var r=e.state,t=e.type,n=yl(r.impact,r.dimensions.droppables),a=!!n,i=r.dimensions.droppables[r.critical.droppable.id],o=n||i,l=o.axis.direction,u=l==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||l==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(u&&!a)return null;var d=t==="MOVE_DOWN"||t==="MOVE_RIGHT",p=r.dimensions.draggables[r.critical.draggable.id],s=r.current.page.borderBoxCenter,c=r.dimensions,f=c.draggables,v=c.droppables;return u?dl({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,destination:o,draggables:f,viewport:r.viewport,previousClientSelection:r.current.client.selection,previousImpact:r.impact,afterCritical:r.afterCritical}):hl({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,isOver:o,draggables:f,droppables:v,viewport:r.viewport,afterCritical:r.afterCritical})};function Ce(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function ma(e){var r=se(e.top,e.bottom),t=se(e.left,e.right);return function(a){return r(a.y)&&t(a.x)}}function Dl(e,r){return e.left<r.right&&e.right>r.left&&e.top<r.bottom&&e.bottom>r.top}function Il(e){var r=e.pageBorderBox,t=e.draggable,n=e.candidates,a=t.page.borderBox.center,i=n.map(function(o){var l=o.axis,u=we(o.axis.line,r.center[l.line],o.page.borderBox.center[l.crossAxisLine]);return{id:o.descriptor.id,distance:Ue(a,u)}}).sort(function(o,l){return l.distance-o.distance});return i[0]?i[0].id:null}function Sl(e){var r=e.pageBorderBox,t=e.draggable,n=e.droppables,a=Br(n).filter(function(i){if(!i.isEnabled)return!1;var o=i.subject.active;if(!o||!Dl(r,o))return!1;if(ma(o)(r.center))return!0;var l=i.axis,u=o.center[l.crossAxisLine],d=r[l.crossAxisStart],p=r[l.crossAxisEnd],s=se(o[l.crossAxisStart],o[l.crossAxisEnd]),c=s(d),f=s(p);return!c&&!f?!0:c?d<u:p>u});return a.length?a.length===1?a[0].descriptor.id:Il({pageBorderBox:r,draggable:t,candidates:a}):null}var ba=function(r,t){return pe(rr(r,t))},Cl=function(e,r){var t=e.frame;return t?ba(r,t.scroll.diff.value):r};function ha(e){var r=e.displaced,t=e.id;return!!(r.visible[t]||r.invisible[t])}function wl(e){var r=e.draggable,t=e.closest,n=e.inHomeList;return t?n&&t.descriptor.index>r.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var El=function(e){var r=e.pageBorderBoxWithDroppableScroll,t=e.draggable,n=e.destination,a=e.insideDestination,i=e.last,o=e.viewport,l=e.afterCritical,u=n.axis,d=tr(n.axis,t.displaceBy),p=d.value,s=r[u.start],c=r[u.end],f=Or(t,a),v=Ie(f,function(m){var b=m.descriptor.id,h=m.page.borderBox.center[u.line],D=De(b,l),y=ha({displaced:i,id:b});return D?y?c<=h:s<h-p:y?c<=h+p:s<h}),g=wl({draggable:t,closest:v,inHomeList:Me(t,n)});return gr({draggable:t,insideDestination:a,destination:n,viewport:o,last:i,displacedBy:d,index:g})},Pl=4,Al=function(e){var r=e.draggable,t=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,a=e.destination,i=e.insideDestination,o=e.afterCritical;if(!a.isCombineEnabled)return null;var l=a.axis,u=tr(a.axis,r.displaceBy),d=u.value,p=t[l.start],s=t[l.end],c=Or(r,i),f=Ie(c,function(g){var m=g.descriptor.id,b=g.page.borderBox,h=b[l.size],D=h/Pl,y=De(m,o),w=ha({displaced:n.displaced,id:m});return y?w?s>b[l.start]+D&&s<b[l.end]-D:p>b[l.start]-d+D&&p<b[l.end]-d-D:w?s>b[l.start]+d+D&&s<b[l.end]+d-D:p>b[l.start]+D&&p<b[l.end]-D});if(!f)return null;var v={displacedBy:u,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:f.descriptor.id,droppableId:a.descriptor.id}}};return v},ya=function(e){var r=e.pageOffset,t=e.draggable,n=e.draggables,a=e.droppables,i=e.previousImpact,o=e.viewport,l=e.afterCritical,u=ba(t.page.borderBox,r),d=Sl({pageBorderBox:u,draggable:t,droppables:a});if(!d)return Uo;var p=a[d],s=Te(p.descriptor.id,n),c=Cl(p,u);return Al({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:i,destination:p,insideDestination:s,afterCritical:l})||El({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:p,insideDestination:s,last:i.displaced,viewport:o,afterCritical:l})},wt=function(e,r){var t;return A({},e,(t={},t[r.descriptor.id]=r,t))},Rl=function(r){var t=r.previousImpact,n=r.impact,a=r.droppables,i=ae(t),o=ae(n);if(!i||i===o)return a;var l=a[i];if(!l.subject.withPlaceholder)return a;var u=ml(l);return wt(a,u)},Bl=function(e){var r=e.draggable,t=e.draggables,n=e.droppables,a=e.previousImpact,i=e.impact,o=Rl({previousImpact:a,impact:i,droppables:n}),l=ae(i);if(!l)return o;var u=n[l];if(Me(r,u)||u.subject.withPlaceholder)return o;var d=ga(u,r,t);return wt(o,d)},We=function(e){var r=e.state,t=e.clientSelection,n=e.dimensions,a=e.viewport,i=e.impact,o=e.scrollJumpRequest,l=a||r.viewport,u=n||r.dimensions,d=t||r.current.client.selection,p=ne(d,r.initial.client.selection),s={offset:p,selection:d,borderBoxCenter:Z(r.initial.client.borderBoxCenter,p)},c={selection:Z(s.selection,l.scroll.current),borderBoxCenter:Z(s.borderBoxCenter,l.scroll.current),offset:Z(s.offset,l.scroll.diff.value)},f={client:s,page:c};if(r.phase==="COLLECTING")return A({phase:"COLLECTING"},r,{dimensions:u,viewport:l,current:f});var v=u.draggables[r.critical.draggable.id],g=i||ya({pageOffset:c.offset,draggable:v,draggables:u.draggables,droppables:u.droppables,previousImpact:r.impact,viewport:l,afterCritical:r.afterCritical}),m=Bl({draggable:v,impact:g,previousImpact:r.impact,draggables:u.draggables,droppables:u.droppables}),b=A({},r,{current:f,dimensions:{draggables:u.draggables,droppables:m},impact:g,viewport:l,scrollJumpRequest:o||null,forceShouldAnimate:o?!1:null});return b};function Nl(e,r){return e.map(function(t){return r[t]})}var xa=function(e){var r=e.impact,t=e.viewport,n=e.draggables,a=e.destination,i=e.forceShouldAnimate,o=r.displaced,l=Nl(o.all,n),u=Ve({afterDragging:l,destination:a,displacedBy:r.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:o});return A({},r,{displaced:u})},Da=function(e){var r=e.impact,t=e.draggable,n=e.droppable,a=e.draggables,i=e.viewport,o=e.afterCritical,l=Tr({impact:r,draggable:t,draggables:a,droppable:n,afterCritical:o});return Ct({pageBorderBoxCenter:l,draggable:t,viewport:i})},Ia=function(e){var r=e.state,t=e.dimensions,n=e.viewport;r.movementMode!=="SNAP"&&x();var a=r.impact,i=n||r.viewport,o=t||r.dimensions,l=o.draggables,u=o.droppables,d=l[r.critical.draggable.id],p=ae(a);p||x();var s=u[p],c=xa({impact:a,viewport:i,destination:s,draggables:l}),f=Da({impact:c,draggable:d,droppable:s,draggables:l,viewport:i,afterCritical:r.afterCritical});return We({impact:c,clientSelection:f,state:r,dimensions:o,viewport:i})},Ol=function(e){return{index:e.index,droppableId:e.droppableId}},Sa=function(e){var r=e.draggable,t=e.home,n=e.draggables,a=e.viewport,i=tr(t.axis,r.displaceBy),o=Te(t.descriptor.id,n),l=o.indexOf(r);l===-1&&x();var u=o.slice(l+1),d=u.reduce(function(f,v){return f[v.descriptor.id]=!0,f},{}),p={inVirtualList:t.descriptor.mode==="virtual",displacedBy:i,effected:d},s=Ve({afterDragging:u,destination:t,displacedBy:i,last:null,viewport:a.frame,forceShouldAnimate:!1}),c={displaced:s,displacedBy:i,at:{type:"REORDER",destination:Ol(r.descriptor)}};return{impact:c,afterCritical:p}},Tl=function(e,r){return{draggables:e.draggables,droppables:wt(e.droppables,r)}},Ml=function(e){var r=e.draggable,t=e.offset,n=e.initialWindowScroll,a=cr(r.client,t),i=dr(a,n),o=A({},r,{placeholder:A({},r.placeholder,{client:a}),client:a,page:i});return o},Fl=function(e){var r=e.frame;return r||x(),r},Ll=function(e){var r=e.additions,t=e.updatedDroppables,n=e.viewport,a=n.scroll.diff.value;return r.map(function(i){var o=i.descriptor.droppableId,l=t[o],u=Fl(l),d=u.scroll.diff.value,p=Z(a,d),s=Ml({draggable:i,offset:p,initialWindowScroll:n.scroll.initial});return s})},Gl=function(e){var r=e.state,t=e.published,n=t.modified.map(function(D){var y=r.dimensions.droppables[D.droppableId],w=ht(y,D.scroll);return w}),a=A({},r.dimensions.droppables,{},ia(n)),i=oa(Ll({additions:t.additions,updatedDroppables:a,viewport:r.viewport})),o=A({},r.dimensions.draggables,{},i);t.removals.forEach(function(D){delete o[D]});var l={droppables:a,draggables:o},u=ae(r.impact),d=u?l.droppables[u]:null,p=l.draggables[r.critical.draggable.id],s=l.droppables[r.critical.droppable.id],c=Sa({draggable:p,home:s,draggables:o,viewport:r.viewport}),f=c.impact,v=c.afterCritical,g=d&&d.isCombineEnabled?r.impact:f,m=ya({pageOffset:r.current.page.offset,draggable:l.draggables[r.critical.draggable.id],draggables:l.draggables,droppables:l.droppables,previousImpact:g,viewport:r.viewport,afterCritical:v}),b=A({phase:"DRAGGING"},r,{phase:"DRAGGING",impact:m,onLiftImpact:f,dimensions:l,afterCritical:v,forceShouldAnimate:!1});if(r.phase==="COLLECTING")return b;var h=A({phase:"DROP_PENDING"},b,{phase:"DROP_PENDING",reason:r.reason,isWaiting:!1});return h},at=function(r){return r.movementMode==="SNAP"},Yr=function(r,t,n){var a=Tl(r.dimensions,t);return!at(r)||n?We({state:r,dimensions:a}):Ia({state:r,dimensions:a})};function Kr(e){return e.isDragging&&e.movementMode==="SNAP"?A({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var pn={phase:"IDLE",completed:null,shouldFlush:!1},$l=function(e,r){if(e===void 0&&(e=pn),r.type==="FLUSH")return A({},pn,{shouldFlush:!0});if(r.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&x();var t=r.payload,n=t.critical,a=t.clientSelection,i=t.viewport,o=t.dimensions,l=t.movementMode,u=o.draggables[n.draggable.id],d=o.droppables[n.droppable.id],p={selection:a,borderBoxCenter:u.client.borderBox.center,offset:J},s={client:p,page:{selection:Z(p.selection,i.scroll.initial),borderBoxCenter:Z(p.selection,i.scroll.initial),offset:Z(p.selection,i.scroll.diff.value)}},c=Br(o.droppables).every(function($r){return!$r.isFixedOnPage}),f=Sa({draggable:u,home:d,draggables:o.draggables,viewport:i}),v=f.impact,g=f.afterCritical,m={phase:"DRAGGING",isDragging:!0,critical:n,movementMode:l,dimensions:o,initial:s,current:s,isWindowScrollAllowed:c,impact:v,afterCritical:g,onLiftImpact:v,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null};return m}if(r.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&x();var b=A({phase:"COLLECTING"},e,{phase:"COLLECTING"});return b}if(r.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||x(),Gl({state:e,published:r.payload});if(r.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;Ce(e)||x();var h=r.payload.client;return ye(h,e.current.client.selection)?e:We({state:e,clientSelection:h,impact:at(e)?e.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return Kr(e);Ce(e)||x();var D=r.payload,y=D.id,w=D.newScroll,E=e.dimensions.droppables[y];if(!E)return e;var T=ht(E,w);return Yr(e,T,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;Ce(e)||x();var $=r.payload,O=$.id,M=$.isEnabled,F=e.dimensions.droppables[O];F||x(),F.isEnabled===M&&x();var Y=A({},F,{isEnabled:M});return Yr(e,Y,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;Ce(e)||x();var _=r.payload,j=_.id,R=_.isCombineEnabled,B=e.dimensions.droppables[j];B||x(),B.isCombineEnabled===R&&x();var q=A({},B,{isCombineEnabled:R});return Yr(e,q,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;Ce(e)||x(),e.isWindowScrollAllowed||x();var H=r.payload.newScroll;if(ye(e.viewport.scroll.current,H))return Kr(e);var z=pa(e.viewport,H);return at(e)?Ia({state:e,viewport:z}):We({state:e,viewport:z})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!Ce(e))return e;var U=r.payload.maxScroll;if(ye(U,e.viewport.scroll.max))return e;var P=A({},e.viewport,{scroll:A({},e.viewport.scroll,{max:U})});return A({phase:"DRAGGING"},e,{viewport:P})}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&x();var N=xl({state:e,type:r.type});return N?We({state:e,impact:N.impact,clientSelection:N.clientSelection,scrollJumpRequest:N.scrollJumpRequest}):e}if(r.type==="DROP_PENDING"){var V=r.payload.reason;e.phase!=="COLLECTING"&&x();var ve=A({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:V});return ve}if(r.type==="DROP_ANIMATE"){var ce=r.payload,ee=ce.completed,de=ce.dropDuration,Ee=ce.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||x();var Gr={phase:"DROP_ANIMATING",completed:ee,dropDuration:de,newHomeClientOffset:Ee,dimensions:e.dimensions};return Gr}if(r.type==="DROP_COMPLETE"){var Pe=r.payload.completed;return{phase:"IDLE",completed:Pe,shouldFlush:!1}}return e},Wl=function(r){return{type:"BEFORE_INITIAL_CAPTURE",payload:r}},jl=function(r){return{type:"LIFT",payload:r}},kl=function(r){return{type:"INITIAL_PUBLISH",payload:r}},Ul=function(r){return{type:"PUBLISH_WHILE_DRAGGING",payload:r}},Hl=function(){return{type:"COLLECTION_STARTING",payload:null}},Vl=function(r){return{type:"UPDATE_DROPPABLE_SCROLL",payload:r}},ql=function(r){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:r}},zl=function(r){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:r}},Ca=function(r){return{type:"MOVE",payload:r}},Yl=function(r){return{type:"MOVE_BY_WINDOW_SCROLL",payload:r}},Kl=function(r){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:r}},Jl=function(){return{type:"MOVE_UP",payload:null}},Xl=function(){return{type:"MOVE_DOWN",payload:null}},Ql=function(){return{type:"MOVE_RIGHT",payload:null}},Zl=function(){return{type:"MOVE_LEFT",payload:null}},Et=function(){return{type:"FLUSH",payload:null}},_l=function(r){return{type:"DROP_ANIMATE",payload:r}},Pt=function(r){return{type:"DROP_COMPLETE",payload:r}},wa=function(r){return{type:"DROP",payload:r}},eu=function(r){return{type:"DROP_PENDING",payload:r}},Ea=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},ru=function(e){return function(r){var t=r.getState,n=r.dispatch;return function(a){return function(i){if(i.type!=="LIFT"){a(i);return}var o=i.payload,l=o.id,u=o.clientSelection,d=o.movementMode,p=t();p.phase==="DROP_ANIMATING"&&n(Pt({completed:p.completed})),t().phase!=="IDLE"&&x(),n(Et()),n(Wl({draggableId:l,movementMode:d}));var s={shouldPublishImmediately:d==="SNAP"},c={draggableId:l,scrollOptions:s},f=e.startPublishing(c),v=f.critical,g=f.dimensions,m=f.viewport;n(kl({critical:v,dimensions:g,clientSelection:u,movementMode:d,viewport:m}))}}}},tu=function(e){return function(){return function(r){return function(t){t.type==="INITIAL_PUBLISH"&&e.dragging(),t.type==="DROP_ANIMATE"&&e.dropping(t.payload.completed.result.reason),(t.type==="FLUSH"||t.type==="DROP_COMPLETE")&&e.resting(),r(t)}}}},At={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},qe={opacity:{drop:0,combining:.7},scale:{drop:.75}},Rt={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Se=Rt.outOfTheWay+"s "+At.outOfTheWay,je={fluid:"opacity "+Se,snap:"transform "+Se+", opacity "+Se,drop:function(r){var t=r+"s "+At.drop;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Se,placeholder:"height "+Se+", width "+Se+", margin "+Se},fn=function(r){return ye(r,J)?null:"translate("+r.x+"px, "+r.y+"px)"},it={moveTo:fn,drop:function(r,t){var n=fn(r);return n?t?n+" scale("+qe.scale.drop+")":n:null}},ot=Rt.minDropTime,Pa=Rt.maxDropTime,nu=Pa-ot,vn=1500,au=.6,iu=function(e){var r=e.current,t=e.destination,n=e.reason,a=Ue(r,t);if(a<=0)return ot;if(a>=vn)return Pa;var i=a/vn,o=ot+nu*i,l=n==="CANCEL"?o*au:o;return Number(l.toFixed(2))},ou=function(e){var r=e.impact,t=e.draggable,n=e.dimensions,a=e.viewport,i=e.afterCritical,o=n.draggables,l=n.droppables,u=ae(r),d=u?l[u]:null,p=l[t.descriptor.droppableId],s=Da({impact:r,draggable:t,draggables:o,afterCritical:i,droppable:d||p,viewport:a}),c=ne(s,t.client.borderBox.center);return c},lu=function(e){var r=e.draggables,t=e.reason,n=e.lastImpact,a=e.home,i=e.viewport,o=e.onLiftImpact;if(!n.at||t!=="DROP"){var l=xa({draggables:r,impact:o,destination:a,viewport:i,forceShouldAnimate:!0});return{impact:l,didDropInsideDroppable:!1}}if(n.at.type==="REORDER")return{impact:n,didDropInsideDroppable:!0};var u=A({},n,{displaced:He});return{impact:u,didDropInsideDroppable:!0}},uu=function(e){var r=e.getState,t=e.dispatch;return function(n){return function(a){if(a.type!=="DROP"){n(a);return}var i=r(),o=a.payload.reason;if(i.phase==="COLLECTING"){t(eu({reason:o}));return}if(i.phase!=="IDLE"){var l=i.phase==="DROP_PENDING"&&i.isWaiting;l&&x(),i.phase==="DRAGGING"||i.phase==="DROP_PENDING"||x();var u=i.critical,d=i.dimensions,p=d.draggables[i.critical.draggable.id],s=lu({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),c=s.impact,f=s.didDropInsideDroppable,v=f?xt(c):null,g=f?Nr(c):null,m={index:u.draggable.index,droppableId:u.droppable.id},b={draggableId:p.descriptor.id,type:p.descriptor.type,source:m,reason:o,mode:i.movementMode,destination:v,combine:g},h=ou({impact:c,draggable:p,dimensions:d,viewport:i.viewport,afterCritical:i.afterCritical}),D={critical:i.critical,afterCritical:i.afterCritical,result:b,impact:c},y=!ye(i.current.client.offset,h)||!!b.combine;if(!y){t(Pt({completed:D}));return}var w=iu({current:i.current.client.offset,destination:h,reason:o}),E={newHomeClientOffset:h,dropDuration:w,completed:D};t(_l(E))}}}},Aa=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function su(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}function cu(e){var r=e.onWindowScroll;function t(){r(Aa())}var n=ke(t),a=su(n),i=he;function o(){return i!==he}function l(){o()&&x(),i=ue(window,[a])}function u(){o()||x(),n.cancel(),i(),i=he}return{start:l,stop:u,isActive:o}}var du=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},pu=function(e){var r=cu({onWindowScroll:function(n){e.dispatch(Yl({newScroll:n}))}});return function(t){return function(n){!r.isActive()&&n.type==="INITIAL_PUBLISH"&&r.start(),r.isActive()&&du(n)&&r.stop(),t(n)}}},fu=function(e){var r=!1,t=!1,n=setTimeout(function(){t=!0}),a=function(o){r||t||(r=!0,e(o),clearTimeout(n))};return a.wasCalled=function(){return r},a},vu=function(){var e=[],r=function(i){var o=yt(e,function(d){return d.timerId===i});o===-1&&x();var l=e.splice(o,1),u=l[0];u.callback()},t=function(i){var o=setTimeout(function(){return r(o)}),l={timerId:o,callback:i};e.push(l)},n=function(){if(e.length){var i=[].concat(e);e.length=0,i.forEach(function(o){clearTimeout(o.timerId),o.callback()})}};return{add:t,flush:n}},gu=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.droppableId===t.droppableId&&r.index===t.index},mu=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.draggableId===t.draggableId&&r.droppableId===t.droppableId},bu=function(r,t){if(r===t)return!0;var n=r.draggable.id===t.draggable.id&&r.draggable.droppableId===t.draggable.droppableId&&r.draggable.type===t.draggable.type&&r.draggable.index===t.draggable.index,a=r.droppable.id===t.droppable.id&&r.droppable.type===t.droppable.type;return n&&a},Le=function(r,t){t()},ar=function(r,t){return{draggableId:r.draggable.id,type:r.droppable.type,source:{droppableId:r.droppable.id,index:r.draggable.index},mode:t}},Jr=function(r,t,n,a){if(!r){n(a(t));return}var i=fu(n),o={announce:i};r(t,o),i.wasCalled()||n(a(t))},hu=function(e,r){var t=vu(),n=null,a=function(c,f){n&&x(),Le("onBeforeCapture",function(){var v=e().onBeforeCapture;if(v){var g={draggableId:c,mode:f};v(g)}})},i=function(c,f){n&&x(),Le("onBeforeDragStart",function(){var v=e().onBeforeDragStart;v&&v(ar(c,f))})},o=function(c,f){n&&x();var v=ar(c,f);n={mode:f,lastCritical:c,lastLocation:v.source,lastCombine:null},t.add(function(){Le("onDragStart",function(){return Jr(e().onDragStart,v,r,ur.onDragStart)})})},l=function(c,f){var v=xt(f),g=Nr(f);n||x();var m=!bu(c,n.lastCritical);m&&(n.lastCritical=c);var b=!gu(n.lastLocation,v);b&&(n.lastLocation=v);var h=!mu(n.lastCombine,g);if(h&&(n.lastCombine=g),!(!m&&!b&&!h)){var D=A({},ar(c,n.mode),{combine:g,destination:v});t.add(function(){Le("onDragUpdate",function(){return Jr(e().onDragUpdate,D,r,ur.onDragUpdate)})})}},u=function(){n||x(),t.flush()},d=function(c){n||x(),n=null,Le("onDragEnd",function(){return Jr(e().onDragEnd,c,r,ur.onDragEnd)})},p=function(){if(n){var c=A({},ar(n.lastCritical,n.mode),{combine:null,destination:null,reason:"CANCEL"});d(c)}};return{beforeCapture:a,beforeStart:i,start:o,update:l,flush:u,drop:d,abort:p}},yu=function(e,r){var t=hu(e,r);return function(n){return function(a){return function(i){if(i.type==="BEFORE_INITIAL_CAPTURE"){t.beforeCapture(i.payload.draggableId,i.payload.movementMode);return}if(i.type==="INITIAL_PUBLISH"){var o=i.payload.critical;t.beforeStart(o,i.payload.movementMode),a(i),t.start(o,i.payload.movementMode);return}if(i.type==="DROP_COMPLETE"){var l=i.payload.completed.result;t.flush(),a(i),t.drop(l);return}if(a(i),i.type==="FLUSH"){t.abort();return}var u=n.getState();u.phase==="DRAGGING"&&t.update(u.critical,u.impact)}}}},xu=function(e){return function(r){return function(t){if(t.type!=="DROP_ANIMATION_FINISHED"){r(t);return}var n=e.getState();n.phase!=="DROP_ANIMATING"&&x(),e.dispatch(Pt({completed:n.completed}))}}},Du=function(e){var r=null,t=null;function n(){t&&(cancelAnimationFrame(t),t=null),r&&(r(),r=null)}return function(a){return function(i){if((i.type==="FLUSH"||i.type==="DROP_COMPLETE"||i.type==="DROP_ANIMATION_FINISHED")&&n(),a(i),i.type==="DROP_ANIMATE"){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var u=e.getState();u.phase==="DROP_ANIMATING"&&e.dispatch(Ea())}};t=requestAnimationFrame(function(){t=null,r=ue(window,[o])})}}}},Iu=function(e){return function(){return function(r){return function(t){(t.type==="DROP_COMPLETE"||t.type==="FLUSH"||t.type==="DROP_ANIMATE")&&e.stopPublishing(),r(t)}}}},Su=function(e){var r=!1;return function(){return function(t){return function(n){if(n.type==="INITIAL_PUBLISH"){r=!0,e.tryRecordFocus(n.payload.critical.draggable.id),t(n),e.tryRestoreFocusRecorded();return}if(t(n),!!r){if(n.type==="FLUSH"){r=!1,e.tryRestoreFocusRecorded();return}if(n.type==="DROP_COMPLETE"){r=!1;var a=n.payload.completed.result;a.combine&&e.tryShiftRecord(a.draggableId,a.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},Cu=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},wu=function(e){return function(r){return function(t){return function(n){if(Cu(n)){e.stop(),t(n);return}if(n.type==="INITIAL_PUBLISH"){t(n);var a=r.getState();a.phase!=="DRAGGING"&&x(),e.start(a);return}t(n),e.scroll(r.getState())}}}},Eu=function(e){return function(r){return function(t){if(r(t),t.type==="PUBLISH_WHILE_DRAGGING"){var n=e.getState();n.phase==="DROP_PENDING"&&(n.isWaiting||e.dispatch(wa({reason:n.reason})))}}}},Pu=Mn,Au=function(e){var r=e.dimensionMarshal,t=e.focusMarshal,n=e.styleMarshal,a=e.getResponders,i=e.announce,o=e.autoScroller;return Tn($l,Pu(fi(tu(n),Iu(r),ru(r),uu,xu,Du,Eu,wu(o),pu,Su(t),yu(a,i))))},Xr=function(){return{additions:{},removals:{},modified:{}}};function Ru(e){var r=e.registry,t=e.callbacks,n=Xr(),a=null,i=function(){a||(t.collectionStarting(),a=requestAnimationFrame(function(){a=null;var p=n,s=p.additions,c=p.removals,f=p.modified,v=Object.keys(s).map(function(b){return r.draggable.getById(b).getDimension(J)}).sort(function(b,h){return b.descriptor.index-h.descriptor.index}),g=Object.keys(f).map(function(b){var h=r.droppable.getById(b),D=h.callbacks.getScrollWhileDragging();return{droppableId:b,scroll:D}}),m={additions:v,removals:Object.keys(c),modified:g};n=Xr(),t.publish(m)}))},o=function(p){var s=p.descriptor.id;n.additions[s]=p,n.modified[p.descriptor.droppableId]=!0,n.removals[s]&&delete n.removals[s],i()},l=function(p){var s=p.descriptor;n.removals[s.id]=!0,n.modified[s.droppableId]=!0,n.additions[s.id]&&delete n.additions[s.id],i()},u=function(){a&&(cancelAnimationFrame(a),a=null,n=Xr())};return{add:o,remove:l,stop:u}}var Ra=function(e){var r=e.scrollHeight,t=e.scrollWidth,n=e.height,a=e.width,i=ne({x:t,y:r},{x:a,y:n}),o={x:Math.max(0,i.x),y:Math.max(0,i.y)};return o},Ba=function(){var e=document.documentElement;return e||x(),e},Na=function(){var e=Ba(),r=Ra({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return r},Bu=function(){var e=Aa(),r=Na(),t=e.y,n=e.x,a=Ba(),i=a.clientWidth,o=a.clientHeight,l=n+i,u=t+o,d=pe({top:t,left:n,right:l,bottom:u}),p={frame:d,scroll:{initial:e,current:e,max:r,diff:{value:J,displacement:J}}};return p},Nu=function(e){var r=e.critical,t=e.scrollOptions,n=e.registry,a=Bu(),i=a.scroll.current,o=r.droppable,l=n.droppable.getAllByType(o.type).map(function(s){return s.callbacks.getDimensionAndWatchScroll(i,t)}),u=n.draggable.getAllByType(r.draggable.type).map(function(s){return s.getDimension(i)}),d={draggables:oa(u),droppables:ia(l)},p={dimensions:d,critical:r,viewport:a};return p};function gn(e,r,t){if(t.descriptor.id===r.id||t.descriptor.type!==r.type)return!1;var n=e.droppable.getById(t.descriptor.droppableId);return n.descriptor.mode==="virtual"}var Ou=function(e,r){var t=null,n=Ru({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:e}),a=function(f,v){e.droppable.exists(f)||x(),t&&r.updateDroppableIsEnabled({id:f,isEnabled:v})},i=function(f,v){t&&(e.droppable.exists(f)||x(),r.updateDroppableIsCombineEnabled({id:f,isCombineEnabled:v}))},o=function(f,v){t&&(e.droppable.exists(f)||x(),r.updateDroppableScroll({id:f,newScroll:v}))},l=function(f,v){t&&e.droppable.getById(f).callbacks.scroll(v)},u=function(){if(t){n.stop();var f=t.critical.droppable;e.droppable.getAllByType(f.type).forEach(function(v){return v.callbacks.dragStopped()}),t.unsubscribe(),t=null}},d=function(f){t||x();var v=t.critical.draggable;f.type==="ADDITION"&&gn(e,v,f.value)&&n.add(f.value),f.type==="REMOVAL"&&gn(e,v,f.value)&&n.remove(f.value)},p=function(f){t&&x();var v=e.draggable.getById(f.draggableId),g=e.droppable.getById(v.descriptor.droppableId),m={draggable:v.descriptor,droppable:g.descriptor},b=e.subscribe(d);return t={critical:m,unsubscribe:b},Nu({critical:m,registry:e,scrollOptions:f.scrollOptions})},s={updateDroppableIsEnabled:a,updateDroppableIsCombineEnabled:i,scrollDroppable:l,updateDroppableScroll:o,startPublishing:p,stopPublishing:u};return s},Oa=function(e,r){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===r?!1:e.completed.result.reason==="DROP"},Tu=function(e){window.scrollBy(e.x,e.y)},Mu=K(function(e){return Br(e).filter(function(r){return!(!r.isEnabled||!r.frame)})}),Fu=function(r,t){var n=Ie(Mu(t),function(a){return a.frame||x(),ma(a.frame.pageMarginBox)(r)});return n},Lu=function(e){var r=e.center,t=e.destination,n=e.droppables;if(t){var a=n[t];return a.frame?a:null}var i=Fu(r,n);return i},xe={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(r){return Math.pow(r,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},Gu=function(e,r){var t=e[r.size]*xe.startFromPercentage,n=e[r.size]*xe.maxScrollAtPercentage,a={startScrollingFrom:t,maxScrollValueAt:n};return a},Ta=function(e){var r=e.startOfRange,t=e.endOfRange,n=e.current,a=t-r;if(a===0)return 0;var i=n-r,o=i/a;return o},Bt=1,$u=function(e,r){if(e>r.startScrollingFrom)return 0;if(e<=r.maxScrollValueAt)return xe.maxPixelScroll;if(e===r.startScrollingFrom)return Bt;var t=Ta({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:e}),n=1-t,a=xe.maxPixelScroll*xe.ease(n);return Math.ceil(a)},mn=xe.durationDampening.accelerateAt,bn=xe.durationDampening.stopDampeningAt,Wu=function(e,r){var t=r,n=bn,a=Date.now(),i=a-t;if(i>=bn)return e;if(i<mn)return Bt;var o=Ta({startOfRange:mn,endOfRange:n,current:i}),l=e*xe.ease(o);return Math.ceil(l)},hn=function(e){var r=e.distanceToEdge,t=e.thresholds,n=e.dragStartTime,a=e.shouldUseTimeDampening,i=$u(r,t);return i===0?0:a?Math.max(Wu(i,n),Bt):i},yn=function(e){var r=e.container,t=e.distanceToEdges,n=e.dragStartTime,a=e.axis,i=e.shouldUseTimeDampening,o=Gu(r,a),l=t[a.end]<t[a.start];return l?hn({distanceToEdge:t[a.end],thresholds:o,dragStartTime:n,shouldUseTimeDampening:i}):-1*hn({distanceToEdge:t[a.start],thresholds:o,dragStartTime:n,shouldUseTimeDampening:i})},ju=function(e){var r=e.container,t=e.subject,n=e.proposedScroll,a=t.height>r.height,i=t.width>r.width;return!i&&!a?n:i&&a?null:{x:i?0:n.x,y:a?0:n.y}},ku=na(function(e){return e===0?0:e}),Ma=function(e){var r=e.dragStartTime,t=e.container,n=e.subject,a=e.center,i=e.shouldUseTimeDampening,o={top:a.y-t.top,right:t.right-a.x,bottom:t.bottom-a.y,left:a.x-t.left},l=yn({container:t,distanceToEdges:o,dragStartTime:r,axis:Dt,shouldUseTimeDampening:i}),u=yn({container:t,distanceToEdges:o,dragStartTime:r,axis:sa,shouldUseTimeDampening:i}),d=ku({x:u,y:l});if(ye(d,J))return null;var p=ju({container:t,subject:n,proposedScroll:d});return p?ye(p,J)?null:p:null},Uu=na(function(e){return e===0?0:e>0?1:-1}),Nt=function(){var e=function(t,n){return t<0?t:t>n?t-n:0};return function(r){var t=r.current,n=r.max,a=r.change,i=Z(t,a),o={x:e(i.x,n.x),y:e(i.y,n.y)};return ye(o,J)?null:o}}(),Fa=function(r){var t=r.max,n=r.current,a=r.change,i={x:Math.max(n.x,t.x),y:Math.max(n.y,t.y)},o=Uu(a),l=Nt({max:i,current:n,change:o});return!l||o.x!==0&&l.x===0||o.y!==0&&l.y===0},Ot=function(r,t){return Fa({current:r.scroll.current,max:r.scroll.max,change:t})},Hu=function(r,t){if(!Ot(r,t))return null;var n=r.scroll.max,a=r.scroll.current;return Nt({current:a,max:n,change:t})},Tt=function(r,t){var n=r.frame;return n?Fa({current:n.scroll.current,max:n.scroll.max,change:t}):!1},Vu=function(r,t){var n=r.frame;return!n||!Tt(r,t)?null:Nt({current:n.scroll.current,max:n.scroll.max,change:t})},qu=function(e){var r=e.viewport,t=e.subject,n=e.center,a=e.dragStartTime,i=e.shouldUseTimeDampening,o=Ma({dragStartTime:a,container:r.frame,subject:t,center:n,shouldUseTimeDampening:i});return o&&Ot(r,o)?o:null},zu=function(e){var r=e.droppable,t=e.subject,n=e.center,a=e.dragStartTime,i=e.shouldUseTimeDampening,o=r.frame;if(!o)return null;var l=Ma({dragStartTime:a,container:o.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:i});return l&&Tt(r,l)?l:null},xn=function(e){var r=e.state,t=e.dragStartTime,n=e.shouldUseTimeDampening,a=e.scrollWindow,i=e.scrollDroppable,o=r.current.page.borderBoxCenter,l=r.dimensions.draggables[r.critical.draggable.id],u=l.page.marginBox;if(r.isWindowScrollAllowed){var d=r.viewport,p=qu({dragStartTime:t,viewport:d,subject:u,center:o,shouldUseTimeDampening:n});if(p){a(p);return}}var s=Lu({center:o,destination:ae(r.impact),droppables:r.dimensions.droppables});if(s){var c=zu({dragStartTime:t,droppable:s,subject:u,center:o,shouldUseTimeDampening:n});c&&i(s.descriptor.id,c)}},Yu=function(e){var r=e.scrollWindow,t=e.scrollDroppable,n=ke(r),a=ke(t),i=null,o=function(p){i||x();var s=i,c=s.shouldUseTimeDampening,f=s.dragStartTime;xn({state:p,scrollWindow:n,scrollDroppable:a,dragStartTime:f,shouldUseTimeDampening:c})},l=function(p){i&&x();var s=Date.now(),c=!1,f=function(){c=!0};xn({state:p,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:f,scrollDroppable:f}),i={dragStartTime:s,shouldUseTimeDampening:c},c&&o(p)},u=function(){i&&(n.cancel(),a.cancel(),i=null)};return{start:l,stop:u,scroll:o}},Ku=function(e){var r=e.move,t=e.scrollDroppable,n=e.scrollWindow,a=function(d,p){var s=Z(d.current.client.selection,p);r({client:s})},i=function(d,p){if(!Tt(d,p))return p;var s=Vu(d,p);if(!s)return t(d.descriptor.id,p),null;var c=ne(p,s);t(d.descriptor.id,c);var f=ne(p,c);return f},o=function(d,p,s){if(!d||!Ot(p,s))return s;var c=Hu(p,s);if(!c)return n(s),null;var f=ne(s,c);n(f);var v=ne(s,f);return v},l=function(d){var p=d.scrollJumpRequest;if(p){var s=ae(d.impact);s||x();var c=i(d.dimensions.droppables[s],p);if(c){var f=d.viewport,v=o(d.isWindowScrollAllowed,f,c);v&&a(d,v)}}};return l},Ju=function(e){var r=e.scrollDroppable,t=e.scrollWindow,n=e.move,a=Yu({scrollWindow:t,scrollDroppable:r}),i=Ku({move:n,scrollWindow:t,scrollDroppable:r}),o=function(d){if(d.phase==="DRAGGING"){if(d.movementMode==="FLUID"){a.scroll(d);return}d.scrollJumpRequest&&i(d)}},l={scroll:o,start:a.start,stop:a.stop};return l},Be="data-rbd",Ne=function(){var e=Be+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),lt=function(){var e=Be+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Xu=function(){var e=Be+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Dn={contextId:Be+"-scroll-container-context-id"},Qu=function(r){return function(t){return"["+t+'="'+r+'"]'}},Ge=function(r,t){return r.map(function(n){var a=n.styles[t];return a?n.selector+" { "+a+" }":""}).join(" ")},Zu="pointer-events: none;",_u=function(e){var r=Qu(e),t=function(){var l=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r(Ne.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:l,dragging:Zu,dropAnimating:l}}}(),n=function(){var l=`
      transition: `+je.outOfTheWay+`;
    `;return{selector:r(lt.contextId),styles:{dragging:l,dropAnimating:l,userCancel:l}}}(),a={selector:r(Xu.contextId),styles:{always:"overflow-anchor: none;"}},i={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},o=[n,t,a,i];return{always:Ge(o,"always"),resting:Ge(o,"resting"),dragging:Ge(o,"dragging"),dropAnimating:Ge(o,"dropAnimating"),userCancel:Ge(o,"userCancel")}},ie=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?S.useLayoutEffect:S.useEffect,Qr=function(){var r=document.querySelector("head");return r||x(),r},In=function(r){var t=document.createElement("style");return r&&t.setAttribute("nonce",r),t.type="text/css",t};function es(e,r){var t=L(function(){return _u(e)},[e]),n=S.useRef(null),a=S.useRef(null),i=C(K(function(s){var c=a.current;c||x(),c.textContent=s}),[]),o=C(function(s){var c=n.current;c||x(),c.textContent=s},[]);ie(function(){!n.current&&!a.current||x();var s=In(r),c=In(r);return n.current=s,a.current=c,s.setAttribute(Be+"-always",e),c.setAttribute(Be+"-dynamic",e),Qr().appendChild(s),Qr().appendChild(c),o(t.always),i(t.resting),function(){var f=function(g){var m=g.current;m||x(),Qr().removeChild(m),g.current=null};f(n),f(a)}},[r,o,i,t.always,t.resting,e]);var l=C(function(){return i(t.dragging)},[i,t.dragging]),u=C(function(s){if(s==="DROP"){i(t.dropAnimating);return}i(t.userCancel)},[i,t.dropAnimating,t.userCancel]),d=C(function(){a.current&&i(t.resting)},[i,t.resting]),p=L(function(){return{dragging:l,dropping:u,resting:d}},[l,u,d]);return p}var La=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function Mr(e){return e instanceof La(e).HTMLElement}function rs(e,r){var t="["+Ne.contextId+'="'+e+'"]',n=aa(document.querySelectorAll(t));if(!n.length)return null;var a=Ie(n,function(i){return i.getAttribute(Ne.draggableId)===r});return!a||!Mr(a)?null:a}function ts(e){var r=S.useRef({}),t=S.useRef(null),n=S.useRef(null),a=S.useRef(!1),i=C(function(c,f){var v={id:c,focus:f};return r.current[c]=v,function(){var m=r.current,b=m[c];b!==v&&delete m[c]}},[]),o=C(function(c){var f=rs(e,c);f&&f!==document.activeElement&&f.focus()},[e]),l=C(function(c,f){t.current===c&&(t.current=f)},[]),u=C(function(){n.current||a.current&&(n.current=requestAnimationFrame(function(){n.current=null;var c=t.current;c&&o(c)}))},[o]),d=C(function(c){t.current=null;var f=document.activeElement;f&&f.getAttribute(Ne.draggableId)===c&&(t.current=c)},[]);ie(function(){return a.current=!0,function(){a.current=!1;var c=n.current;c&&cancelAnimationFrame(c)}},[]);var p=L(function(){return{register:i,tryRecordFocus:d,tryRestoreFocusRecorded:u,tryShiftRecord:l}},[i,d,u,l]);return p}function ns(){var e={draggables:{},droppables:{}},r=[];function t(s){return r.push(s),function(){var f=r.indexOf(s);f!==-1&&r.splice(f,1)}}function n(s){r.length&&r.forEach(function(c){return c(s)})}function a(s){return e.draggables[s]||null}function i(s){var c=a(s);return c||x(),c}var o={register:function(c){e.draggables[c.descriptor.id]=c,n({type:"ADDITION",value:c})},update:function(c,f){var v=e.draggables[f.descriptor.id];v&&v.uniqueId===c.uniqueId&&(delete e.draggables[f.descriptor.id],e.draggables[c.descriptor.id]=c)},unregister:function(c){var f=c.descriptor.id,v=a(f);v&&c.uniqueId===v.uniqueId&&(delete e.draggables[f],n({type:"REMOVAL",value:c}))},getById:i,findById:a,exists:function(c){return!!a(c)},getAllByType:function(c){return vr(e.draggables).filter(function(f){return f.descriptor.type===c})}};function l(s){return e.droppables[s]||null}function u(s){var c=l(s);return c||x(),c}var d={register:function(c){e.droppables[c.descriptor.id]=c},unregister:function(c){var f=l(c.descriptor.id);f&&c.uniqueId===f.uniqueId&&delete e.droppables[c.descriptor.id]},getById:u,findById:l,exists:function(c){return!!l(c)},getAllByType:function(c){return vr(e.droppables).filter(function(f){return f.descriptor.type===c})}};function p(){e.draggables={},e.droppables={},r.length=0}return{draggable:o,droppable:d,subscribe:t,clean:p}}function as(){var e=L(ns,[]);return S.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var Mt=k.createContext(null),mr=function(){var e=document.body;return e||x(),e},is={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},os=function(r){return"rbd-announcement-"+r};function ls(e){var r=L(function(){return os(e)},[e]),t=S.useRef(null);S.useEffect(function(){var i=document.createElement("div");return t.current=i,i.id=r,i.setAttribute("aria-live","assertive"),i.setAttribute("aria-atomic","true"),A(i.style,is),mr().appendChild(i),function(){setTimeout(function(){var u=mr();u.contains(i)&&u.removeChild(i),i===t.current&&(t.current=null)})}},[r]);var n=C(function(a){var i=t.current;if(i){i.textContent=a;return}},[]);return n}var us=0,ss={separator:"::"};function Ft(e,r){return r===void 0&&(r=ss),L(function(){return""+e+r.separator+us++},[r.separator,e])}function cs(e){var r=e.contextId,t=e.uniqueId;return"rbd-hidden-text-"+r+"-"+t}function ds(e){var r=e.contextId,t=e.text,n=Ft("hidden-text",{separator:"-"}),a=L(function(){return cs({contextId:r,uniqueId:n})},[n,r]);return S.useEffect(function(){var o=document.createElement("div");return o.id=a,o.textContent=t,o.style.display="none",mr().appendChild(o),function(){var u=mr();u.contains(o)&&u.removeChild(o)}},[a,t]),a}var Fr=k.createContext(null);function Ga(e){var r=S.useRef(e);return S.useEffect(function(){r.current=e}),r}function ps(){var e=null;function r(){return!!e}function t(o){return o===e}function n(o){e&&x();var l={abandon:o};return e=l,l}function a(){e||x(),e=null}function i(){e&&(e.abandon(),a())}return{isClaimed:r,isActive:t,claim:n,release:a,tryAbandon:i}}var fs=9,vs=13,Lt=27,$a=32,gs=33,ms=34,bs=35,hs=36,ys=37,xs=38,Ds=39,Is=40,ir,Ss=(ir={},ir[vs]=!0,ir[fs]=!0,ir),Wa=function(e){Ss[e.keyCode]&&e.preventDefault()},Lr=function(){var e="visibilitychange";if(typeof document>"u")return e;var r=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],t=Ie(r,function(n){return"on"+n in document});return t||e}(),ja=0,Sn=5;function Cs(e,r){return Math.abs(r.x-e.x)>=Sn||Math.abs(r.y-e.y)>=Sn}var Cn={type:"IDLE"};function ws(e){var r=e.cancel,t=e.completed,n=e.getPhase,a=e.setPhase;return[{eventName:"mousemove",fn:function(o){var l=o.button,u=o.clientX,d=o.clientY;if(l===ja){var p={x:u,y:d},s=n();if(s.type==="DRAGGING"){o.preventDefault(),s.actions.move(p);return}s.type!=="PENDING"&&x();var c=s.point;if(Cs(c,p)){o.preventDefault();var f=s.actions.fluidLift(p);a({type:"DRAGGING",actions:f})}}}},{eventName:"mouseup",fn:function(o){var l=n();if(l.type!=="DRAGGING"){r();return}o.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:function(o){n().type==="DRAGGING"&&o.preventDefault(),r()}},{eventName:"keydown",fn:function(o){var l=n();if(l.type==="PENDING"){r();return}if(o.keyCode===Lt){o.preventDefault(),r();return}Wa(o)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){n().type==="PENDING"&&r()}},{eventName:"webkitmouseforcedown",fn:function(o){var l=n();if(l.type==="IDLE"&&x(),l.actions.shouldRespectForcePress()){r();return}o.preventDefault()}},{eventName:Lr,fn:r}]}function Es(e){var r=S.useRef(Cn),t=S.useRef(he),n=L(function(){return{eventName:"mousedown",fn:function(s){if(!s.defaultPrevented&&s.button===ja&&!(s.ctrlKey||s.metaKey||s.shiftKey||s.altKey)){var c=e.findClosestDraggableId(s);if(c){var f=e.tryGetLock(c,o,{sourceEvent:s});if(f){s.preventDefault();var v={x:s.clientX,y:s.clientY};t.current(),d(f,v)}}}}}},[e]),a=L(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(s){if(!s.defaultPrevented){var c=e.findClosestDraggableId(s);if(c){var f=e.findOptionsForDraggable(c);f&&(f.shouldRespectForcePress||e.canGetLock(c)&&s.preventDefault())}}}}},[e]),i=C(function(){var s={passive:!1,capture:!0};t.current=ue(window,[a,n],s)},[a,n]),o=C(function(){var p=r.current;p.type!=="IDLE"&&(r.current=Cn,t.current(),i())},[i]),l=C(function(){var p=r.current;o(),p.type==="DRAGGING"&&p.actions.cancel({shouldBlockNextClick:!0}),p.type==="PENDING"&&p.actions.abort()},[o]),u=C(function(){var s={capture:!0,passive:!1},c=ws({cancel:l,completed:o,getPhase:function(){return r.current},setPhase:function(v){r.current=v}});t.current=ue(window,c,s)},[l,o]),d=C(function(s,c){r.current.type!=="IDLE"&&x(),r.current={type:"PENDING",point:c,actions:s},u()},[u]);ie(function(){return i(),function(){t.current()}},[i])}var Ae;function Ps(){}var As=(Ae={},Ae[ms]=!0,Ae[gs]=!0,Ae[hs]=!0,Ae[bs]=!0,Ae);function Rs(e,r){function t(){r(),e.cancel()}function n(){r(),e.drop()}return[{eventName:"keydown",fn:function(i){if(i.keyCode===Lt){i.preventDefault(),t();return}if(i.keyCode===$a){i.preventDefault(),n();return}if(i.keyCode===Is){i.preventDefault(),e.moveDown();return}if(i.keyCode===xs){i.preventDefault(),e.moveUp();return}if(i.keyCode===Ds){i.preventDefault(),e.moveRight();return}if(i.keyCode===ys){i.preventDefault(),e.moveLeft();return}if(As[i.keyCode]){i.preventDefault();return}Wa(i)}},{eventName:"mousedown",fn:t},{eventName:"mouseup",fn:t},{eventName:"click",fn:t},{eventName:"touchstart",fn:t},{eventName:"resize",fn:t},{eventName:"wheel",fn:t,options:{passive:!0}},{eventName:Lr,fn:t}]}function Bs(e){var r=S.useRef(Ps),t=L(function(){return{eventName:"keydown",fn:function(i){if(i.defaultPrevented||i.keyCode!==$a)return;var o=e.findClosestDraggableId(i);if(!o)return;var l=e.tryGetLock(o,p,{sourceEvent:i});if(!l)return;i.preventDefault();var u=!0,d=l.snapLift();r.current();function p(){u||x(),u=!1,r.current(),n()}r.current=ue(window,Rs(d,p),{capture:!0,passive:!1})}}},[e]),n=C(function(){var i={passive:!1,capture:!0};r.current=ue(window,[t],i)},[t]);ie(function(){return n(),function(){r.current()}},[n])}var Zr={type:"IDLE"},Ns=120,Os=.15;function Ts(e){var r=e.cancel,t=e.getPhase;return[{eventName:"orientationchange",fn:r},{eventName:"resize",fn:r},{eventName:"contextmenu",fn:function(a){a.preventDefault()}},{eventName:"keydown",fn:function(a){if(t().type!=="DRAGGING"){r();return}a.keyCode===Lt&&a.preventDefault(),r()}},{eventName:Lr,fn:r}]}function Ms(e){var r=e.cancel,t=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(i){var o=n();if(o.type!=="DRAGGING"){r();return}o.hasMoved=!0;var l=i.touches[0],u=l.clientX,d=l.clientY,p={x:u,y:d};i.preventDefault(),o.actions.move(p)}},{eventName:"touchend",fn:function(i){var o=n();if(o.type!=="DRAGGING"){r();return}i.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:function(i){if(n().type!=="DRAGGING"){r();return}i.preventDefault(),r()}},{eventName:"touchforcechange",fn:function(i){var o=n();o.type==="IDLE"&&x();var l=i.touches[0];if(l){var u=l.force>=Os;if(u){var d=o.actions.shouldRespectForcePress();if(o.type==="PENDING"){d&&r();return}if(d){if(o.hasMoved){i.preventDefault();return}r();return}i.preventDefault()}}}},{eventName:Lr,fn:r}]}function Fs(e){var r=S.useRef(Zr),t=S.useRef(he),n=C(function(){return r.current},[]),a=C(function(f){r.current=f},[]),i=L(function(){return{eventName:"touchstart",fn:function(f){if(!f.defaultPrevented){var v=e.findClosestDraggableId(f);if(v){var g=e.tryGetLock(v,l,{sourceEvent:f});if(g){var m=f.touches[0],b=m.clientX,h=m.clientY,D={x:b,y:h};t.current(),s(g,D)}}}}}},[e]),o=C(function(){var f={capture:!0,passive:!1};t.current=ue(window,[i],f)},[i]),l=C(function(){var c=r.current;c.type!=="IDLE"&&(c.type==="PENDING"&&clearTimeout(c.longPressTimerId),a(Zr),t.current(),o())},[o,a]),u=C(function(){var c=r.current;l(),c.type==="DRAGGING"&&c.actions.cancel({shouldBlockNextClick:!0}),c.type==="PENDING"&&c.actions.abort()},[l]),d=C(function(){var f={capture:!0,passive:!1},v={cancel:u,completed:l,getPhase:n},g=ue(window,Ms(v),f),m=ue(window,Ts(v),f);t.current=function(){g(),m()}},[u,n,l]),p=C(function(){var f=n();f.type!=="PENDING"&&x();var v=f.actions.fluidLift(f.point);a({type:"DRAGGING",actions:v,hasMoved:!1})},[n,a]),s=C(function(f,v){n().type!=="IDLE"&&x();var g=setTimeout(p,Ns);a({type:"PENDING",point:v,actions:f,longPressTimerId:g}),d()},[d,n,a,p]);ie(function(){return o(),function(){t.current();var v=n();v.type==="PENDING"&&(clearTimeout(v.longPressTimerId),a(Zr))}},[n,o,a]),ie(function(){var f=ue(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return f},[])}var Ls={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function ka(e,r){if(r==null)return!1;var t=!!Ls[r.tagName.toLowerCase()];if(t)return!0;var n=r.getAttribute("contenteditable");return n==="true"||n===""?!0:r===e?!1:ka(e,r.parentElement)}function Gs(e,r){var t=r.target;return Mr(t)?ka(e,t):!1}var $s=function(e){return pe(e.getBoundingClientRect()).center};function Ws(e){return e instanceof La(e).Element}var js=function(){var e="matches";if(typeof document>"u")return e;var r=[e,"msMatchesSelector","webkitMatchesSelector"],t=Ie(r,function(n){return n in Element.prototype});return t||e}();function Ua(e,r){return e==null?null:e[js](r)?e:Ua(e.parentElement,r)}function ks(e,r){return e.closest?e.closest(r):Ua(e,r)}function Us(e){return"["+Ne.contextId+'="'+e+'"]'}function Hs(e,r){var t=r.target;if(!Ws(t))return null;var n=Us(e),a=ks(t,n);return!a||!Mr(a)?null:a}function Vs(e,r){var t=Hs(e,r);return t?t.getAttribute(Ne.draggableId):null}function qs(e,r){var t="["+lt.contextId+'="'+e+'"]',n=aa(document.querySelectorAll(t)),a=Ie(n,function(i){return i.getAttribute(lt.id)===r});return!a||!Mr(a)?null:a}function zs(e){e.preventDefault()}function or(e){var r=e.expected,t=e.phase,n=e.isLockActive;return e.shouldWarn,!(!n()||r!==t)}function Ha(e){var r=e.lockAPI,t=e.store,n=e.registry,a=e.draggableId;if(r.isClaimed())return!1;var i=n.draggable.findById(a);return!(!i||!i.options.isEnabled||!Oa(t.getState(),a))}function Ys(e){var r=e.lockAPI,t=e.contextId,n=e.store,a=e.registry,i=e.draggableId,o=e.forceSensorStop,l=e.sourceEvent,u=Ha({lockAPI:r,store:n,registry:a,draggableId:i});if(!u)return null;var d=a.draggable.getById(i),p=qs(t,d.descriptor.id);if(!p||l&&!d.options.canDragInteractiveElements&&Gs(p,l))return null;var s=r.claim(o||he),c="PRE_DRAG";function f(){return d.options.shouldRespectForcePress}function v(){return r.isActive(s)}function g(E,T){or({expected:E,phase:c,isLockActive:v,shouldWarn:!0})&&n.dispatch(T())}var m=g.bind(null,"DRAGGING");function b(E){function T(){r.release(),c="COMPLETED"}c!=="PRE_DRAG"&&(T(),c!=="PRE_DRAG"&&x()),n.dispatch(jl(E.liftActionArgs)),c="DRAGGING";function $(O,M){if(M===void 0&&(M={shouldBlockNextClick:!1}),E.cleanup(),M.shouldBlockNextClick){var F=ue(window,[{eventName:"click",fn:zs,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(F)}T(),n.dispatch(wa({reason:O}))}return A({isActive:function(){return or({expected:"DRAGGING",phase:c,isLockActive:v,shouldWarn:!1})},shouldRespectForcePress:f,drop:function(M){return $("DROP",M)},cancel:function(M){return $("CANCEL",M)}},E.actions)}function h(E){var T=ke(function(O){m(function(){return Ca({client:O})})}),$=b({liftActionArgs:{id:i,clientSelection:E,movementMode:"FLUID"},cleanup:function(){return T.cancel()},actions:{move:T}});return A({},$,{move:T})}function D(){var E={moveUp:function(){return m(Jl)},moveRight:function(){return m(Ql)},moveDown:function(){return m(Xl)},moveLeft:function(){return m(Zl)}};return b({liftActionArgs:{id:i,clientSelection:$s(p),movementMode:"SNAP"},cleanup:he,actions:E})}function y(){var E=or({expected:"PRE_DRAG",phase:c,isLockActive:v,shouldWarn:!0});E&&r.release()}var w={isActive:function(){return or({expected:"PRE_DRAG",phase:c,isLockActive:v,shouldWarn:!1})},shouldRespectForcePress:f,fluidLift:h,snapLift:D,abort:y};return w}var Ks=[Es,Bs,Fs];function Js(e){var r=e.contextId,t=e.store,n=e.registry,a=e.customSensors,i=e.enableDefaultSensors,o=[].concat(i?Ks:[],a||[]),l=S.useState(function(){return ps()})[0],u=C(function(h,D){h.isDragging&&!D.isDragging&&l.tryAbandon()},[l]);ie(function(){var h=t.getState(),D=t.subscribe(function(){var y=t.getState();u(h,y),h=y});return D},[l,t,u]),ie(function(){return l.tryAbandon},[l.tryAbandon]);for(var d=C(function(b){return Ha({lockAPI:l,registry:n,store:t,draggableId:b})},[l,n,t]),p=C(function(b,h,D){return Ys({lockAPI:l,registry:n,contextId:r,store:t,draggableId:b,forceSensorStop:h,sourceEvent:D&&D.sourceEvent?D.sourceEvent:null})},[r,l,n,t]),s=C(function(b){return Vs(r,b)},[r]),c=C(function(b){var h=n.draggable.findById(b);return h?h.options:null},[n.draggable]),f=C(function(){l.isClaimed()&&(l.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(Et()))},[l,t]),v=C(l.isClaimed,[l]),g=L(function(){return{canGetLock:d,tryGetLock:p,findClosestDraggableId:s,findOptionsForDraggable:c,tryReleaseLock:f,isLockClaimed:v}},[d,p,s,c,f,v]),m=0;m<o.length;m++)o[m](g)}var Xs=function(r){return{onBeforeCapture:r.onBeforeCapture,onBeforeDragStart:r.onBeforeDragStart,onDragStart:r.onDragStart,onDragEnd:r.onDragEnd,onDragUpdate:r.onDragUpdate}};function $e(e){return e.current||x(),e.current}function Qs(e){var r=e.contextId,t=e.setCallbacks,n=e.sensors,a=e.nonce,i=e.dragHandleUsageInstructions,o=S.useRef(null),l=Ga(e),u=C(function(){return Xs(l.current)},[l]),d=ls(r),p=ds({contextId:r,text:i}),s=es(r,a),c=C(function(O){$e(o).dispatch(O)},[]),f=L(function(){return zt({publishWhileDragging:Ul,updateDroppableScroll:Vl,updateDroppableIsEnabled:ql,updateDroppableIsCombineEnabled:zl,collectionStarting:Hl},c)},[c]),v=as(),g=L(function(){return Ou(v,f)},[v,f]),m=L(function(){return Ju(A({scrollWindow:Tu,scrollDroppable:g.scrollDroppable},zt({move:Ca},c)))},[g.scrollDroppable,c]),b=ts(r),h=L(function(){return Au({announce:d,autoScroller:m,dimensionMarshal:g,focusMarshal:b,getResponders:u,styleMarshal:s})},[d,m,g,b,u,s]);o.current=h;var D=C(function(){var O=$e(o),M=O.getState();M.phase!=="IDLE"&&O.dispatch(Et())},[]),y=C(function(){var O=$e(o).getState();return O.isDragging||O.phase==="DROP_ANIMATING"},[]),w=L(function(){return{isDragging:y,tryAbort:D}},[y,D]);t(w);var E=C(function(O){return Oa($e(o).getState(),O)},[]),T=C(function(){return Ce($e(o).getState())},[]),$=L(function(){return{marshal:g,focus:b,contextId:r,canLift:E,isMovementAllowed:T,dragHandleUsageInstructionsId:p,registry:v}},[r,g,p,b,E,T,v]);return Js({contextId:r,store:h,registry:v,customSensors:n,enableDefaultSensors:e.enableDefaultSensors!==!1}),S.useEffect(function(){return D},[D]),k.createElement(Fr.Provider,{value:$},k.createElement(hi,{context:Mt,store:h},e.children))}var Zs=0;function _s(){return L(function(){return""+Zs++},[])}function ec(e){var r=_s(),t=e.dragHandleUsageInstructions||ur.dragHandleUsageInstructions;return k.createElement(Bo,null,function(n){return k.createElement(Qs,{nonce:e.nonce,contextId:r,setCallbacks:n,dragHandleUsageInstructions:t,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var Va=function(r){return function(t){return r===t}},rc=Va("scroll"),tc=Va("auto"),wn=function(r,t){return t(r.overflowX)||t(r.overflowY)},nc=function(r){var t=window.getComputedStyle(r),n={overflowX:t.overflowX,overflowY:t.overflowY};return wn(n,rc)||wn(n,tc)},ac=function(){return!1},ic=function e(r){return r==null?null:r===document.body?ac()?r:null:r===document.documentElement?null:nc(r)?r:e(r.parentElement)},ut=function(e){return{x:e.scrollLeft,y:e.scrollTop}},oc=function e(r){if(!r)return!1;var t=window.getComputedStyle(r);return t.position==="fixed"?!0:e(r.parentElement)},lc=function(e){var r=ic(e),t=oc(e);return{closestScrollable:r,isFixedOnPage:t}},uc=function(e){var r=e.descriptor,t=e.isEnabled,n=e.isCombineEnabled,a=e.isFixedOnPage,i=e.direction,o=e.client,l=e.page,u=e.closest,d=function(){if(!u)return null;var f=u.scrollSize,v=u.client,g=Ra({scrollHeight:f.scrollHeight,scrollWidth:f.scrollWidth,height:v.paddingBox.height,width:v.paddingBox.width});return{pageMarginBox:u.page.marginBox,frameClient:v,scrollSize:f,shouldClipSubject:u.shouldClipSubject,scroll:{initial:u.scroll,current:u.scroll,max:g,diff:{value:J,displacement:J}}}}(),p=i==="vertical"?Dt:sa,s=Re({page:l,withPlaceholder:null,axis:p,frame:d}),c={descriptor:r,isCombineEnabled:n,isFixedOnPage:a,axis:p,isEnabled:t,client:o,page:l,frame:d,subject:s};return c},sc=function(r,t){var n=_n(r);if(!t||r!==t)return n;var a=n.paddingBox.top-t.scrollTop,i=n.paddingBox.left-t.scrollLeft,o=a+t.scrollHeight,l=i+t.scrollWidth,u={top:a,right:l,bottom:o,left:i},d=mt(u,n.border),p=bt({borderBox:d,margin:n.margin,border:n.border,padding:n.padding});return p},cc=function(e){var r=e.ref,t=e.descriptor,n=e.env,a=e.windowScroll,i=e.direction,o=e.isDropDisabled,l=e.isCombineEnabled,u=e.shouldClipSubject,d=n.closestScrollable,p=sc(r,d),s=dr(p,a),c=function(){if(!d)return null;var v=_n(d),g={scrollHeight:d.scrollHeight,scrollWidth:d.scrollWidth};return{client:v,page:dr(v,a),scroll:ut(d),scrollSize:g,shouldClipSubject:u}}(),f=uc({descriptor:t,isEnabled:!o,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:i,client:p,page:s,closest:c});return f},dc={passive:!1},pc={passive:!0},En=function(e){return e.shouldPublishImmediately?dc:pc};function br(e){var r=S.useContext(e);return r||x(),r}var lr=function(r){return r&&r.env.closestScrollable||null};function fc(e){var r=S.useRef(null),t=br(Fr),n=Ft("droppable"),a=t.registry,i=t.marshal,o=Ga(e),l=L(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),u=S.useRef(l),d=L(function(){return K(function(y,w){r.current||x();var E={x:y,y:w};i.updateDroppableScroll(l.id,E)})},[l.id,i]),p=C(function(){var y=r.current;return!y||!y.env.closestScrollable?J:ut(y.env.closestScrollable)},[]),s=C(function(){var y=p();d(y.x,y.y)},[p,d]),c=L(function(){return ke(s)},[s]),f=C(function(){var y=r.current,w=lr(y);y&&w||x();var E=y.scrollOptions;if(E.shouldPublishImmediately){s();return}c()},[c,s]),v=C(function(y,w){r.current&&x();var E=o.current,T=E.getDroppableRef();T||x();var $=lc(T),O={ref:T,descriptor:l,env:$,scrollOptions:w};r.current=O;var M=cc({ref:T,descriptor:l,env:$,windowScroll:y,direction:E.direction,isDropDisabled:E.isDropDisabled,isCombineEnabled:E.isCombineEnabled,shouldClipSubject:!E.ignoreContainerClipping}),F=$.closestScrollable;return F&&(F.setAttribute(Dn.contextId,t.contextId),F.addEventListener("scroll",f,En(O.scrollOptions))),M},[t.contextId,l,f,o]),g=C(function(){var y=r.current,w=lr(y);return y&&w||x(),ut(w)},[]),m=C(function(){var y=r.current;y||x();var w=lr(y);r.current=null,w&&(c.cancel(),w.removeAttribute(Dn.contextId),w.removeEventListener("scroll",f,En(y.scrollOptions)))},[f,c]),b=C(function(y){var w=r.current;w||x();var E=lr(w);E||x(),E.scrollTop+=y.y,E.scrollLeft+=y.x},[]),h=L(function(){return{getDimensionAndWatchScroll:v,getScrollWhileDragging:g,dragStopped:m,scroll:b}},[m,v,g,b]),D=L(function(){return{uniqueId:n,descriptor:l,callbacks:h}},[h,l,n]);ie(function(){return u.current=D.descriptor,a.droppable.register(D),function(){r.current&&m(),a.droppable.unregister(D)}},[h,l,m,D,i,a.droppable]),ie(function(){r.current&&i.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),ie(function(){r.current&&i.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}function _r(){}var Pn={width:0,height:0,margin:Lo},vc=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,a=r.animate;return t||a==="close"?Pn:{height:n.client.borderBox.height,width:n.client.borderBox.width,margin:n.client.margin}},gc=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,a=r.animate,i=vc({isAnimatingOpenOnMount:t,placeholder:n,animate:a});return{display:n.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:a!=="none"?je.placeholder:null}};function mc(e){var r=S.useRef(null),t=C(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),n=e.animate,a=e.onTransitionEnd,i=e.onClose,o=e.contextId,l=S.useState(e.animate==="open"),u=l[0],d=l[1];S.useEffect(function(){return u?n!=="open"?(t(),d(!1),_r):r.current?_r:(r.current=setTimeout(function(){r.current=null,d(!1)}),t):_r},[n,u,t]);var p=C(function(c){c.propertyName==="height"&&(a(),n==="close"&&i())},[n,i,a]),s=gc({isAnimatingOpenOnMount:u,animate:e.animate,placeholder:e.placeholder});return k.createElement(e.placeholder.tagName,{style:s,"data-rbd-placeholder-context-id":o,onTransitionEnd:p,ref:e.innerRef})}var bc=k.memo(mc),Gt=k.createContext(null),hc=function(e){On(r,e);function r(){for(var n,a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return n=e.call.apply(e,[this].concat(i))||this,n.state={isVisible:!!n.props.on,data:n.props.on,animate:n.props.shouldAnimate&&n.props.on?"open":"none"},n.onClose=function(){n.state.animate==="close"&&n.setState({isVisible:!1})},n}r.getDerivedStateFromProps=function(a,i){return a.shouldAnimate?a.on?{isVisible:!0,data:a.on,animate:"open"}:i.isVisible?{isVisible:!0,data:i.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!a.on,data:a.on,animate:"none"}};var t=r.prototype;return t.render=function(){if(!this.state.isVisible)return null;var a={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(a)},r}(k.PureComponent),An={dragging:5e3,dropAnimating:4500},yc=function(r,t){return t?je.drop(t.duration):r?je.snap:je.fluid},xc=function(r,t){return r?t?qe.opacity.drop:qe.opacity.combining:null},Dc=function(r){return r.forceShouldAnimate!=null?r.forceShouldAnimate:r.mode==="SNAP"};function Ic(e){var r=e.dimension,t=r.client,n=e.offset,a=e.combineWith,i=e.dropping,o=!!a,l=Dc(e),u=!!i,d=u?it.drop(n,o):it.moveTo(n),p={position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:yc(l,i),transform:d,opacity:xc(o,u),zIndex:u?An.dropAnimating:An.dragging,pointerEvents:"none"};return p}function Sc(e){return{transform:it.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function Cc(e){return e.type==="DRAGGING"?Ic(e):Sc(e)}function wc(e,r,t){t===void 0&&(t=J);var n=window.getComputedStyle(r),a=r.getBoundingClientRect(),i=Zn(a,n),o=dr(i,t),l={client:i,tagName:r.tagName.toLowerCase(),display:n.display},u={x:i.marginBox.width,y:i.marginBox.height},d={descriptor:e,placeholder:l,displaceBy:u,client:i,page:o};return d}function Ec(e){var r=Ft("draggable"),t=e.descriptor,n=e.registry,a=e.getDraggableRef,i=e.canDragInteractiveElements,o=e.shouldRespectForcePress,l=e.isEnabled,u=L(function(){return{canDragInteractiveElements:i,shouldRespectForcePress:o,isEnabled:l}},[i,l,o]),d=C(function(f){var v=a();return v||x(),wc(t,v,f)},[t,a]),p=L(function(){return{uniqueId:r,descriptor:t,options:u,getDimension:d}},[t,d,u,r]),s=S.useRef(p),c=S.useRef(!0);ie(function(){return n.draggable.register(s.current),function(){return n.draggable.unregister(s.current)}},[n.draggable]),ie(function(){if(c.current){c.current=!1;return}var f=s.current;s.current=p,n.draggable.update(p,f)},[p,n.draggable])}function Pc(e){e.preventDefault()}function Ac(e){var r=S.useRef(null),t=C(function(O){r.current=O},[]),n=C(function(){return r.current},[]),a=br(Fr),i=a.contextId,o=a.dragHandleUsageInstructionsId,l=a.registry,u=br(Gt),d=u.type,p=u.droppableId,s=L(function(){return{id:e.draggableId,index:e.index,type:d,droppableId:p}},[e.draggableId,e.index,d,p]),c=e.children,f=e.draggableId,v=e.isEnabled,g=e.shouldRespectForcePress,m=e.canDragInteractiveElements,b=e.isClone,h=e.mapped,D=e.dropAnimationFinished;if(!b){var y=L(function(){return{descriptor:s,registry:l,getDraggableRef:n,canDragInteractiveElements:m,shouldRespectForcePress:g,isEnabled:v}},[s,l,n,m,g,v]);Ec(y)}var w=L(function(){return v?{tabIndex:0,role:"button","aria-describedby":o,"data-rbd-drag-handle-draggable-id":f,"data-rbd-drag-handle-context-id":i,draggable:!1,onDragStart:Pc}:null},[i,o,f,v]),E=C(function(O){h.type==="DRAGGING"&&h.dropping&&O.propertyName==="transform"&&D()},[D,h]),T=L(function(){var O=Cc(h),M=h.type==="DRAGGING"&&h.dropping?E:null,F={innerRef:t,draggableProps:{"data-rbd-draggable-context-id":i,"data-rbd-draggable-id":f,style:O,onTransitionEnd:M},dragHandleProps:w};return F},[i,w,f,h,E,t]),$=L(function(){return{draggableId:s.id,type:s.type,source:{index:s.index,droppableId:s.droppableId}}},[s.droppableId,s.id,s.index,s.type]);return c(T,h.snapshot,$)}var qa=function(e,r){return e===r},za=function(e){var r=e.combine,t=e.destination;return t?t.droppableId:r?r.droppableId:null},Rc=function(r){return r.combine?r.combine.draggableId:null},Bc=function(r){return r.at&&r.at.type==="COMBINE"?r.at.combine.draggableId:null};function Nc(){var e=K(function(a,i){return{x:a,y:i}}),r=K(function(a,i,o,l,u){return{isDragging:!0,isClone:i,isDropAnimating:!!u,dropAnimation:u,mode:a,draggingOver:o,combineWith:l,combineTargetFor:null}}),t=K(function(a,i,o,l,u,d,p){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:u,combineWith:d,mode:i,offset:a,dimension:o,forceShouldAnimate:p,snapshot:r(i,l,u,d,null)}}}),n=function(i,o){if(i.isDragging){if(i.critical.draggable.id!==o.draggableId)return null;var l=i.current.client.offset,u=i.dimensions.draggables[o.draggableId],d=ae(i.impact),p=Bc(i.impact),s=i.forceShouldAnimate;return t(e(l.x,l.y),i.movementMode,u,o.isClone,d,p,s)}if(i.phase==="DROP_ANIMATING"){var c=i.completed;if(c.result.draggableId!==o.draggableId)return null;var f=o.isClone,v=i.dimensions.draggables[o.draggableId],g=c.result,m=g.mode,b=za(g),h=Rc(g),D=i.dropDuration,y={duration:D,curve:At.drop,moveTo:i.newHomeClientOffset,opacity:h?qe.opacity.drop:null,scale:h?qe.scale.drop:null};return{mapped:{type:"DRAGGING",offset:i.newHomeClientOffset,dimension:v,dropping:y,draggingOver:b,combineWith:h,mode:m,forceShouldAnimate:null,snapshot:r(m,f,b,h,y)}}}return null};return n}function Ya(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var Oc={mapped:{type:"SECONDARY",offset:J,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Ya(null)}};function Tc(){var e=K(function(o,l){return{x:o,y:l}}),r=K(Ya),t=K(function(o,l,u){return l===void 0&&(l=null),{mapped:{type:"SECONDARY",offset:o,combineTargetFor:l,shouldAnimateDisplacement:u,snapshot:r(l)}}}),n=function(l){return l?t(J,l,!0):null},a=function(l,u,d,p){var s=d.displaced.visible[l],c=!!(p.inVirtualList&&p.effected[l]),f=Nr(d),v=f&&f.draggableId===l?u:null;if(!s){if(!c)return n(v);if(d.displaced.invisible[l])return null;var g=Oe(p.displacedBy.point),m=e(g.x,g.y);return t(m,v,!0)}if(c)return n(v);var b=d.displacedBy.point,h=e(b.x,b.y);return t(h,v,s.shouldAnimate)},i=function(l,u){if(l.isDragging)return l.critical.draggable.id===u.draggableId?null:a(u.draggableId,l.critical.draggable.id,l.impact,l.afterCritical);if(l.phase==="DROP_ANIMATING"){var d=l.completed;return d.result.draggableId===u.draggableId?null:a(u.draggableId,d.result.draggableId,d.impact,d.afterCritical)}return null};return i}var Mc=function(){var r=Nc(),t=Tc(),n=function(i,o){return r(i,o)||t(i,o)||Oc};return n},Fc={dropAnimationFinished:Ea},Lc=Xn(Mc,Fc,null,{context:Mt,pure:!0,areStatePropsEqual:qa})(Ac);function Ka(e){var r=br(Gt),t=r.isUsingCloneFor;return t===e.draggableId&&!e.isClone?null:k.createElement(Lc,e)}function Gc(e){var r=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,t=!!e.disableInteractiveElementBlocking,n=!!e.shouldRespectForcePress;return k.createElement(Ka,A({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:t,shouldRespectForcePress:n}))}function $c(e){var r=S.useContext(Fr);r||x();var t=r.contextId,n=r.isMovementAllowed,a=S.useRef(null),i=S.useRef(null),o=e.children,l=e.droppableId,u=e.type,d=e.mode,p=e.direction,s=e.ignoreContainerClipping,c=e.isDropDisabled,f=e.isCombineEnabled,v=e.snapshot,g=e.useClone,m=e.updateViewportMaxScroll,b=e.getContainerForClone,h=C(function(){return a.current},[]),D=C(function(F){a.current=F},[]);C(function(){return i.current},[]);var y=C(function(F){i.current=F},[]),w=C(function(){n()&&m({maxScroll:Na()})},[n,m]);fc({droppableId:l,type:u,mode:d,direction:p,isDropDisabled:c,isCombineEnabled:f,ignoreContainerClipping:s,getDroppableRef:h});var E=k.createElement(hc,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(F){var Y=F.onClose,_=F.data,j=F.animate;return k.createElement(bc,{placeholder:_,onClose:Y,innerRef:y,animate:j,contextId:t,onTransitionEnd:w})}),T=L(function(){return{innerRef:D,placeholder:E,droppableProps:{"data-rbd-droppable-id":l,"data-rbd-droppable-context-id":t}}},[t,l,E,D]),$=g?g.dragging.draggableId:null,O=L(function(){return{droppableId:l,type:u,isUsingCloneFor:$}},[l,$,u]);function M(){if(!g)return null;var F=g.dragging,Y=g.render,_=k.createElement(Ka,{draggableId:F.draggableId,index:F.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(j,R){return Y(j,R,F)});return ri.createPortal(_,b())}return k.createElement(Gt.Provider,{value:O},o(T,v),M())}var et=function(r,t){return r===t.droppable.type},Rn=function(r,t){return t.draggables[r.draggable.id]},Wc=function(){var r={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=A({},r,{shouldAnimatePlaceholder:!1}),n=K(function(o){return{draggableId:o.id,type:o.type,source:{index:o.index,droppableId:o.droppableId}}}),a=K(function(o,l,u,d,p,s){var c=p.descriptor.id,f=p.descriptor.droppableId===o;if(f){var v=s?{render:s,dragging:n(p.descriptor)}:null,g={isDraggingOver:u,draggingOverWith:u?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!1,snapshot:g,useClone:v}}if(!l)return t;if(!d)return r;var m={isDraggingOver:u,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!0,snapshot:m,useClone:null}}),i=function(l,u){var d=u.droppableId,p=u.type,s=!u.isDropDisabled,c=u.renderClone;if(l.isDragging){var f=l.critical;if(!et(p,f))return t;var v=Rn(f,l.dimensions),g=ae(l.impact)===d;return a(d,s,g,g,v,c)}if(l.phase==="DROP_ANIMATING"){var m=l.completed;if(!et(p,m.critical))return t;var b=Rn(m.critical,l.dimensions);return a(d,s,za(m.result)===d,ae(m.impact)===d,b,c)}if(l.phase==="IDLE"&&l.completed&&!l.shouldFlush){var h=l.completed;if(!et(p,h.critical))return t;var D=ae(h.impact)===d,y=!!(h.impact.at&&h.impact.at.type==="COMBINE"),w=h.critical.droppable.id===d;return D?y?r:t:w?r:t}return t};return i},jc={updateViewportMaxScroll:Kl};function kc(){return document.body||x(),document.body}var Uc={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:kc},Ja=Xn(Wc,jc,null,{context:Mt,pure:!0,areStatePropsEqual:qa})($c);Ja.defaultProps=Uc;const rt=[{id:"pending",title:"Assigned",status:"pending",avatarClass:"assign-user-avatar",nextStatus:"in-process"},{id:"in-process",title:"In Process",status:"in-process",avatarClass:"assign-user-avatar avatar-process",nextStatus:"completed"},{id:"completed",title:"Completed",status:"completed",avatarClass:"assign-user-avatar avatar-complete",nextStatus:"verified"}],Hc=()=>I.jsx("div",{className:"py-5 px-3",children:I.jsx(Bn,{avatar:!0,paragraph:{rows:3},active:!0})}),Vc=()=>{var j;const e=(j=window.user)==null?void 0:j.user,[r,t]=S.useState({create:!1,filter:!1}),{loading:n,data:a}=me("project",{enablePagination:!0,defaultQueryParams:{page:1,limit:1e3}}),[i,o]=S.useState(""),[l,u]=S.useState({filter_by:"",start_at:"",end_at:"",page:1,limit:1e3}),[d,p]=S.useState("all"),[s,c]=S.useState(!1),{loading:f,postData:v}=me("update_task",{type:"submit"}),{data:g}=me("get_profile",{type:"mount",slug:`/${e._id}`,enablePagination:!1}),{data:m,loading:b,fetchApi:h,setQueryParams:D}=me("task",{enablePagination:!1,defaultQueryParams:{page:1,limit:1e3}}),y=S.useMemo(()=>{if(!i.trim()||!m)return m;const R=i.toLowerCase();return m.filter(B=>{var q,H,z,U;return((q=B.title)==null?void 0:q.toLowerCase().includes(R))||((H=B.description)==null?void 0:H.toLowerCase().includes(R))||((U=(z=B.assignee)==null?void 0:z.name)==null?void 0:U.toLowerCase().includes(R))})},[m,i]),w=R=>{t(B=>({...B,[R]:!B[R]}))},E=S.useCallback(R=>{p(R),D(B=>({...B,project_id:R==="all"||R===""?"":R}))},[D]),T=R=>{const B={...R,page:1,limit:1e3};u(B),D(B),c(!!(R.filter_by||R.start_at||R.end_at)),t(q=>({...q,filter:!1}))},$=()=>{const R={filter_by:"",start_at:"",end_at:"",page:1,limit:1e3};u(R),D(R),c(!1),t(B=>({...B,filter:!1}))},O=S.useCallback(R=>{o(R.target.value)},[]),M=async R=>{if(!R.destination)return;const{source:B,destination:q,draggableId:H}=R;if(B.droppableId===q.droppableId)return;const z=rt.find(P=>P.id===B.droppableId),U=rt.find(P=>P.id===q.droppableId);if(U.status!==z.status){const P=new FormData;P.append("status",U.status),v(P,N=>{(N==null?void 0:N.statusCode)===200&&h()},H)}},F=S.useMemo(()=>{var R;return(g==null?void 0:g.role)==="company"||((R=g==null?void 0:g.policies)==null?void 0:R.some(B=>B.module==="task"&&B.can_create))},[g]),Y=(R,B,q)=>{const H=y==null?void 0:y.filter(z=>z.status===B);return I.jsx("div",{className:"col-12 col-md-6 col-lg-4",children:I.jsx(ui,{title:R,children:b?I.jsx(Hc,{}):I.jsx(Ja,{droppableId:B,children:z=>I.jsxs("div",{...z.droppableProps,ref:z.innerRef,style:{minHeight:"100px"},children:[H!=null&&H.length?H.map((U,P)=>I.jsx(Gc,{draggableId:U._id,index:P,children:(N,V)=>I.jsx("div",{ref:N.innerRef,...N.draggableProps,...N.dragHandleProps,style:{...N.draggableProps.style,opacity:V.isDragging?.5:1},children:I.jsx(si,{className:q,...U,index:P+1})})},U._id)):I.jsx(tt,{image:tt.PRESENTED_IMAGE_SIMPLE,description:"No tasks found",className:"py-5"}),z.placeholder]})})})},B)},_=()=>I.jsxs(I.Fragment,{children:[I.jsx(ge,{type:"select",defaultValue:d,options:[{value:"all",label:"All Task"},...(a==null?void 0:a.map(R=>({value:R._id,label:R.title})))||[]],onChange:E,value:d,loading:n,showSearch:!0,style:{width:"300px"}}),I.jsx("div",{children:F&&I.jsx(Nn,{title:"+ Add New",className:"mx-auto add-new-btn me-3",onClick:()=>w("create")})}),I.jsx("div",{children:I.jsx(ge,{name:"search",placeholder:"Search Task",className:"me-2",icon:I.jsx("img",{src:"/admin/assets/img/search-icon.png",alt:"search"}),value:i,onChange:O})}),I.jsx("div",{children:I.jsx(li,{title:I.jsxs(I.Fragment,{children:["Filter",s&&I.jsx("span",{className:"filter-dot"})]}),className:"mx-auto ms-3 share-btn",icon:I.jsx("img",{src:"/admin/assets/img/filter-icon.png",alt:"filter"}),iconPosition:"right",onClick:()=>w("filter")})})]});return I.jsxs(I.Fragment,{children:[I.jsxs(ti,{children:[I.jsx(ni,{title:"Tasks",buttons:_()}),I.jsx(ec,{onDragEnd:M,children:I.jsx("div",{className:"row mb-4 mt-4",children:rt.map(({id:R,title:B,status:q,avatarClass:H})=>Y(B,q,H))})})]}),I.jsx(kt,{width:900,title:"Create Task",onCancel:()=>w("create"),open:r.create,className:"custom-modal",footer:!1,children:I.jsx(di,{onCancel:()=>w("create"),fetchTask:h})}),I.jsx(kt,{title:"Filters",onCancel:()=>w("filter"),open:r.filter,className:"custom-modal",footer:!1,children:I.jsx(oi,{onApplyFilter:T,onCancel:()=>w("filter"),isFilterRemove:$,projectdetail:"false"})})]})},ed=S.memo(Vc);export{ed as default};
