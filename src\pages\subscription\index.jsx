import React from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import CustomTabs from "../../components/shared/tabs";
import AnnualPlan from "../../components/partial/annualplan";
import MonthlyPlan from "../../components/partial/monthlyplan";
import PageTitle from "../../components/shared/pagetitle";
import "./subscription.css";

const Subscription = () => {
  const onChange = (key) => {};
  const items = [
    {
      key: "1",
      label: "Annual",
      children: <AnnualPlan />,
    },
    {
      key: "2",
      label: "Monthly",
      children: <MonthlyPlan />,
    },
  ];
  return (
    <InnerLayout>
      <PageTitle title="Subscription" />
      <div className="subscription-box mt-4">
        <div className="row">
          <div className="col-12">
            <h2 className="text-center mb-3 font-22">Select Plan</h2>
            <p className="text-center mb-4">
              Transparent pricing for teams and projects of any size
            </p>
            <CustomTabs
              onChange={onChange}
              items={items}
              className="custom-tabs"
            />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Subscription;
