import{r as p,u,a as x,j as e,L as r}from"./index-DS_hjASx.js";import{A as f}from"./auth-DgrT2eL3.js";import{u as h,F as g,B as i,c as j,a as w}from"./rules-_ZUHH97D.js";import{r as b}from"./notification-7LrlFXRF.js";const y=()=>{u();let{role:t}=x();const{loading:l,postData:c}=h("login",{type:"submit"}),m=async a=>{let o=await b();o=`web|${o}`;const s=new FormData;s.append("device","web"),s.append("device_token",o),s.append("role",t);for(const n in a)s.append(n,a[n]);c(s,d)},d=a=>{a.statusCode==200&&(window.location.href="/projects")};return e.jsx(f,{title:"Welcome back",detail:"Welcome back! Please enter your details",children:e.jsxs(g,{name:"login",layout:"vertical",onFinish:m,initialValues:{remember:!0},children:[e.jsx(i,{name:"email",placeholder:"Email",label:"Email Address",rules:j.email}),e.jsx(i,{type:"password",name:"password",id:"password",placeholder:"Password",label:"Password",rules:[{required:!0,message:"Password is required!"}]}),e.jsx("div",{className:"d-flex align-items-center justify-content-end mt-0",children:e.jsx(r,{to:"/forget-password",className:"link-color mt-1 font-600 font-14 ",children:"Forgot Password?"})}),e.jsx("div",{children:e.jsx(w,{title:"Sign In",className:"mx-auto mt-4 signin-btn mt-5",htmlType:"submit",loading:l})}),t&&t=="company"&&e.jsx("div",{children:e.jsxs("p",{className:"signup-text",children:["Don’t have an account?",e.jsx(r,{to:"/sign-up",className:"color-blue font-600 font-16 ms-1",children:"Sign up"})]})})]})})},N=p.memo(y);export{N as default};
