import { Form } from "antd";
import React, { memo, useState } from "react";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import CustomUpload from "../../shared/upload";

const AddFolder = ({
  onCancel,
  onSuccess,
  projectId,
  parentId,
  type,
  title,
}) => {
  const [form] = Form.useForm();
  const { loading: createLoading, postData: createDirectory } = useFetch(
    "create_directories",
    {
      type: "submit",
    }
  );
  const { loading: uploadLoading, postData: uploadFiles } = useFetch(
    "upload_multiple_files",
    {
      type: "submit",
    }
  );
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [resetImages, setResetImages] = useState(false);

  const onFinish = (values) => {
    const fd = new FormData();
    setResetImages(true);
    if (parentId) {
      fd.append("parent_id", parentId);
    }
    if (type && uploadedFiles?.length > 0) {
      uploadedFiles.forEach((file) => {
        fd.append("files", file); // Use files[] for each file
      });
    }
    fd.append("project_id", projectId);
    for (const key in values) {
      if (key !== "document") {
        fd.append(key, values[key]);
      }
    }
    const postData = type === "upload-pdf" ? uploadFiles : createDirectory;
    const loading = type === "upload-pdf" ? uploadLoading : createLoading;

    postData(fd, cbSuccess);
  };

  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      onCancel();
      form.resetFields();
      onSuccess();
      setTimeout(() => setResetImages(false), 0);
    }
  };

  return (
    <Form
      name="create-folder"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
    >
      {type === "upload-pdf" ? (
        <div className="mt-4">
          <Form.Item
            name="document"
            rules={[{ required: true, message: "PDF is required!" }]}
            validateTrigger="onSubmit"
          >
            <CustomUpload
              maxFiles={10}
              allowedTypes={["application/pdf"]}
              maxSizeMB={20}
              value={uploadedFiles}
              resetImages={resetImages}
              onChange={(files) => setUploadedFiles(files)}
            />
          </Form.Item>
        </div>
      ) : (
        <BaseInput
          name="title"
          placeholder=""
          label={title ? title : "Folder Name"}
          rules={[
            { required: true, message: "Sub Folder Name is required!" },
            {
              min: 2,
              message: "Sub Folder Name must be more than 2 characters",
            },
            {
              max: 40,
              message: "Sub Folder Name must not be more than 40 characters",
            },
          ]}
        />
      )}
      <div className="text-end mt-4">
        <FlatButton
          title="Add"
          className="add-new-btn"
          htmlType="submit"
          loading={type === "upload-pdf" ? uploadLoading : createLoading}
        />
      </div>
    </Form>
  );
};

export default memo(AddFolder);
