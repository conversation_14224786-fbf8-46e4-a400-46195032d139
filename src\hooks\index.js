import { useEffect, useState, useCallback, useMemo } from "react";
import HttpRequest from "../services/index";
import api from "../services/api";
import cacheService from "../services/cacheService";

export function useFetch(
  header,
  {
    type = "mount",
    slug = "",
    dependencies = [],
    enablePagination = false,
    defaultQueryParams = { page: 1, limit: 10 },
    noCache = false, // New cache control option
    cacheTTL = 5 * 60 * 1000, // Cache TTL in milliseconds (5 minutes default)
    skipNotification = false,
  } = {}
) {
  const [response, setResponse] = useState({
    loading: false,
    data: null,
    pagination: {},
    fromCache: false, // New state to track cache hits
  });
  const [queryParams, setQueryParams] = useState(defaultQueryParams);

  // Fetch API Function
  const fetchApi = useCallback(
    async (alternateSlug = slug, additionalQueryParams = {}) => {
      setResponse((prev) => ({ ...prev, loading: true }));
      const { method, url } = api[header];
      let route = `${window.constants.api_base_url}${url}`;

      // Add alternateSlug or default slug
      if (alternateSlug) {
        route += alternateSlug.startsWith("/")
          ? alternateSlug
          : `/${alternateSlug}`;
      }

      // Merge default query parameters, additional parameters, and existing queryParams
      const finalQueryParams = {
        ...queryParams,
        ...additionalQueryParams,
      };

      // Generate query string
      const queryString = new URLSearchParams(finalQueryParams).toString();
      if (queryString)
        route += route.includes("?") ? `&${queryString}` : `?${queryString}`;

      try {
        // Pass cache control options to makeRequest
        const res = await HttpRequest.makeRequest(
          method,
          route,
          undefined,
          {},
          { noCache }
        );
        console.log("res", res);
        if (res.data.statusCode === 200 || res.data.statusCode === 403) {
          setResponse({
            loading: false,
            data: res.data.data,
            pagination: res.data.pagination || {},
            fromCache: res.fromCache || false,
          });
        }
        return res.data;
      } catch (error) {
        setResponse((prev) => ({ ...prev, loading: false }));
        throw error;
      }
    },
    [header, queryParams]
  );

  // Post Data Function
  const postData = useCallback(
    async (payload, callback, alternateSlug = slug) => {
      setResponse((prev) => ({ ...prev, loading: true }));
      const { method, url } = api[header];
      let route = `${window.constants.api_base_url}${url}`;

      if (alternateSlug) route += `/${alternateSlug}`;

      try {
        // Force cache invalidation for mutations
        const res = await HttpRequest.makeRequest(
          method,
          route,
          payload,
          {},
          { noCache: true }
        );
        console.log("await HttpRequest.makeRequest", res);
        if (res.data.statusCode === 200) {
          callback && callback(res.data);
          if (!skipNotification) {
            handleSuccessNotification(res.data.message);
          }

          // Refresh the data after successful mutation if this is a list/detail view
          if (type === "mount") {
            fetchApi();
          }
        } else {
          handleErrors(res);
        }
        setResponse({ loading: false, data: res.data.data });
      } catch (error) {
        // Handle 404 errors the same way as other errors
        if (error.response?.status === 404) {
          handleErrors(error.response);
        } else {
          handleErrorNotification(error.message);
        }
      }
    },
    [header, slug, type, fetchApi, skipNotification]
  );

  // Error Handler
  const handleErrors = (res) => {
    const errorMessages = res?.data;

    if (Array.isArray(errorMessages)) {
      // For multiple errors (like bulk uploads), consolidate into one message
      const consolidatedMessage = errorMessages.join("; ");
      window.helper.sendNotification(
        "error",
        "Validation Error",
        consolidatedMessage
      );
    } else if (typeof errorMessages === "string") {
      // If `res.data` is a string, send a single notification
      window.helper.sendNotification(
        "error",
        "Validation Error",
        errorMessages
      );
    } else if (errorMessages && typeof errorMessages === "object") {
      // If `res.data` is an object, consolidate error messages
      const consolidatedErrors = Object.entries(errorMessages)
        .map(
          ([key, value]) =>
            `${key}: ${Array.isArray(value) ? value.join(", ") : value}`
        )
        .join("; ");
      window.helper.sendNotification(
        "error",
        "Validation Error",
        consolidatedErrors
      );
    } else {
      // Default case for unknown data types
      window.helper.sendNotification(
        "error",
        "Validation Error",
        "Unknown error"
      );
    }

    setResponse((prev) => ({ ...prev, loading: false }));
  };

  // Notification Handlers
  const handleErrorNotification = (message) => {
    window.helper.sendNotification("error", "Network error", message);
    setResponse((prev) => ({ ...prev, loading: false }));
  };

  const handleSuccessNotification = (message) => {
    window.helper.sendNotification("success", message, "");
  };

  // Effect to Fetch Data on Mount or Dependency Change
  useEffect(() => {
    if (type === "mount") fetchApi();

    return () => {
      setResponse({ loading: false, data: null });
    };
  }, [fetchApi, type, ...dependencies]);

  // Expose Functions and State
  return {
    loading: response.loading,
    data: response.data,
    postData,
    fetchApi,
    setQueryParams,
    queryParams,
    pagination: response.pagination,
  };
}
