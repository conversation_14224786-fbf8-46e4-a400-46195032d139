import React, { memo } from "react";
import { <PERSON> } from "react-router-dom";
import IconButton from "../button/iconbutton";
import { DateTime } from "luxon";
import moment from "moment";
import { useFetch } from "../../../hooks";
const ProjectCard = ({
  title,
  address,
  start_at,
  directories_count,
  members_count,
  file_count,
  project_status,
  type,
  completion_at,
  image_url,
  _id,
  onEdit,
  user,
  directories,
  files,
  members,
}) => {
  const defaultImage =
    "https://azizidevelopments.com/assets/images/projects/1624972383238283745.jpg";
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${window.user?.user?._id}`,
    enablePagination: false,
  });

  const handleEditClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onEdit();
  };

  return (
    <Link
      to={`/projects/${_id}`}
      className="project-card"
      style={{ textDecoration: "none" }}
    >
      <div className="project-header">
        <div className="project-header-img">
          <img
            src={image_url?.[0] || defaultImage}
            alt={title}
            style={{ width: "100%", height: "100%" }}
          />
          <div className="project-header-detail">
            <p className="color-fff font-20 mb-1">{title}</p>
            <p className="color-light-white">{address}</p>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <p className="color-light-white">
                  {moment(start_at).format("D MMM, YYYY")}
                </p>
                {/* <p className="color-light-white dot"></p>
                <p className="color-light-white">
                  {moment(start_at).format("hh:mm A")}
                </p> */}
              </div>
              {(userObj?.role === "company" ||
                (userObj?._id === user?._id &&
                  userObj?.policies?.some(
                    (policy) => policy.module === "project" && policy.can_create
                  ))) && (
                <div>
                  <IconButton
                    title="Edit"
                    icon={<img src="/admin/assets/img/btnedit-icon.png" />}
                    className="edit-btn"
                    onClick={handleEditClick}
                  />
                </div>
              )}
              {/* {userRole === "company" && (
                <div>
                  <IconButton
                    title="Edit"
                    icon={<img src="/admin/assets/img/btnedit-icon.png" />}
                    className="edit-btn"
                    onClick={handleEditClick} // Edit button opens modal
                  />
                </div>
              )} */}
            </div>
          </div>
        </div>
      </div>
      <div className="project-body">
        <div className="row">
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail mb-2">
              <p className="color-light font-12">Folders</p>
              <p className="color-dark-blue font-16">
                {directories?.length || 0}
              </p>
            </div>
          </div>
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail mb-2">
              <p className="color-light font-12">Employees:</p>
              <p className="color-dark-blue font-16">{members?.length || 0}</p>
            </div>
          </div>
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail mb-2">
              <p className="color-light font-12">Total Drawings:</p>
              <p className="color-dark-blue font-16">{files?.length || 0}</p>
            </div>
          </div>
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail">
              <p className="color-light font-12">Status</p>
              <p
                className={`font-16 ${
                  project_status === "in-process"
                    ? "color-yellow"
                    : project_status === "completed"
                    ? "color-dark-blue"
                    : ""
                }`}
              >
                {project_status === "in-process"
                  ? "In Process"
                  : project_status === "completed"
                  ? "Completed"
                  : ""}
              </p>
            </div>
          </div>
          {/* <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail">
              <p className="color-light font-12">Type:</p>
              <p className="color-dark-blue font-16">{type}</p>
            </div>
          </div> */}
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail">
              <p className="color-light font-12">Completion Date:</p>
              <p className="color-dark-blue font-16">
                {moment(completion_at).format("D MMM, YYYY")}
              </p>
            </div>
          </div>
          <div className="col-12 col-md-6 col-lg-6 col-xl-4">
            <div className="body-detail">
              <p className="color-light font-12">Created by:</p>
              <p className="color-dark-blue font-16">
                <p className="color-dark-blue font-16">
                  {user?.name
                    ? `${user.name.slice(0, 20)}${
                        user.name.length > 20 ? "..." : ""
                      }`
                    : ""}
                  {user?._id === window.user.user._id ? " (me)" : ""}
                </p>
              </p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default memo(ProjectCard);
