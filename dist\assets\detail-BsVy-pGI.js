import{r as l,u as J,m as K,j as e,h as xe,R as _e,a as je,n as be}from"./index-C6D6r1Zc.js";import{S as ne,a as we,d as ye,I as ve,P as Ne}from"./index-D5NY69qa.js";import{B as Se}from"./backbutton-CVjBhEnB.js";import{F as te,u as P,d as H,B as R,f as $,a as q,E as Ce,e as Fe,I as Ae}from"./rules-CIBIafmf.js";import{P as Pe,_ as Ee,w as Te,a as Me}from"./assigntask-ByVP1_vo.js";import{S as Re}from"./index-BzLA3ZKF.js";import"./index-BiW9UDU7.js";import"./notification-C5lHSl-d.js";const $e=(s,d)=>{const[i,t]=l.useState([]),[p,f]=l.useState(!0);return l.useEffect(()=>{if(!(!s||!d))return f(!0),s.emit("load_conversations",{task_id:d},c=>{(c==null?void 0:c.statusCode)===200?t(c.data):t([]),f(!1)}),()=>{t([]),f(!0)}},[s,d]),{messages:i,loading:p}},Ie=({data:s})=>{const d=J(),i=window.socket,t=window.user.user,{messages:p,loading:f}=$e(i,s==null?void 0:s._id),[c,u]=l.useState(p||[]),r=l.useRef(null),_=()=>{var a;(a=r.current)==null||a.scrollIntoView({behavior:"smooth"})};l.useEffect(()=>{p&&p.length>0&&u(p)},[p]),l.useEffect(()=>(i&&i.on("message",a=>{a.statusCode===200?u(o=>{const N={_id:a.data.task_id,message:a.data.message,type:a.data.type,url:a.data.url,user:a.data.user,created_at:a.data.created_at};return o.some(w=>w._id===a._id)?o:[...o,N]}):(K.error("This task has been deleted, you cannot send messages now."),d("/assign-task"))}),()=>{i==null||i.off("message")}),[i]),l.useEffect(()=>{_()},[c]);const h=a=>{const o=a.message;return a.type==="image"&&a.url?e.jsx("img",{src:a.url,alt:"attachment",className:"message-img",style:{maxWidth:"100%",maxHeight:"200px",borderRadius:"8px"}}):a.type==="text"?e.jsx("span",{className:"message-text",children:o}):null},x=a=>{var w,E;const o=((w=a.user)==null?void 0:w._id)===(t==null?void 0:t._id),N=xe(a.created_at).format("D/M/YY h:mm A"),b=`${a._id}-${a.created_at}`;return e.jsxs("div",{className:`message-wrapper ${o?"own":"other"}`,children:[!o&&e.jsx("div",{className:"avatar",children:window.helper.getInitials((E=a.user)==null?void 0:E.name)}),e.jsxs("div",{className:`message-bubble ${o?"own":"other"}`,children:[h(a),e.jsx("div",{className:"message-time",children:N})]})]},b)},y=[...c].sort((a,o)=>new Date(a.created_at)-new Date(o.created_at));return e.jsx("div",{className:"chat-container",children:f?e.jsx("div",{className:"skeleton-wrapper",children:Array.from({length:4}).map((a,o)=>e.jsx(ne,{avatar:!0,paragraph:{rows:1},active:!0},o))}):e.jsxs(e.Fragment,{children:[y.map(x),e.jsx("div",{ref:r})]})})},ke=l.memo(Ie),Be=({isOpen:s,onClose:d,taskData:i,onSuccess:t,fetchApi:p})=>{var ee,se;const f=(se=(ee=window.user)==null?void 0:ee.user)==null?void 0:se._id,[c]=te.useForm(),[u,r]=l.useState(null),[_,h]=l.useState(null),[x,y]=l.useState(null),[a,o]=l.useState([]),[N,b]=l.useState(null),[w,E]=l.useState(null),[g,S]=l.useState(!1),[Y,L]=l.useState([]),U=l.useRef([]),{loading:ae,postData:le}=P("update_task",{type:"submit"}),{loading:G,data:I}=P("project",{enablePagination:!0,defaultQueryParams:{page:1,limit:1e3}}),O={type:"delay"},{loading:re,data:T,fetchApi:Q}=P("directories",O),{loading:oe,data:M,fetchApi:X}=P("directories",O),{loading:Z,data:F,fetchApi:W}=P("directories",O);l.useEffect(()=>{var n,m,v,A,k,z,D;if(i){const j=((m=(n=i.project.members)==null?void 0:n.filter(C=>C._id!==f))==null?void 0:m.map(C=>({value:C._id,label:C.name})))||[];L(j);const B=(A=(v=i.assignees)==null?void 0:v.filter(C=>C._id._id!==f))==null?void 0:A.map(C=>({value:C._id._id,label:C._id.name})),ge=(B==null?void 0:B.map(C=>C.value))||[];if(U.current=ge,c.setFieldsValue({title:i.title,project_id:(k=i.project)==null?void 0:k._id,assignees:B,description:i.description,start_at:i.start_at?H(i.start_at):null,end_at:i.end_at?H(i.end_at):null}),r((z=i.project)==null?void 0:z._id),i.root_directory&&b(i.root_directory),i.sub_directory&&E(i.sub_directory),(D=i==null?void 0:i.directories)!=null&&D.length){const C=i.directories.map(fe=>fe._id._id);o(C)}else o([])}},[i,c,f]),l.useEffect(()=>{u&&(Q(`/?project_id=${u}&page=1&limit=1000`),N||(h(null),V("root_directory",null,{sub_directory:null}),S(!1)))},[u,N,Q]),l.useEffect(()=>{u&&_&&(X(`/?project_id=${u}&parent_id=${_}&page=1&limit=1000`),w||(V("sub_directory",null),S(!1)))},[_,u,w,X]),l.useEffect(()=>{if(T!=null&&T.length&&N){const n=T.find(m=>m._id===N);n&&(h(n._id),c.setFieldsValue({root_directory:n._id}))}},[T,N,c]),l.useEffect(()=>{if(M!=null&&M.length&&w){const n=M.find(m=>m._id===w);n&&(y(n._id),c.setFieldsValue({sub_directory:n._id}),!g&&w&&u&&(W(`/?project_id=${u}&parent_id=${w}&page=1&limit=1000`),S(!0)))}},[M,w,c,u,g,W]);const V=(n,m,v={})=>{const A={[n]:m,...v};c.setFieldsValue(A)},ce=n=>{r(n),h(null),V("root_directory",null,{sub_directory:null}),b(null),E(null),o([]),S(!1),c.setFieldsValue({assignees:[]});const m=I==null?void 0:I.find(v=>v._id===n);m!=null&&m.members?L(m.members.filter(v=>v._id!==f).map(v=>({value:v._id,label:v.name}))):L([])},de=n=>{h(n),V("sub_directory",null),E(null),o([]),S(!1)},ue=n=>{var m;y(n),n&&(!(F!=null&&F.length)||((m=F[0])==null?void 0:m.parent_id)!==n)&&!Z&&u&&(W(`/?project_id=${u}&parent_id=${n}&page=1&limit=1000`),S(!0))},me=n=>m=>{o(v=>m.target.checked?[...v,n]:v.filter(A=>A!==n))},pe=async n=>{var D;const m=new FormData;["start_at","end_at"].forEach(j=>{n[j]&&m.append(j,H(n[j]).format("YYYY-MM-DD"))});const v=((D=n.assignees)==null?void 0:D.map(j=>typeof j=="object"?j.value:j).filter(Boolean))||[],A=U.current.includes(f)?[...v,f]:v,k=U.current||[];JSON.stringify(A.sort())!==JSON.stringify(k.sort())&&(A.forEach(j=>{k.includes(j)||m.append("assignees[]",j)}),k.forEach(j=>{A.includes(j)||m.append("_assignees[]",j)})),n.root_directory&&m.append("root_directory",n.root_directory),n.sub_directory&&m.append("sub_directory",n.sub_directory),a.length?a.forEach(j=>{m.append("directories[]",j)}):m.append("directories[]",""),Object.entries(n).forEach(([j,B])=>{["start_at","end_at","assignees","root_directory","sub_directory"].includes(j)||m.append(j,B)}),le(m,j=>{j.statusCode===200&&(d(),p(),t&&t())},i._id)},he=()=>x?e.jsxs("div",{className:"col-12",children:[e.jsx("label",{className:"color-black font-600 mt-3 mb-3",children:"Select Drawing PDF"}),Z?e.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:[...Array(5)].map((n,m)=>e.jsx("div",{style:{margin:"10px"},children:e.jsx(ne.Image,{active:!0,style:{width:100,height:100}})},m))}):F!=null&&F.length?e.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:F==null?void 0:F.map(n=>e.jsx(Pe,{pdfUrl:n.file,checked:Array.isArray(a)&&a.includes(n._id),onChange:me(n._id)},n._id))}):e.jsx(Ce,{description:"No drawings found"})]}):null;return e.jsxs(te,{form:c,onFinish:pe,layout:"vertical",className:"row",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"title",label:"Title",rules:$.title})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"project_id",type:"select",label:"Project",options:I==null?void 0:I.map(n=>({value:n._id,label:n.title})),onChange:ce,loading:G,rules:$.project_id,showSearch:!0,disabled:!0})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"root_directory",type:"select",label:"Select Folder",options:T==null?void 0:T.map(n=>({value:n._id,label:n.title})),rules:$.root_directory,loading:re,onChange:de,disabled:!u})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"sub_directory",type:"select",label:"Select Sub Folder",options:M==null?void 0:M.map(n=>({value:n._id,label:n.title})),rules:$.sub_directory,loading:oe,disabled:!_,onChange:ue})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"assignees",type:"select",label:"Assignee",options:Y,mode:"multiple",loading:G,rules:$.assignees,disabled:!u})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"start_at",type:"datepiker",label:"Start Date",rules:$.start_at,disablePastDates:!0,format:"YYYY-MM-DD"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"end_at",type:"datepiker",label:"End Date",rules:$.end_at,disablePastDates:!0,format:"YYYY-MM-DD"})})]})}),e.jsx("div",{className:"col-12",children:e.jsx(R,{name:"description",type:"textarea",label:"Description",rows:"5",rules:$.description})}),he()]}),e.jsx("div",{className:"col-12 text-end mt-4",children:e.jsx(q,{title:"Update",className:"add-new-btn",loading:ae,htmlType:"submit"})})]})},Ye=l.memo(Be),De=({data:s,fetchApi:d})=>{var x,y,a;const i=J(),t=(x=window.user)==null?void 0:x.user,{data:p}=P("get_profile",{type:"mount",slug:`/${t._id}`,enablePagination:!1}),[f,c]=l.useState(!1),{postData:u}=P("delete_task",{type:"submit"}),{showAlert:r}=Fe(),_=async(o,N)=>{(await r({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&u("",h,o)},h=o=>{o.statusCode===200&&i("/assign-task")};return e.jsxs(_e.Fragment,{children:[e.jsxs("div",{className:"chat-header d-flex justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"assign-user-avatar "+((s==null?void 0:s.status)==="in-process"?"avatar-process":(s==null?void 0:s.status)==="completed"?"avatar-complete":(s==null?void 0:s.status)==="verified"?"avatar-verified":""),children:e.jsx("p",{children:window.helper.getInitials(s==null?void 0:s.title)})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-12 color-light",children:["ID: ",s==null?void 0:s.short_id," | ",(y=s==null?void 0:s.project)==null?void 0:y.title]}),e.jsx("p",{className:"font-14 color-black",children:s==null?void 0:s.title})]})]}),((p==null?void 0:p.role)==="company"||((a=p==null?void 0:p.policies)==null?void 0:a.some(o=>o.module==="task"&&o.can_create)))&&e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{className:"me-4 cursor-pointer",src:"/admin/assets/img/chat-delete-icon.png",alt:"Delete",onClick:o=>_(s._id)}),e.jsx("img",{className:"cursor-pointer",src:"/admin/assets/img/chat-edit-icon.png",alt:"Edit",onClick:o=>c(!0)})]})]}),e.jsx(we,{width:900,title:"Edit Task",onCancel:o=>c(!1),open:f,className:"custom-modal",footer:!1,children:e.jsx(Ye,{onClose:o=>c(!1),taskData:s,fetchApi:d})})]})},Ve=(s,d,i)=>l.useCallback(({message:p,type:f="text",url:c=""})=>{if(!s||!d||!i)return;const u={room_id:d,message:p,task_id:i,type:f,url:c};s.emit("message",u,r=>{console.log("✅ Server response:",r)})},[s,d,i]),{TextArea:Le}=Ae,Ue=({taskId:s})=>{const d=l.useRef(),{loading:i,postData:t}=P("upload_media",{type:"submit",skipNotification:!0}),p=window.socket,f=`task_${s}`,c=Ve(p,f,s),[u,r]=l.useState(""),[_,h]=l.useState(null),[x,y]=l.useState(null),a=async()=>{if(u.trim()){const g=u.trim();c({message:g,type:"text"}),r("")}if(_){const g=new FormData;g.append("file",_),g.append("type","image"),t(g,b)}},o=()=>{var g;(g=d.current)==null||g.click()},N=g=>{const S=g.target.files[0];if(S)if(S.type.startsWith("image/")){h(S);const Y=new FileReader;Y.onloadend=()=>y(Y.result),Y.readAsDataURL(S)}else alert("Only image files are accepted");g.target.value=""},b=g=>{var S;(g==null?void 0:g.statusCode)===200&&((S=g.data)!=null&&S.url)&&c({type:"image",url:g.data.url,message:""}),h(null),y(null)},w=g=>{if(g.key==="Enter"){if(g.shiftKey)return;g.preventDefault(),a()}},E=()=>{h(null),y(null)};return e.jsxs("div",{className:"chat-footer d-flex flex-column w-100",children:[x&&e.jsxs("div",{className:"preview-box d-flex align-items-center justify-content-center mb-2 p-2",style:{position:"relative",width:200,height:200},children:[e.jsx("button",{onClick:E,style:{position:"absolute",top:0,right:0,background:"#fff",border:"1px solid #ccc",borderRadius:"50%",width:20,height:20,fontSize:12,lineHeight:"16px",textAlign:"center",cursor:"pointer",zIndex:10},children:"✕"}),e.jsx("img",{src:x,alt:"preview",style:{maxHeight:"100%",maxWidth:"100%",objectFit:"contain"}})]}),e.jsx("input",{type:"file",accept:"image/*",ref:d,style:{display:"none"},onChange:N}),e.jsxs("div",{className:"d-flex align-items-center w-100",children:[e.jsxs("div",{style:{position:"relative",width:"100%"},children:[e.jsx(Le,{placeholder:_&&!x?_.name:"Enter message here....",value:_?"":u,onChange:g=>r(g.target.value),onKeyDown:w,disabled:i,autoSize:{minRows:3,maxRows:3},style:{paddingRight:"30px"}}),e.jsx("img",{src:"/admin/assets/img/input-icon.png",alt:"input icon",style:{position:"absolute",right:"8px",top:"50%",transform:"translateY(-50%)",cursor:"pointer"},onClick:o})]}),e.jsx(q,{title:i?"Sending...":"Send",className:"add-new-btn ms-2",onClick:a,disabled:i})]})]})},Oe=l.memo(Ue),We=({data:s,fetchApi:d})=>e.jsxs("div",{className:"chat-box",children:[e.jsx(De,{data:s,fetchApi:d}),e.jsx("div",{className:"chat-body",children:e.jsx(ke,{data:s})}),e.jsx(Oe,{taskId:s==null?void 0:s._id})]}),ze=l.memo(We);Ee.workerSrc=Te;const He=({pdfUrl:s})=>{const d=l.useRef(null),[i,t]=l.useState(""),[p,f]=l.useState(!1);return l.useEffect(()=>{(async()=>{const r=await(await Me(s).promise).getPage(1),_=r.getViewport({scale:.3}),h=d.current,x=h.getContext("2d");h.width=_.width,h.height=_.height,await r.render({canvasContext:x,viewport:_}).promise,t(h.toDataURL())})()},[s]),e.jsxs("div",{children:[e.jsx("canvas",{ref:d,style:{display:"none"}}),i&&e.jsx(ye,{preview:{visible:p,src:i,alt:"PDF Page 1",onVisibleChange:c=>f(c)},src:i,alt:"PDF Page 1"})]})},Ke=l.memo(He),ie=({direction:s,onClick:d})=>e.jsx("button",{className:`custom-arrow-btn custom-arrow-${s}`,onClick:d,children:s==="prev"?"<":">"}),Je=({children:s,attributes_img:d,id:i,btn_title:t,btn_className:p,postData:f,fetchApi:c,status:u,directories:r,pdfTitle:_})=>{const[h,x]=l.useState(0),y=()=>{switch(u){case"pending":return"in-process";case"in-process":return"completed";default:return null}},a={dots:!1,arrows:!0,infinite:!1,speed:500,slidesToShow:1,slidesToScroll:1,arrows:!0,adaptiveHeight:!0,beforeChange:(b,w)=>x(w),prevArrow:(r==null?void 0:r.length)>1&&h>0&&e.jsx(ie,{direction:"prev"}),nextArrow:(r==null?void 0:r.length)>1&&h<r.length-1&&e.jsx(ie,{direction:"next"})},o=async()=>{const b=y();if(!b)return;const w=new FormData;w.append("status",b),f(w,N,i)},N=b=>{(b==null?void 0:b.statusCode)===200&&c()};return e.jsxs("div",{className:"task-attributes",children:[e.jsx("div",{className:"attributes-header",children:e.jsx("p",{children:"Task Attributes"})}),e.jsxs("div",{className:"attributes-body",children:[(r==null?void 0:r.length)>0&&e.jsx("div",{className:"attributes-slider",children:e.jsx(Re,{...a,children:r.map((b,w)=>e.jsxs("div",{className:"slider-item",children:[e.jsx("div",{className:"attributes-img text-center",children:e.jsx(Ke,{pdfUrl:b._id.file})}),e.jsx("p",{className:"font-16 color-light text-center",children:b._id.title}),e.jsx("div",{className:"text-center mt-3",children:e.jsx("a",{href:b._id.file,title:"View PDF",className:"view-pdf-btn text-white",target:"_blank",children:"View Pdf"})})]},b._id._id||w))})}),s,y()&&e.jsx("div",{className:"text-center mt-3",children:e.jsx(q,{title:t,onClick:o,className:`attributes-btn ${p}`})})]})]})},qe=l.memo(Je),Ge=({iconSrc:s,label:d,value:i})=>e.jsxs("div",{className:"attributes-items d-flex align-items-center justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:s,alt:""}),e.jsx("p",{className:"font-14 color-light ms-2",children:d})]}),e.jsx("p",{className:"color-black ms-5",style:{wordBreak:"break-all"},children:i})]}),Qe=()=>{var _;const{id:s}=je(),d=J(),{loading:i,data:t,fetchApi:p}=P("task",{type:"mount",slug:`/${s}`}),{postData:f}=P("update_task",{type:"submit"});l.useEffect(()=>{i||(async()=>{var x,y;try{await p()==="Record not found"&&(K.error("This content is no longer available."),d("/assign-task"))}catch(a){((y=(x=a==null?void 0:a.response)==null?void 0:x.data)==null?void 0:y.message)==="Record not found"&&(K.error("This content is no longer available."),d("/assign-task"))}})()},[s,i,d,p]);const c=l.useMemo(()=>{var h;return[{iconSrc:"/admin/assets/img/status-icon.png",label:"Status",value:window.helper.capitalizeFirstLetter(t==null?void 0:t.status)},{iconSrc:"/admin/assets/img/assignee-icon.png",label:"Assignees",value:((h=t==null?void 0:t.assignees)==null?void 0:h.map(x=>x._id.name).join(", "))||"-"},{iconSrc:"/admin/assets/img/date-icon.png",label:"Start Date",value:window.helper.formatDate(t==null?void 0:t.start_at)},{iconSrc:"/admin/assets/img/date-icon.png",label:"End Date",value:window.helper.formatDate(t==null?void 0:t.end_at)},{iconSrc:"/admin/assets/img/category-img.png",label:"Description",value:(t==null?void 0:t.description)||"-"}]},[t]),u=l.useMemo(()=>{var y;const h={pending:{title:"Start",className:"btn-primary"},in_process:{title:"Complete",className:"btn-warning"},completed:{title:"Complete",className:"btn-success"}},x=(y=t==null?void 0:t.status)==null?void 0:y.replace("-","_");return x==="verified"?null:h[x]||h.pending},[t==null?void 0:t.status]);if(i)return e.jsx(be,{});if(!t)return null;const r=(_=t==null?void 0:t.directories[0])==null?void 0:_._id;return e.jsxs(ve,{children:[e.jsx(Ne,{title:e.jsx(Se,{})}),t&&e.jsxs("div",{className:"row mb-5",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-7 col-xl-8 mt-4",children:e.jsx(ze,{data:t,fetchApi:p})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-5 col-xl-4 mt-4",children:e.jsx(qe,{attributes_img:r==null?void 0:r.file,id:t==null?void 0:t._id,btn_title:u.title,btn_className:u.className,postData:f,status:t==null?void 0:t.status,fetchApi:p,pdfTitle:r==null?void 0:r.title,directories:t==null?void 0:t.directories,children:c.map((h,x)=>e.jsx(Ge,{...h},x))})})]})]})},ls=l.memo(Qe);export{ls as default};
