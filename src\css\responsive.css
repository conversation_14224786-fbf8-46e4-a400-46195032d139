

@media screen and (max-width: 1400px) {
  .auth-box {
    margin-top: 70px;
  }
  .auth-logo img {
    width: 240px;
  }

  .auth-logo {
    padding-left: 50px;
  }

  .font-36 {
    font-size: 30px !important;
  }
  .auth-box p {
    font-size: 12px;
  }
  .signin-btn {
    font-size: 14px;
  }
  .font-12 {
    font-size: 10px;
}

.font-20 {
    font-size: 18px !important;
}

.project-header-detail p {
    font-size: 12px;
}

.ant-menu-item {
    margin-bottom: 10px !important;
}

.ant-menu-item a {
    font-size: 14px;
}
.content-header h2 {
  font-size: 22px;
}
.suscription-card ul>li {
  font-size: 12px;
}
}

@media screen and (max-width: 1200px) {
  .auth-sidebar {
    width: 55%;
}
.suscription-active-btn {
  padding: 5px 15px;
  font-size: 12px;
}

.suscription-btn {
  padding: 5px 15px;
  font-size: 12px;
}

.suscription-business-btn {
  padding: 5px 15px;
  font-size: 12px;
}
.font-36 {
  font-size: 26px !important;
}

.font-90 {
  font-size: 65px;
}
.suscription-card {
    margin-top: 10px;
    min-height: 450px;
}
.share-btn {
  padding: 4px 15px;
  font-size: 12px;
}

.share-btn img {
  width: 18px;
}
.content-header .ant-btn.add-new-btn,
.content-header .ant-btn.share-btn{
  margin-left: 10px !important;
}

.content-header-child .ant-input-affix-wrapper {
  height: 33px;
}

.content-header-child .ant-input-affix-wrapper img {
  width: 18px;
}
.content-header h2 {
  margin-right: 10px;
}
.ant-select-selector {
  height: 33px !important;
  margin-top: 3px !important;
}
}

@media screen and (max-width: 991px) {
  .auth-sidebar {
    width: 50%;
}
aside.ant-layout-sider {
  flex: 0 0 150px !important;
  max-width: 150px !important;
  min-width: 150px !important;
  width: 150px !important;
}
.ant-layout {
  margin-inline-start: 74px !important;
}
.ant-menu-item a {
  font-size: 12px;
}

.ant-menu-item img {
  width: 22px;
}

.ant-menu-item {
  margin-bottom: 5px !important;
}
.content-header {
  flex-wrap: wrap;
 
}

.content-header-child {flex-wrap: wrap;align-items: end !important;    width: 100%;
  justify-content: space-between;margin-top: 10px;}
.filter-btn {
  font-size: 12px;
  padding: 4px 25px;
  border-radius: 8px !important;
}

.add-new-btn {
  padding: 4px 25px;
  font-size: 12px;
}
.reset-btn{
  padding: 4px 25px;
  font-size: 12px;
}
.content-header .ant-btn{
  margin: 10px 10px 0 0 !important;
}
.font-36 {
  font-size: 22px !important;
}

.font-90 {
  font-size: 50px;
}
.suscription-card {
  margin-top: 10px;
  min-height: 429px;
}
.demo-logo-vertical .logo-text h2 {
  display: none;
}
.demo-logo-vertical {
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-body .d-flex {
  flex-wrap: wrap;
}

}

@media screen and (max-width: 767px) {
  .auth-sidebar {
    display: none !important;
}


aside.ant-layout-sider {
    flex: 0 0 55px !important;
    max-width: 55px !important;
    min-width: 55px !important;
    width: 55px !important;
}

.ant-menu-title-content {
    display: none;
}

.ant-menu-item img {
    width: 20px;
}

.ant-menu-item {
    margin-bottom: 4px !important;
}

.ant-layout {
    margin-inline-start: 25px !important;
}
.content-header {
  flex-wrap: wrap;
  justify-content: start;
}

.content-header-child {flex-wrap: wrap;align-items: end !important;    width: 100%;
  justify-content: space-between;margin-top: 10px;}

.filter-btn {
  font-size: 12px;
  padding: 4px 25px;
  border-radius: 8px !important;
}

.add-new-btn {
  padding: 4px 25px;
  font-size: 12px;
}

.ant-btn {
  border-radius: 8px !important;
}
.font-36 {
  font-size: 18px !important;
}

.font-90 {
  font-size: 30px;
}
.suscription-card {
  margin-top: 10px;
  min-height: 309px;
}


.content-header-child .ms-3 {
    margin-left: 0 !important;
}
.demo-logo-vertical img{
  width: 30px;
}

/* .content-header .add-new-btn {
  margin: 10px 10px 0 0 !important;
} */
}
