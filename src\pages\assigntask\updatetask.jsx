import React, { memo, useState, useEffect, useRef } from "react";
import { Form, Skeleton, Empty } from "antd";
import dayjs from "dayjs";
import { useFetch } from "../../hooks";
import PdfFirstPageThumbnail from "../../components/shared/pdftoimage";
import BaseInput from "../../components/shared/inputs";
import FlatButton from "../../components/shared/button/flatbutton";
import { create_task } from "../../config/rules";

const UpdateTask = ({ isOpen, onClose, taskData, onSuccess, fetchApi }) => {
  const currentUserId = window.user?.user?._id;
  const [form] = Form.useForm();
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [selectedSubFolder, setSelectedSubFolder] = useState(null);
  const [checkedItems, setCheckedItems] = useState([]);
  const [initialRootDir, setInitialRootDir] = useState(null);
  const [initialSubDir, setInitialSubDir] = useState(null);
  const [pdfFetched, setPdfFetched] = useState(false);
  const [projectAssignees, setProjectAssignees] = useState([]);
  const initialAssigneesRef = useRef([]);
  const { loading, postData } = useFetch("update_task", { type: "submit" });
  const { loading: projectLoading, data: projectData } = useFetch("project", {
    enablePagination: true,
    defaultQueryParams: { page: 1, limit: 1000 },
  });

  const baseDirectoriesParams = { type: "delay" };
  const {
    loading: folderLoading,
    data: folderData,
    fetchApi: fetchFolders,
  } = useFetch("directories", baseDirectoriesParams);
  const {
    loading: subFolderLoading,
    data: subFolderData,
    fetchApi: fetchSubFolders,
  } = useFetch("directories", baseDirectoriesParams);
  const {
    loading: pdfLoading,
    data: pdfData,
    fetchApi: fetchPdf,
  } = useFetch("directories", baseDirectoriesParams);

  useEffect(() => {
    if (taskData) {
      const projectMembers =
        taskData.project.members
          ?.filter((member) => member._id !== currentUserId)
          ?.map((member) => ({
            value: member._id,
            label: member.name,
          })) || [];
      setProjectAssignees(projectMembers);
      const filteredAssignees = taskData.assignees
        ?.filter((assignee) => assignee._id._id !== currentUserId)
        ?.map((assignee) => ({
          value: assignee._id._id,
          label: assignee._id.name,
        }));
      const assigneeIds =
        filteredAssignees?.map((assignee) => assignee.value) || [];
      initialAssigneesRef.current = assigneeIds;
      form.setFieldsValue({
        title: taskData.title,
        project_id: taskData.project?._id,
        assignees: filteredAssignees,
        description: taskData.description,
        start_at: taskData.start_at ? dayjs(taskData.start_at) : null,
        end_at: taskData.end_at ? dayjs(taskData.end_at) : null,
      });

      setSelectedProject(taskData.project?._id);

      if (taskData.root_directory) {
        setInitialRootDir(taskData.root_directory);
      }

      if (taskData.sub_directory) {
        setInitialSubDir(taskData.sub_directory);
      }
      if (taskData?.directories?.length) {
        const initialCheckedIds = taskData.directories.map(
          (dir) => dir._id._id
        );
        setCheckedItems(initialCheckedIds);
      } else {
        setCheckedItems([]);
      }
    }
  }, [taskData, form, currentUserId]);

  useEffect(() => {
    if (selectedProject) {
      fetchFolders(`/?project_id=${selectedProject}&page=1&limit=1000`);
      if (!initialRootDir) {
        setSelectedFolder(null);
        resetFormAndFetch("root_directory", null, { sub_directory: null });
        setPdfFetched(false);
      }
    }
  }, [selectedProject, initialRootDir, fetchFolders]);

  useEffect(() => {
    if (selectedProject && selectedFolder) {
      fetchSubFolders(
        `/?project_id=${selectedProject}&parent_id=${selectedFolder}&page=1&limit=1000`
      );

      if (!initialSubDir) {
        resetFormAndFetch("sub_directory", null);
        setPdfFetched(false);
      }
    }
  }, [selectedFolder, selectedProject, initialSubDir, fetchSubFolders]);

  useEffect(() => {
    if (folderData?.length && initialRootDir) {
      const folder = folderData.find((folder) => folder._id === initialRootDir);

      if (folder) {
        setSelectedFolder(folder._id);
        form.setFieldsValue({ root_directory: folder._id });
      }
    }
  }, [folderData, initialRootDir, form]);

  useEffect(() => {
    if (subFolderData?.length && initialSubDir) {
      const subfolder = subFolderData.find(
        (subfolder) => subfolder._id === initialSubDir
      );

      if (subfolder) {
        setSelectedSubFolder(subfolder._id);
        form.setFieldsValue({ sub_directory: subfolder._id });

        if (!pdfFetched && initialSubDir && selectedProject) {
          fetchPdf(
            `/?project_id=${selectedProject}&parent_id=${initialSubDir}&page=1&limit=1000`
          );
          setPdfFetched(true);
        }
      }
    }
  }, [
    subFolderData,
    initialSubDir,
    form,
    selectedProject,
    pdfFetched,
    fetchPdf,
  ]);

  const resetFormAndFetch = (field, value, additionalFields = {}) => {
    const resetFields = { [field]: value, ...additionalFields };
    form.setFieldsValue(resetFields);
  };

  const handleProjectChange = (val) => {
    setSelectedProject(val);
    setSelectedFolder(null);
    resetFormAndFetch("root_directory", null, { sub_directory: null });
    setInitialRootDir(null);
    setInitialSubDir(null);
    setCheckedItems([]);
    setPdfFetched(false);
    form.setFieldsValue({ assignees: [] });
    const selectedProject = projectData?.find((p) => p._id === val);

    if (selectedProject?.members) {
      setProjectAssignees(
        selectedProject.members
          .filter((member) => member._id !== currentUserId) // Filter out current user
          .map((member) => ({
            value: member._id,
            label: member.name,
          }))
      );
    } else {
      setProjectAssignees([]);
    }
  };

  const handleFolderChange = (val) => {
    setSelectedFolder(val);
    resetFormAndFetch("sub_directory", null);
    setInitialSubDir(null);
    setCheckedItems([]);
    setPdfFetched(false);
  };

  const handleSubfolderChange = (val) => {
    setSelectedSubFolder(val);
    // Only fetch PDFs if we haven't fetched them yet or if the parent_id changed
    if (
      val &&
      (!pdfData?.length || pdfData[0]?.parent_id !== val) &&
      !pdfLoading &&
      selectedProject
    ) {
      fetchPdf(
        `/?project_id=${selectedProject}&parent_id=${val}&page=1&limit=1000`
      );
      setPdfFetched(true);
    }
  };

  const handleCheckboxChange = (id) => (e) => {
    setCheckedItems((prev) => {
      if (e.target.checked) {
        return [...prev, id];
      } else {
        return prev.filter((itemId) => itemId !== id);
      }
    });
  };

  const handleFinish = async (values) => {
    const fd = new FormData();

    ["start_at", "end_at"].forEach((field) => {
      if (values[field]) {
        fd.append(field, dayjs(values[field]).format("YYYY-MM-DD"));
      }
    });
    const selectedAssignees =
      values.assignees
        ?.map((assignee) =>
          typeof assignee === "object" ? assignee.value : assignee
        )
        .filter(Boolean) || [];
    const currentAssignees = initialAssigneesRef.current.includes(currentUserId)
      ? [...selectedAssignees, currentUserId]
      : selectedAssignees;

    const initialAssignees = initialAssigneesRef.current || [];
    const hasAssigneeChanges =
      JSON.stringify(currentAssignees.sort()) !==
      JSON.stringify(initialAssignees.sort());

    if (hasAssigneeChanges) {
      // Add new assignees
      currentAssignees.forEach((id) => {
        if (!initialAssignees.includes(id)) {
          fd.append("assignees[]", id);
        }
      });

      initialAssignees.forEach((id) => {
        if (!currentAssignees.includes(id)) {
          fd.append("_assignees[]", id);
        }
      });
    }
    if (values.root_directory)
      fd.append("root_directory", values.root_directory);
    if (values.sub_directory) fd.append("sub_directory", values.sub_directory);

    if (checkedItems.length) {
      checkedItems.forEach((id) => {
        fd.append("directories[]", id);
      });
    } else {
      fd.append("directories[]", ""); // Send empty array
    }

    Object.entries(values).forEach(([key, value]) => {
      if (
        ![
          "start_at",
          "end_at",
          "assignees",
          "root_directory",
          "sub_directory",
        ].includes(key)
      ) {
        fd.append(key, value);
      }
    });

    postData(
      fd,
      (res) => {
        if (res.statusCode === 200) {
          onClose();
          fetchApi();
          if (onSuccess) onSuccess();
        }
      },
      taskData._id
    );
  };

  const renderPdfSection = () => {
    if (!selectedSubFolder) return null;

    return (
      <div className="col-12">
        <label className="color-black font-600 mt-3 mb-3">
          Select Drawing PDF
        </label>
        {pdfLoading ? (
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {[...Array(5)].map((_, index) => (
              <div key={index} style={{ margin: "10px" }}>
                <Skeleton.Image active style={{ width: 100, height: 100 }} />
              </div>
            ))}
          </div>
        ) : pdfData?.length ? (
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {pdfData?.map((item) => (
              <PdfFirstPageThumbnail
                key={item._id}
                pdfUrl={item.file}
                checked={
                  Array.isArray(checkedItems) && checkedItems.includes(item._id)
                }
                onChange={handleCheckboxChange(item._id)}
              />
            ))}
          </div>
        ) : (
          <Empty description="No drawings found" />
        )}
      </div>
    );
  };

  return (
    <Form form={form} onFinish={handleFinish} layout="vertical" className="row">
      <div className="row">
        <div className="col-12 col-md-6">
          <BaseInput name="title" label="Title" rules={create_task.title} />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="project_id"
            type="select"
            label="Project"
            options={projectData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            onChange={handleProjectChange}
            loading={projectLoading}
            rules={create_task.project_id}
            showSearch={true}
            disabled={true}
          />
        </div>

        <div className="col-12 col-md-6">
          <BaseInput
            name="root_directory"
            type="select"
            label="Select Folder"
            options={folderData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            rules={create_task.root_directory}
            loading={folderLoading}
            onChange={handleFolderChange}
            disabled={!selectedProject}
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="sub_directory"
            type="select"
            label="Select Sub Folder"
            options={subFolderData?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            rules={create_task.sub_directory}
            loading={subFolderLoading}
            disabled={!selectedFolder}
            onChange={handleSubfolderChange}
          />
        </div>

        <div className="col-12 col-md-6">
          <BaseInput
            name="assignees"
            type="select"
            label="Assignee"
            options={projectAssignees}
            mode="multiple"
            loading={projectLoading}
            rules={create_task.assignees}
            disabled={!selectedProject}
          />
        </div>

        <div className="col-12 col-md-6">
          <div className="row">
            <div className="col-12 col-md-6">
              <BaseInput
                name="start_at"
                type="datepiker"
                label="Start Date"
                rules={create_task.start_at}
                disablePastDates={true}
                format="YYYY-MM-DD"
              />
            </div>
            <div className="col-12 col-md-6">
              <BaseInput
                name="end_at"
                type="datepiker"
                label="End Date"
                rules={create_task.end_at}
                disablePastDates={true}
                format="YYYY-MM-DD"
              />
            </div>
          </div>
        </div>

        <div className="col-12">
          <BaseInput
            name="description"
            type="textarea"
            label="Description"
            rows="5"
            rules={create_task.description}
          />
        </div>

        {renderPdfSection()}
      </div>

      <div className="col-12 text-end mt-4">
        <FlatButton
          title="Update"
          className="add-new-btn"
          loading={loading}
          htmlType="submit"
        />
      </div>
    </Form>
  );
};

export default memo(UpdateTask);
