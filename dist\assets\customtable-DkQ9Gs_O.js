import{w as Zt,t as Vr,v as nt,x as se,d as _,y as mt,z as Je,r as c,I as rt,f as de,A as gt,B as me,C as Nt,D as At,i as Jo,E as sr,F as qr,G as Tn,H as Z,J as w,K as cr,M as Jt,N as Xr,O as Ur,P as Gr,Q as H,T as Yr,U as On,V as Qo,W as pt,X as Zr,Y as ea,Z as Jr,$ as Pn,a0 as ta,a1 as j,R as ut,a2 as na,a3 as dr,a4 as tn,a5 as Qr,a6 as ra,a7 as oa,a8 as Bt,a9 as aa,S as la,aa as ia,j as Ht}from"./index-C6D6r1Zc.js";import{t as eo,o as to,j as Dn,k as ur,l as sa,m as fr,L as no,n as ca,C as Qt,p as da,q as ua,r as fa,s as pa,I as va,v as ma,g as pr,E as vr,T as mr,w as ga,D as ha}from"./rules-CIBIafmf.js";import{f as Ut,g as gr,D as ro,u as ya,M as xa,O as ba,h as Ca}from"./index-D5NY69qa.js";import{R as oo}from"./index-DDGMAcG0.js";import{P as ao}from"./Pagination-COdCWbqj.js";const Sa=n=>({color:n.colorLink,textDecoration:"none",outline:"none",cursor:"pointer",transition:`color ${n.motionDurationSlow}`,"&:focus, &:hover":{color:n.colorLinkHover},"&:active":{color:n.colorLinkActive}});function xn(n){return n!=null&&n===n.window}const wa=n=>{var t,r;if(typeof window>"u")return 0;let e=0;return xn(n)?e=n.pageYOffset:n instanceof Document?e=n.documentElement.scrollTop:(n instanceof HTMLElement||n)&&(e=n.scrollTop),n&&!xn(n)&&typeof e!="number"&&(e=(r=((t=n.ownerDocument)!==null&&t!==void 0?t:n).documentElement)===null||r===void 0?void 0:r.scrollTop),e};function Ea(n,t,r,e){const o=r-t;return n/=e/2,n<1?o/2*n*n*n+t:o/2*((n-=2)*n*n+2)+t}function ka(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{getContainer:r=()=>window,callback:e,duration:o=450}=t,l=r(),d=wa(l),a=Date.now(),i=()=>{const u=Date.now()-a,f=Ea(u>o?o:u,d,n,o);xn(l)?l.scrollTo(window.pageXOffset,f):l instanceof Document||l.constructor.name==="HTMLDocument"?l.documentElement.scrollTop=f:l.scrollTop=f,u<o?Zt(i):typeof e=="function"&&e()};Zt(i)}var lo=function(t){if(Vr()&&window.document.documentElement){var r=Array.isArray(t)?t:[t],e=window.document.documentElement;return r.some(function(o){return o in e.style})}return!1},$a=function(t,r){if(!lo(t))return!1;var e=document.createElement("div"),o=e.style[t];return e.style[t]=r,e.style[t]!==o};function Na(n,t){return!Array.isArray(n)&&t!==void 0?$a(n,t):lo(n)}function Xe(n,t){return n[t]}var Ka=["children"];function io(n,t){return"".concat(n,"-").concat(t)}function Ra(n){return n&&n.type&&n.type.isTreeNode}function zt(n,t){return n??t}function Kt(n){var t=n||{},r=t.title,e=t._title,o=t.key,l=t.children,d=r||"title";return{title:d,_title:e||[d],key:o||"key",children:l||"children"}}function so(n){function t(r){var e=eo(r);return e.map(function(o){if(!Ra(o))return mt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var l=o.key,d=o.props,a=d.children,i=Je(d,Ka),s=_({key:l},i),u=t(a);return u.length&&(s.children=u),s}).filter(function(o){return o})}return t(n)}function pn(n,t,r){var e=Kt(r),o=e._title,l=e.key,d=e.children,a=new Set(t===!0?[]:t),i=[];function s(u){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return u.map(function(p,v){for(var m=io(f?f.pos:"0",v),h=zt(p[l],m),g,y=0;y<o.length;y+=1){var b=o[y];if(p[b]!==void 0){g=p[b];break}}var x=Object.assign(to(p,[].concat(se(o),[l,d])),{title:g,key:h,parent:f,pos:m,children:null,data:p,isStart:[].concat(se(f?f.isStart:[]),[v===0]),isEnd:[].concat(se(f?f.isEnd:[]),[v===u.length-1])});return i.push(x),t===!0||a.has(h)?x.children=s(p[d]||[],x):x.children=[],x})}return s(n),i}function Ia(n,t,r){var e={};nt(r)==="object"?e=r:e={externalGetKey:r},e=e||{};var o=e,l=o.childrenPropName,d=o.externalGetKey,a=o.fieldNames,i=Kt(a),s=i.key,u=i.children,f=l||u,p;d?typeof d=="string"?p=function(h){return h[d]}:typeof d=="function"&&(p=function(h){return d(h)}):p=function(h,g){return zt(h[s],g)};function v(m,h,g,y){var b=m?m[f]:n,x=m?io(g.pos,h):"0",C=m?[].concat(se(y),[m]):[];if(m){var S=p(m,x),N={node:m,index:h,pos:x,key:S,parentPos:g.node?g.pos:null,level:g.level+1,nodes:C};t(N)}b&&b.forEach(function(k,T){v(k,T,{node:m,pos:x,level:g?g.level+1:-1},C)})}v(null)}function Mn(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,e=t.processEntity,o=t.onProcessFinished,l=t.externalGetKey,d=t.childrenPropName,a=t.fieldNames,i=arguments.length>2?arguments[2]:void 0,s=l||i,u={},f={},p={posEntities:u,keyEntities:f};return r&&(p=r(p)||p),Ia(n,function(v){var m=v.node,h=v.index,g=v.pos,y=v.key,b=v.parentPos,x=v.level,C=v.nodes,S={node:m,nodes:C,index:h,key:y,pos:g,level:x},N=zt(y,g);u[g]=S,f[N]=S,S.parent=u[b],S.parent&&(S.parent.children=S.parent.children||[],S.parent.children.push(S)),e&&e(S,p)},{externalGetKey:s,childrenPropName:d,fieldNames:a}),o&&o(p),p}function _t(n,t){var r=t.expandedKeys,e=t.selectedKeys,o=t.loadedKeys,l=t.loadingKeys,d=t.checkedKeys,a=t.halfCheckedKeys,i=t.dragOverNodeKey,s=t.dropPosition,u=t.keyEntities,f=Xe(u,n),p={eventKey:n,expanded:r.indexOf(n)!==-1,selected:e.indexOf(n)!==-1,loaded:o.indexOf(n)!==-1,loading:l.indexOf(n)!==-1,checked:d.indexOf(n)!==-1,halfChecked:a.indexOf(n)!==-1,pos:String(f?f.pos:""),dragOver:i===n&&s===0,dragOverGapTop:i===n&&s===-1,dragOverGapBottom:i===n&&s===1};return p}function Oe(n){var t=n.data,r=n.expanded,e=n.selected,o=n.checked,l=n.loaded,d=n.loading,a=n.halfChecked,i=n.dragOver,s=n.dragOverGapTop,u=n.dragOverGapBottom,f=n.pos,p=n.active,v=n.eventKey,m=_(_({},t),{},{expanded:r,selected:e,checked:o,loaded:l,loading:d,halfChecked:a,dragOver:i,dragOverGapTop:s,dragOverGapBottom:u,pos:f,active:p,key:v});return"props"in m||Object.defineProperty(m,"props",{get:function(){return mt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),n}}),m}function co(n,t){var r=new Set;return n.forEach(function(e){t.has(e)||r.add(e)}),r}function Ta(n){var t=n||{},r=t.disabled,e=t.disableCheckbox,o=t.checkable;return!!(r||e)||o===!1}function Oa(n,t,r,e){for(var o=new Set(n),l=new Set,d=0;d<=r;d+=1){var a=t.get(d)||new Set;a.forEach(function(f){var p=f.key,v=f.node,m=f.children,h=m===void 0?[]:m;o.has(p)&&!e(v)&&h.filter(function(g){return!e(g.node)}).forEach(function(g){o.add(g.key)})})}for(var i=new Set,s=r;s>=0;s-=1){var u=t.get(s)||new Set;u.forEach(function(f){var p=f.parent,v=f.node;if(!(e(v)||!f.parent||i.has(f.parent.key))){if(e(f.parent.node)){i.add(p.key);return}var m=!0,h=!1;(p.children||[]).filter(function(g){return!e(g.node)}).forEach(function(g){var y=g.key,b=o.has(y);m&&!b&&(m=!1),!h&&(b||l.has(y))&&(h=!0)}),m&&o.add(p.key),h&&l.add(p.key),i.add(p.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(co(l,o))}}function Pa(n,t,r,e,o){for(var l=new Set(n),d=new Set(t),a=0;a<=e;a+=1){var i=r.get(a)||new Set;i.forEach(function(p){var v=p.key,m=p.node,h=p.children,g=h===void 0?[]:h;!l.has(v)&&!d.has(v)&&!o(m)&&g.filter(function(y){return!o(y.node)}).forEach(function(y){l.delete(y.key)})})}d=new Set;for(var s=new Set,u=e;u>=0;u-=1){var f=r.get(u)||new Set;f.forEach(function(p){var v=p.parent,m=p.node;if(!(o(m)||!p.parent||s.has(p.parent.key))){if(o(p.parent.node)){s.add(v.key);return}var h=!0,g=!1;(v.children||[]).filter(function(y){return!o(y.node)}).forEach(function(y){var b=y.key,x=l.has(b);h&&!x&&(h=!1),!g&&(x||d.has(b))&&(g=!0)}),h||l.delete(v.key),g&&d.add(v.key),s.add(v.key)}})}return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(co(d,l))}}function kt(n,t,r,e){var o=[],l;e?l=e:l=Ta;var d=new Set(n.filter(function(u){var f=!!Xe(r,u);return f||o.push(u),f})),a=new Map,i=0;Object.keys(r).forEach(function(u){var f=r[u],p=f.level,v=a.get(p);v||(v=new Set,a.set(p,v)),v.add(f),i=Math.max(i,p)}),mt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(u){return"'".concat(u,"'")}).join(", ")));var s;return t===!0?s=Oa(d,a,i,l):s=Pa(d,t.halfCheckedKeys,a,i,l),s}const Da=function(){const n=Object.assign({},arguments.length<=0?void 0:arguments[0]);for(let t=1;t<arguments.length;t++){const r=t<0||arguments.length<=t?void 0:arguments[t];r&&Object.keys(r).forEach(e=>{const o=r[e];o!==void 0&&(n[e]=o)})}return n};var Ma={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},La=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Ma}))},Ba=c.forwardRef(La),Ha={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},_a=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Ha}))},Fa=c.forwardRef(_a),Aa={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},za=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Aa}))},ja=c.forwardRef(za),Wa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},Va=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Wa}))},uo=c.forwardRef(Va),qa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},Xa=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:qa}))},Ua=c.forwardRef(Xa),Ga={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},Ya=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Ga}))},Za=c.forwardRef(Ya),Ja={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},Qa=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:Ja}))},el=c.forwardRef(Qa),tl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},nl=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:tl}))},rl=c.forwardRef(nl),ol={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},al=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:ol}))},ll=c.forwardRef(al),il={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},sl=function(t,r){return c.createElement(rt,de({},t,{ref:r,icon:il}))},cl=c.forwardRef(sl),vt={},jt="rc-table-internal-hook";function Ln(n){var t=c.createContext(void 0),r=function(o){var l=o.value,d=o.children,a=c.useRef(l);a.current=l;var i=c.useState(function(){return{getValue:function(){return a.current},listeners:new Set}}),s=me(i,1),u=s[0];return Nt(function(){Jo.unstable_batchedUpdates(function(){u.listeners.forEach(function(f){f(l)})})},[l]),c.createElement(t.Provider,{value:u},d)};return{Context:t,Provider:r,defaultValue:n}}function Ae(n,t){var r=gt(typeof t=="function"?t:function(f){if(t===void 0)return f;if(!Array.isArray(t))return f[t];var p={};return t.forEach(function(v){p[v]=f[v]}),p}),e=c.useContext(n==null?void 0:n.Context),o=e||{},l=o.listeners,d=o.getValue,a=c.useRef();a.current=r(e?d():n==null?void 0:n.defaultValue);var i=c.useState({}),s=me(i,2),u=s[1];return Nt(function(){if(!e)return;function f(p){var v=r(p);At(a.current,v,!0)||u({})}return l.add(f),function(){l.delete(f)}},[e]),a.current}function dl(){var n=c.createContext(null);function t(){return c.useContext(n)}function r(o,l){var d=sr(o),a=function(s,u){var f=d?{ref:u}:{},p=c.useRef(0),v=c.useRef(s),m=t();return m!==null?c.createElement(o,de({},s,f)):((!l||l(v.current,s))&&(p.current+=1),v.current=s,c.createElement(n.Provider,{value:p.current},c.createElement(o,de({},s,f))))};return d?c.forwardRef(a):a}function e(o,l){var d=sr(o),a=function(s,u){var f=d?{ref:u}:{};return t(),c.createElement(o,de({},s,f))};return d?c.memo(c.forwardRef(a),l):c.memo(a,l)}return{makeImmutable:r,responseImmutable:e,useImmutableMark:t}}var Bn=dl(),fo=Bn.makeImmutable,It=Bn.responseImmutable,ul=Bn.useImmutableMark,Ue=Ln(),po=c.createContext({renderWithProps:!1}),fl="RC_TABLE_KEY";function pl(n){return n==null?[]:Array.isArray(n)?n:[n]}function nn(n){var t=[],r={};return n.forEach(function(e){for(var o=e||{},l=o.key,d=o.dataIndex,a=l||pl(d).join("-")||fl;r[a];)a="".concat(a,"_next");r[a]=!0,t.push(a)}),t}function bn(n){return n!=null}function vl(n){return n&&nt(n)==="object"&&!Array.isArray(n)&&!c.isValidElement(n)}function ml(n,t,r,e,o,l){var d=c.useContext(po),a=ul(),i=qr(function(){if(bn(e))return[e];var s=t==null||t===""?[]:Array.isArray(t)?t:[t],u=Tn(n,s),f=u,p=void 0;if(o){var v=o(u,n,r);vl(v)?(f=v.children,p=v.props,d.renderWithProps=!0):f=v}return[f,p]},[a,n,e,t,o,r],function(s,u){if(l){var f=me(s,2),p=f[1],v=me(u,2),m=v[1];return l(m,p)}return d.renderWithProps?!0:!At(s,u,!0)});return i}function gl(n,t,r,e){var o=n+t-1;return n<=e&&o>=r}function hl(n,t){return Ae(Ue,function(r){var e=gl(n,t||1,r.hoverStartRow,r.hoverEndRow);return[e,r.onHover]})}var yl=function(t){var r=t.ellipsis,e=t.rowType,o=t.children,l,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||e==="header")&&(typeof o=="string"||typeof o=="number"?l=o.toString():c.isValidElement(o)&&typeof o.props.children=="string"&&(l=o.props.children)),l};function xl(n){var t,r,e,o,l,d,a,i,s=n.component,u=n.children,f=n.ellipsis,p=n.scope,v=n.prefixCls,m=n.className,h=n.align,g=n.record,y=n.render,b=n.dataIndex,x=n.renderIndex,C=n.shouldCellUpdate,S=n.index,N=n.rowType,k=n.colSpan,T=n.rowSpan,E=n.fixLeft,$=n.fixRight,M=n.firstFixLeft,K=n.lastFixLeft,O=n.firstFixRight,R=n.lastFixRight,L=n.appendNode,D=n.additionalProps,I=D===void 0?{}:D,B=n.isSticky,P="".concat(v,"-cell"),V=Ae(Ue,["supportSticky","allColumnsFixedLeft","rowHoverable"]),W=V.supportSticky,re=V.allColumnsFixedLeft,J=V.rowHoverable,$e=ml(g,b,x,u,y,C),ge=me($e,2),Ne=ge[0],ee=ge[1],pe={},te=typeof E=="number"&&W,ce=typeof $=="number"&&W;te&&(pe.position="sticky",pe.left=E),ce&&(pe.position="sticky",pe.right=$);var Q=(t=(r=(e=ee==null?void 0:ee.colSpan)!==null&&e!==void 0?e:I.colSpan)!==null&&r!==void 0?r:k)!==null&&t!==void 0?t:1,U=(o=(l=(d=ee==null?void 0:ee.rowSpan)!==null&&d!==void 0?d:I.rowSpan)!==null&&l!==void 0?l:T)!==null&&o!==void 0?o:1,z=hl(S,U),F=me(z,2),q=F[0],ae=F[1],ne=gt(function(ve){var xe;g&&ae(S,S+U-1),I==null||(xe=I.onMouseEnter)===null||xe===void 0||xe.call(I,ve)}),A=gt(function(ve){var xe;g&&ae(-1,-1),I==null||(xe=I.onMouseLeave)===null||xe===void 0||xe.call(I,ve)});if(Q===0||U===0)return null;var ie=(a=I.title)!==null&&a!==void 0?a:yl({rowType:N,ellipsis:f,children:Ne}),G=Z(P,m,(i={},w(i,"".concat(P,"-fix-left"),te&&W),w(i,"".concat(P,"-fix-left-first"),M&&W),w(i,"".concat(P,"-fix-left-last"),K&&W),w(i,"".concat(P,"-fix-left-all"),K&&re&&W),w(i,"".concat(P,"-fix-right"),ce&&W),w(i,"".concat(P,"-fix-right-first"),O&&W),w(i,"".concat(P,"-fix-right-last"),R&&W),w(i,"".concat(P,"-ellipsis"),f),w(i,"".concat(P,"-with-append"),L),w(i,"".concat(P,"-fix-sticky"),(te||ce)&&B&&W),w(i,"".concat(P,"-row-hover"),!ee&&q),i),I.className,ee==null?void 0:ee.className),ue={};h&&(ue.textAlign=h);var we=_(_(_(_({},pe),I.style),ue),ee==null?void 0:ee.style),X=Ne;return nt(X)==="object"&&!Array.isArray(X)&&!c.isValidElement(X)&&(X=null),f&&(K||O)&&(X=c.createElement("span",{className:"".concat(P,"-content")},X)),c.createElement(s,de({},ee,I,{className:G,style:we,title:ie,scope:p,onMouseEnter:J?ne:void 0,onMouseLeave:J?A:void 0,colSpan:Q!==1?Q:null,rowSpan:U!==1?U:null}),L,X)}const Ct=c.memo(xl);function Hn(n,t,r,e,o){var l=r[n]||{},d=r[t]||{},a,i;l.fixed==="left"?a=e.left[o==="rtl"?t:n]:d.fixed==="right"&&(i=e.right[o==="rtl"?n:t]);var s=!1,u=!1,f=!1,p=!1,v=r[t+1],m=r[n-1],h=v&&!v.fixed||m&&!m.fixed||r.every(function(C){return C.fixed==="left"});if(o==="rtl"){if(a!==void 0){var g=m&&m.fixed==="left";p=!g&&h}else if(i!==void 0){var y=v&&v.fixed==="right";f=!y&&h}}else if(a!==void 0){var b=v&&v.fixed==="left";s=!b&&h}else if(i!==void 0){var x=m&&m.fixed==="right";u=!x&&h}return{fixLeft:a,fixRight:i,lastFixLeft:s,firstFixRight:u,lastFixRight:f,firstFixLeft:p,isSticky:e.isSticky}}var vo=c.createContext({});function bl(n){var t=n.className,r=n.index,e=n.children,o=n.colSpan,l=o===void 0?1:o,d=n.rowSpan,a=n.align,i=Ae(Ue,["prefixCls","direction"]),s=i.prefixCls,u=i.direction,f=c.useContext(vo),p=f.scrollColumnIndex,v=f.stickyOffsets,m=f.flattenColumns,h=r+l-1,g=h+1===p?l+1:l,y=Hn(r,r+g-1,m,v,u);return c.createElement(Ct,de({className:t,index:r,component:"td",prefixCls:s,record:null,dataIndex:null,align:a,colSpan:g,rowSpan:d,render:function(){return e}},y))}var Cl=["children"];function Sl(n){var t=n.children,r=Je(n,Cl);return c.createElement("tr",r,t)}function rn(n){var t=n.children;return t}rn.Row=Sl;rn.Cell=bl;function wl(n){var t=n.children,r=n.stickyOffsets,e=n.flattenColumns,o=Ae(Ue,"prefixCls"),l=e.length-1,d=e[l],a=c.useMemo(function(){return{stickyOffsets:r,flattenColumns:e,scrollColumnIndex:d!=null&&d.scrollbar?l:null}},[d,e,l,r]);return c.createElement(vo.Provider,{value:a},c.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const Gt=It(wl);var mo=rn;function El(n){return null}function kl(n){return null}function go(n,t,r,e,o,l,d){n.push({record:t,indent:r,index:d});var a=l(t),i=o==null?void 0:o.has(a);if(t&&Array.isArray(t[e])&&i)for(var s=0;s<t[e].length;s+=1)go(n,t[e][s],r+1,e,o,l,s)}function ho(n,t,r,e){var o=c.useMemo(function(){if(r!=null&&r.size){for(var l=[],d=0;d<(n==null?void 0:n.length);d+=1){var a=n[d];go(l,a,0,t,r,e,d)}return l}return n==null?void 0:n.map(function(i,s){return{record:i,indent:0,index:s}})},[n,t,r,e]);return o}function yo(n,t,r,e){var o=Ae(Ue,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),l=o.flattenColumns,d=o.expandableType,a=o.expandedKeys,i=o.childrenColumnName,s=o.onTriggerExpand,u=o.rowExpandable,f=o.onRow,p=o.expandRowByClick,v=o.rowClassName,m=d==="nest",h=d==="row"&&(!u||u(n)),g=h||m,y=a&&a.has(t),b=i&&n&&n[i],x=gt(s),C=f==null?void 0:f(n,r),S=C==null?void 0:C.onClick,N=function($){p&&g&&s(n,$);for(var M=arguments.length,K=new Array(M>1?M-1:0),O=1;O<M;O++)K[O-1]=arguments[O];S==null||S.apply(void 0,[$].concat(K))},k;typeof v=="string"?k=v:typeof v=="function"&&(k=v(n,r,e));var T=nn(l);return _(_({},o),{},{columnsKey:T,nestExpandable:m,expanded:y,hasNestChildren:b,record:n,onTriggerExpand:x,rowSupportExpand:h,expandable:g,rowProps:_(_({},C),{},{className:Z(k,C==null?void 0:C.className),onClick:N})})}function xo(n){var t=n.prefixCls,r=n.children,e=n.component,o=n.cellComponent,l=n.className,d=n.expanded,a=n.colSpan,i=n.isEmpty,s=Ae(Ue,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),u=s.scrollbarSize,f=s.fixHeader,p=s.fixColumn,v=s.componentWidth,m=s.horizonScroll,h=r;return(i?m&&v:p)&&(h=c.createElement("div",{style:{width:v-(f?u:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},h)),c.createElement(e,{className:l,style:{display:d?null:"none"}},c.createElement(Ct,{component:o,prefixCls:t,colSpan:a},h))}function bo(n,t,r,e,o){var l=n.record,d=n.prefixCls,a=n.columnsKey,i=n.fixedInfoList,s=n.expandIconColumnIndex,u=n.nestExpandable,f=n.indentSize,p=n.expandIcon,v=n.expanded,m=n.hasNestChildren,h=n.onTriggerExpand,g=a[r],y=i[r],b;r===(s||0)&&u&&(b=c.createElement(c.Fragment,null,c.createElement("span",{style:{paddingLeft:"".concat(f*e,"px")},className:"".concat(d,"-row-indent indent-level-").concat(e)}),p({prefixCls:d,expanded:v,expandable:m,record:l,onExpand:h})));var x;return t.onCell&&(x=t.onCell(l,o)),{key:g,fixedInfo:y,appendCellNode:b,additionalCellProps:x||{}}}function $l(n){var t=n.className,r=n.style,e=n.record,o=n.index,l=n.renderIndex,d=n.rowKey,a=n.indent,i=a===void 0?0:a,s=n.rowComponent,u=n.cellComponent,f=n.scopeCellComponent,p=yo(e,d,o,i),v=p.prefixCls,m=p.flattenColumns,h=p.expandedRowClassName,g=p.expandedRowRender,y=p.rowProps,b=p.expanded,x=p.rowSupportExpand,C=c.useRef(!1);C.current||(C.current=b);var S=h&&h(e,o,i),N=c.createElement(s,de({},y,{"data-row-key":d,className:Z(t,"".concat(v,"-row"),"".concat(v,"-row-level-").concat(i),y==null?void 0:y.className,i>=1?S:""),style:_(_({},r),y==null?void 0:y.style)}),m.map(function(E,$){var M=E.render,K=E.dataIndex,O=E.className,R=bo(p,E,$,i,o),L=R.key,D=R.fixedInfo,I=R.appendCellNode,B=R.additionalCellProps;return c.createElement(Ct,de({className:O,ellipsis:E.ellipsis,align:E.align,scope:E.rowScope,component:E.rowScope?f:u,prefixCls:v,key:L,record:e,index:o,renderIndex:l,dataIndex:K,render:M,shouldCellUpdate:E.shouldCellUpdate},D,{appendNode:I,additionalProps:B}))})),k;if(x&&(C.current||b)){var T=g(e,o,i+1,b);k=c.createElement(xo,{expanded:b,className:Z("".concat(v,"-expanded-row"),"".concat(v,"-expanded-row-level-").concat(i+1),S),prefixCls:v,component:s,cellComponent:u,colSpan:m.length,isEmpty:!1},T)}return c.createElement(c.Fragment,null,N,k)}const Nl=It($l);function Kl(n){var t=n.columnKey,r=n.onColumnResize,e=c.useRef();return c.useEffect(function(){e.current&&r(t,e.current.offsetWidth)},[]),c.createElement(Dn,{data:t},c.createElement("td",{ref:e,style:{padding:0,border:0,height:0}},c.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function Rl(n){var t=n.prefixCls,r=n.columnsKey,e=n.onColumnResize;return c.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},c.createElement(Dn.Collection,{onBatchResize:function(l){l.forEach(function(d){var a=d.data,i=d.size;e(a,i.offsetWidth)})}},r.map(function(o){return c.createElement(Kl,{key:o,columnKey:o,onColumnResize:e})})))}function Il(n){var t=n.data,r=n.measureColumnWidth,e=Ae(Ue,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),o=e.prefixCls,l=e.getComponent,d=e.onColumnResize,a=e.flattenColumns,i=e.getRowKey,s=e.expandedKeys,u=e.childrenColumnName,f=e.emptyNode,p=ho(t,u,s,i),v=c.useRef({renderWithProps:!1}),m=l(["body","wrapper"],"tbody"),h=l(["body","row"],"tr"),g=l(["body","cell"],"td"),y=l(["body","cell"],"th"),b;t.length?b=p.map(function(C,S){var N=C.record,k=C.indent,T=C.index,E=i(N,S);return c.createElement(Nl,{key:E,rowKey:E,record:N,index:S,renderIndex:T,rowComponent:h,cellComponent:g,scopeCellComponent:y,getRowKey:i,indent:k})}):b=c.createElement(xo,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:h,cellComponent:g,colSpan:a.length,isEmpty:!0},f);var x=nn(a);return c.createElement(po.Provider,{value:v.current},c.createElement(m,{className:"".concat(o,"-tbody")},r&&c.createElement(Rl,{prefixCls:o,columnsKey:x,onColumnResize:d}),b))}const Tl=It(Il);var Ol=["expandable"],Ft="RC_TABLE_INTERNAL_COL_DEFINE";function Pl(n){var t=n.expandable,r=Je(n,Ol),e;return"expandable"in n?e=_(_({},r),t):e=r,e.showExpandColumn===!1&&(e.expandIconColumnIndex=-1),e}var Dl=["columnType"];function Co(n){for(var t=n.colWidths,r=n.columns,e=n.columCount,o=[],l=e||r.length,d=!1,a=l-1;a>=0;a-=1){var i=t[a],s=r&&r[a],u=s&&s[Ft];if(i||u||d){var f=u||{};f.columnType;var p=Je(f,Dl);o.unshift(c.createElement("col",de({key:a,style:{width:i}},p))),d=!0}}return c.createElement("colgroup",null,o)}var Ml=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function Ll(n,t){return c.useMemo(function(){for(var r=[],e=0;e<t;e+=1){var o=n[e];if(o!==void 0)r[e]=o;else return null}return r},[n.join("_"),t])}var Bl=c.forwardRef(function(n,t){var r=n.className,e=n.noData,o=n.columns,l=n.flattenColumns,d=n.colWidths,a=n.columCount,i=n.stickyOffsets,s=n.direction,u=n.fixHeader,f=n.stickyTopOffset,p=n.stickyBottomOffset,v=n.stickyClassName,m=n.onScroll,h=n.maxContentScroll,g=n.children,y=Je(n,Ml),b=Ae(Ue,["prefixCls","scrollbarSize","isSticky","getComponent"]),x=b.prefixCls,C=b.scrollbarSize,S=b.isSticky,N=b.getComponent,k=N(["header","table"],"table"),T=S&&!u?0:C,E=c.useRef(null),$=c.useCallback(function(B){cr(t,B),cr(E,B)},[]);c.useEffect(function(){var B;function P(V){var W=V,re=W.currentTarget,J=W.deltaX;J&&(m({currentTarget:re,scrollLeft:re.scrollLeft+J}),V.preventDefault())}return(B=E.current)===null||B===void 0||B.addEventListener("wheel",P,{passive:!1}),function(){var V;(V=E.current)===null||V===void 0||V.removeEventListener("wheel",P)}},[]);var M=c.useMemo(function(){return l.every(function(B){return B.width})},[l]),K=l[l.length-1],O={fixed:K?K.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(x,"-cell-scrollbar")}}},R=c.useMemo(function(){return T?[].concat(se(o),[O]):o},[T,o]),L=c.useMemo(function(){return T?[].concat(se(l),[O]):l},[T,l]),D=c.useMemo(function(){var B=i.right,P=i.left;return _(_({},i),{},{left:s==="rtl"?[].concat(se(P.map(function(V){return V+T})),[0]):P,right:s==="rtl"?B:[].concat(se(B.map(function(V){return V+T})),[0]),isSticky:S})},[T,i,S]),I=Ll(d,a);return c.createElement("div",{style:_({overflow:"hidden"},S?{top:f,bottom:p}:{}),ref:$,className:Z(r,w({},v,!!v))},c.createElement(k,{style:{tableLayout:"fixed",visibility:e||I?null:"hidden"}},(!e||!h||M)&&c.createElement(Co,{colWidths:I?[].concat(se(I),[T]):[],columCount:a+1,columns:L}),g(_(_({},y),{},{stickyOffsets:D,columns:R,flattenColumns:L}))))});const hr=c.memo(Bl);var Hl=function(t){var r=t.cells,e=t.stickyOffsets,o=t.flattenColumns,l=t.rowComponent,d=t.cellComponent,a=t.onHeaderRow,i=t.index,s=Ae(Ue,["prefixCls","direction"]),u=s.prefixCls,f=s.direction,p;a&&(p=a(r.map(function(m){return m.column}),i));var v=nn(r.map(function(m){return m.column}));return c.createElement(l,p,r.map(function(m,h){var g=m.column,y=Hn(m.colStart,m.colEnd,o,e,f),b;return g&&g.onHeaderCell&&(b=m.column.onHeaderCell(g)),c.createElement(Ct,de({},m,{scope:g.title?m.colSpan>1?"colgroup":"col":null,ellipsis:g.ellipsis,align:g.align,component:d,prefixCls:u,key:v[h]},y,{additionalProps:b,rowType:"header"}))}))};function _l(n){var t=[];function r(d,a){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[i]=t[i]||[];var s=a,u=d.filter(Boolean).map(function(f){var p={key:f.key,className:f.className||"",children:f.title,column:f,colStart:s},v=1,m=f.children;return m&&m.length>0&&(v=r(m,s,i+1).reduce(function(h,g){return h+g},0),p.hasSubColumns=!0),"colSpan"in f&&(v=f.colSpan),"rowSpan"in f&&(p.rowSpan=f.rowSpan),p.colSpan=v,p.colEnd=p.colStart+v-1,t[i].push(p),s+=v,v});return u}r(n,0);for(var e=t.length,o=function(a){t[a].forEach(function(i){!("rowSpan"in i)&&!i.hasSubColumns&&(i.rowSpan=e-a)})},l=0;l<e;l+=1)o(l);return t}var Fl=function(t){var r=t.stickyOffsets,e=t.columns,o=t.flattenColumns,l=t.onHeaderRow,d=Ae(Ue,["prefixCls","getComponent"]),a=d.prefixCls,i=d.getComponent,s=c.useMemo(function(){return _l(e)},[e]),u=i(["header","wrapper"],"thead"),f=i(["header","row"],"tr"),p=i(["header","cell"],"th");return c.createElement(u,{className:"".concat(a,"-thead")},s.map(function(v,m){var h=c.createElement(Hl,{key:m,flattenColumns:o,cells:v,stickyOffsets:r,rowComponent:f,cellComponent:p,onHeaderRow:l,index:m});return h}))};const yr=It(Fl);function xr(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?n*parseFloat(t)/100:null}function Al(n,t,r){return c.useMemo(function(){if(t&&t>0){var e=0,o=0;n.forEach(function(p){var v=xr(t,p.width);v?e+=v:o+=1});var l=Math.max(t,r),d=Math.max(l-e,o),a=o,i=d/o,s=0,u=n.map(function(p){var v=_({},p),m=xr(t,v.width);if(m)v.width=m;else{var h=Math.floor(i);v.width=a===1?d:h,d-=h,a-=1}return s+=v.width,v});if(s<l){var f=l/s;d=l,u.forEach(function(p,v){var m=Math.floor(p.width*f);p.width=v===u.length-1?d:m,d-=m})}return[u,Math.max(s,l)]}return[n,t]},[n,t,r])}var zl=["children"],jl=["fixed"];function _n(n){return eo(n).filter(function(t){return c.isValidElement(t)}).map(function(t){var r=t.key,e=t.props,o=e.children,l=Je(e,zl),d=_({key:r},l);return o&&(d.children=_n(o)),d})}function So(n){return n.filter(function(t){return t&&nt(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?_(_({},t),{},{children:So(r)}):t})}function Cn(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return n.filter(function(r){return r&&nt(r)==="object"}).reduce(function(r,e,o){var l=e.fixed,d=l===!0?"left":l,a="".concat(t,"-").concat(o),i=e.children;return i&&i.length>0?[].concat(se(r),se(Cn(i,a).map(function(s){return _({fixed:d},s)}))):[].concat(se(r),[_(_({key:a},e),{},{fixed:d})])},[])}function Wl(n){return n.map(function(t){var r=t.fixed,e=Je(t,jl),o=r;return r==="left"?o="right":r==="right"&&(o="left"),_({fixed:o},e)})}function Vl(n,t){var r=n.prefixCls,e=n.columns,o=n.children,l=n.expandable,d=n.expandedKeys,a=n.columnTitle,i=n.getRowKey,s=n.onTriggerExpand,u=n.expandIcon,f=n.rowExpandable,p=n.expandIconColumnIndex,v=n.direction,m=n.expandRowByClick,h=n.columnWidth,g=n.fixed,y=n.scrollWidth,b=n.clientWidth,x=c.useMemo(function(){var K=e||_n(o)||[];return So(K.slice())},[e,o]),C=c.useMemo(function(){if(l){var K,O=x.slice();if(!O.includes(vt)){var R=p||0;R>=0&&O.splice(R,0,vt)}var L=O.indexOf(vt);O=O.filter(function(P,V){return P!==vt||V===L});var D=x[L],I;(g==="left"||g)&&!p?I="left":(g==="right"||g)&&p===x.length?I="right":I=D?D.fixed:null;var B=(K={},w(K,Ft,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),w(K,"title",a),w(K,"fixed",I),w(K,"className","".concat(r,"-row-expand-icon-cell")),w(K,"width",h),w(K,"render",function(V,W,re){var J=i(W,re),$e=d.has(J),ge=f?f(W):!0,Ne=u({prefixCls:r,expanded:$e,expandable:ge,record:W,onExpand:s});return m?c.createElement("span",{onClick:function(pe){return pe.stopPropagation()}},Ne):Ne}),K);return O.map(function(P){return P===vt?B:P})}return x.filter(function(P){return P!==vt})},[l,x,i,d,u,v]),S=c.useMemo(function(){var K=C;return t&&(K=t(K)),K.length||(K=[{render:function(){return null}}]),K},[t,C,v]),N=c.useMemo(function(){return v==="rtl"?Wl(Cn(S)):Cn(S)},[S,v,y]),k=c.useMemo(function(){for(var K=-1,O=N.length-1;O>=0;O-=1){var R=N[O].fixed;if(R==="left"||R===!0){K=O;break}}if(K>=0)for(var L=0;L<=K;L+=1){var D=N[L].fixed;if(D!=="left"&&D!==!0)return!0}var I=N.findIndex(function(V){var W=V.fixed;return W==="right"});if(I>=0)for(var B=I;B<N.length;B+=1){var P=N[B].fixed;if(P!=="right")return!0}return!1},[N]),T=Al(N,y,b),E=me(T,2),$=E[0],M=E[1];return[S,$,M,k]}function ql(n){var t,r=n.prefixCls,e=n.record,o=n.onExpand,l=n.expanded,d=n.expandable,a="".concat(r,"-row-expand-icon");if(!d)return c.createElement("span",{className:Z(a,"".concat(r,"-row-spaced"))});var i=function(u){o(e,u),u.stopPropagation()};return c.createElement("span",{className:Z(a,(t={},w(t,"".concat(r,"-row-expanded"),l),w(t,"".concat(r,"-row-collapsed"),!l),t)),onClick:i})}function Xl(n,t,r){var e=[];function o(l){(l||[]).forEach(function(d,a){e.push(t(d,a)),o(d[r])})}return o(n),e}function Ul(n,t,r){var e=Pl(n),o=e.expandIcon,l=e.expandedRowKeys,d=e.defaultExpandedRowKeys,a=e.defaultExpandAllRows,i=e.expandedRowRender,s=e.onExpand,u=e.onExpandedRowsChange,f=e.childrenColumnName,p=o||ql,v=f||"children",m=c.useMemo(function(){return i?"row":n.expandable&&n.internalHooks===jt&&n.expandable.__PARENT_RENDER_ICON__||t.some(function(S){return S&&nt(S)==="object"&&S[v]})?"nest":!1},[!!i,t]),h=c.useState(function(){return d||(a?Xl(t,r,v):[])}),g=me(h,2),y=g[0],b=g[1],x=c.useMemo(function(){return new Set(l||y||[])},[l,y]),C=c.useCallback(function(S){var N=r(S,t.indexOf(S)),k,T=x.has(N);T?(x.delete(N),k=se(x)):k=[].concat(se(x),[N]),b(k),s&&s(!T,S),u&&u(k)},[r,x,t,s,u]);return[e,m,x,p,v,C]}function Gl(n,t,r){var e=n.map(function(o,l){return Hn(l,l,n,t,r)});return qr(function(){return e},[e],function(o,l){return!At(o,l)})}function wo(n){var t=c.useRef(n),r=c.useState({}),e=me(r,2),o=e[1],l=c.useRef(null),d=c.useRef([]);function a(i){d.current.push(i);var s=Promise.resolve();l.current=s,s.then(function(){if(l.current===s){var u=d.current,f=t.current;d.current=[],u.forEach(function(p){t.current=p(t.current)}),l.current=null,f!==t.current&&o({})}})}return c.useEffect(function(){return function(){l.current=null}},[]),[t.current,a]}function Yl(n){var t=c.useRef(null),r=c.useRef();function e(){window.clearTimeout(r.current)}function o(d){t.current=d,e(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function l(){return t.current}return c.useEffect(function(){return e},[]),[o,l]}function Zl(){var n=c.useState(-1),t=me(n,2),r=t[0],e=t[1],o=c.useState(-1),l=me(o,2),d=l[0],a=l[1],i=c.useCallback(function(s,u){e(s),a(u)},[]);return[r,d,i]}var br=Vr()?window:null;function Jl(n,t){var r=nt(n)==="object"?n:{},e=r.offsetHeader,o=e===void 0?0:e,l=r.offsetSummary,d=l===void 0?0:l,a=r.offsetScroll,i=a===void 0?0:a,s=r.getContainer,u=s===void 0?function(){return br}:s,f=u()||br;return c.useMemo(function(){var p=!!n;return{isSticky:p,stickyClassName:p?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:i,container:f}},[i,o,d,t,f])}function Ql(n,t,r){var e=c.useMemo(function(){var o=t.length,l=function(s,u,f){for(var p=[],v=0,m=s;m!==u;m+=f)p.push(v),t[m].fixed&&(v+=n[m]||0);return p},d=l(0,o,1),a=l(o-1,-1,-1).reverse();return r==="rtl"?{left:a,right:d}:{left:d,right:a}},[n,t,r]);return e}function Cr(n){var t=n.className,r=n.children;return c.createElement("div",{className:t},r)}var ei=function(t,r){var e,o,l=t.scrollBodyRef,d=t.onScroll,a=t.offsetScroll,i=t.container,s=Ae(Ue,"prefixCls"),u=((e=l.current)===null||e===void 0?void 0:e.scrollWidth)||0,f=((o=l.current)===null||o===void 0?void 0:o.clientWidth)||0,p=u&&f*(f/u),v=c.useRef(),m=wo({scrollLeft:0,isHiddenScrollBar:!1}),h=me(m,2),g=h[0],y=h[1],b=c.useRef({delta:0,x:0}),x=c.useState(!1),C=me(x,2),S=C[0],N=C[1],k=c.useRef(null);c.useEffect(function(){return function(){Zt.cancel(k.current)}},[]);var T=function(){N(!1)},E=function(R){R.persist(),b.current.delta=R.pageX-g.scrollLeft,b.current.x=0,N(!0),R.preventDefault()},$=function(R){var L,D=R||((L=window)===null||L===void 0?void 0:L.event),I=D.buttons;if(!S||I===0){S&&N(!1);return}var B=b.current.x+R.pageX-b.current.x-b.current.delta;B<=0&&(B=0),B+p>=f&&(B=f-p),d({scrollLeft:B/f*(u+2)}),b.current.x=R.pageX},M=function(){k.current=Zt(function(){if(l.current){var R=gr(l.current).top,L=R+l.current.offsetHeight,D=i===window?document.documentElement.scrollTop+window.innerHeight:gr(i).top+i.clientHeight;L-ur()<=D||R>=D-a?y(function(I){return _(_({},I),{},{isHiddenScrollBar:!0})}):y(function(I){return _(_({},I),{},{isHiddenScrollBar:!1})})}})},K=function(R){y(function(L){return _(_({},L),{},{scrollLeft:R/u*f||0})})};return c.useImperativeHandle(r,function(){return{setScrollLeft:K,checkScrollBarVisible:M}}),c.useEffect(function(){var O=Ut(document.body,"mouseup",T,!1),R=Ut(document.body,"mousemove",$,!1);return M(),function(){O.remove(),R.remove()}},[p,S]),c.useEffect(function(){var O=Ut(i,"scroll",M,!1),R=Ut(window,"resize",M,!1);return function(){O.remove(),R.remove()}},[i]),c.useEffect(function(){g.isHiddenScrollBar||y(function(O){var R=l.current;return R?_(_({},O),{},{scrollLeft:R.scrollLeft/R.scrollWidth*R.clientWidth}):O})},[g.isHiddenScrollBar]),u<=f||!p||g.isHiddenScrollBar?null:c.createElement("div",{style:{height:ur(),width:f,bottom:a},className:"".concat(s,"-sticky-scroll")},c.createElement("div",{onMouseDown:E,ref:v,className:Z("".concat(s,"-sticky-scroll-bar"),w({},"".concat(s,"-sticky-scroll-bar-active"),S)),style:{width:"".concat(p,"px"),transform:"translate3d(".concat(g.scrollLeft,"px, 0, 0)")}}))};const ti=c.forwardRef(ei);var Eo="rc-table",ni=[],ri={};function oi(){return"No Data"}function ai(n,t){var r,e=_({rowKey:"key",prefixCls:Eo,emptyText:oi},n),o=e.prefixCls,l=e.className,d=e.rowClassName,a=e.style,i=e.data,s=e.rowKey,u=e.scroll,f=e.tableLayout,p=e.direction,v=e.title,m=e.footer,h=e.summary,g=e.caption,y=e.id,b=e.showHeader,x=e.components,C=e.emptyText,S=e.onRow,N=e.onHeaderRow,k=e.onScroll,T=e.internalHooks,E=e.transformColumns,$=e.internalRefs,M=e.tailor,K=e.getContainerWidth,O=e.sticky,R=e.rowHoverable,L=R===void 0?!0:R,D=i||ni,I=!!D.length,B=T===jt,P=c.useCallback(function(le,fe){return Tn(x,le)||fe},[x]),V=c.useMemo(function(){return typeof s=="function"?s:function(le){var fe=le&&le[s];return fe}},[s]),W=P(["body"]),re=Zl(),J=me(re,3),$e=J[0],ge=J[1],Ne=J[2],ee=Ul(e,D,V),pe=me(ee,6),te=pe[0],ce=pe[1],Q=pe[2],U=pe[3],z=pe[4],F=pe[5],q=u==null?void 0:u.x,ae=c.useState(0),ne=me(ae,2),A=ne[0],ie=ne[1],G=Vl(_(_(_({},e),te),{},{expandable:!!te.expandedRowRender,columnTitle:te.columnTitle,expandedKeys:Q,getRowKey:V,onTriggerExpand:F,expandIcon:U,expandIconColumnIndex:te.expandIconColumnIndex,direction:p,scrollWidth:B&&M&&typeof q=="number"?q:null,clientWidth:A}),B?E:null),ue=me(G,4),we=ue[0],X=ue[1],ve=ue[2],xe=ue[3],he=ve??q,We=c.useMemo(function(){return{columns:we,flattenColumns:X}},[we,X]),De=c.useRef(),ot=c.useRef(),Y=c.useRef(),be=c.useRef();c.useImperativeHandle(t,function(){return{nativeElement:De.current,scrollTo:function(fe){var He;if(Y.current instanceof HTMLElement){var Ge=fe.index,_e=fe.top,Et=fe.key;if(_e){var yt;(yt=Y.current)===null||yt===void 0||yt.scrollTo({top:_e})}else{var xt,Lt=Et??V(D[Ge]);(xt=Y.current.querySelector('[data-row-key="'.concat(Lt,'"]')))===null||xt===void 0||xt.scrollIntoView()}}else(He=Y.current)!==null&&He!==void 0&&He.scrollTo&&Y.current.scrollTo(fe)}}});var Ce=c.useRef(),Pe=c.useState(!1),Ke=me(Pe,2),Ee=Ke[0],Te=Ke[1],Me=c.useState(!1),Re=me(Me,2),at=Re[0],Ve=Re[1],Pt=wo(new Map),ke=me(Pt,2),Dt=ke[0],qe=ke[1],Ye=nn(X),Ze=Ye.map(function(le){return Dt.get(le)}),St=c.useMemo(function(){return Ze},[Ze.join("_")]),lt=Ql(St,X,p),Le=u&&bn(u.y),ze=u&&bn(he)||!!te.fixed,Qe=ze&&X.some(function(le){var fe=le.fixed;return fe}),Mt=c.useRef(),dt=Jl(O,o),et=dt.isSticky,an=dt.offsetHeader,Wt=dt.offsetSummary,ln=dt.offsetScroll,oe=dt.stickyClassName,ye=dt.container,Se=c.useMemo(function(){return h==null?void 0:h(D)},[h,D]),Ie=(Le||et)&&c.isValidElement(Se)&&Se.type===rn&&Se.props.fixed,je,Fe,it;Le&&(Fe={overflowY:"scroll",maxHeight:u.y}),ze&&(je={overflowX:"auto"},Le||(Fe={overflowY:"hidden"}),it={width:he===!0?"auto":he,minWidth:"100%"});var Be=c.useCallback(function(le,fe){sa(De.current)&&qe(function(He){if(He.get(le)!==fe){var Ge=new Map(He);return Ge.set(le,fe),Ge}return He})},[]),Ao=Yl(),qn=me(Ao,2),zo=qn[0],Xn=qn[1];function Vt(le,fe){fe&&(typeof fe=="function"?fe(le):fe.scrollLeft!==le&&(fe.scrollLeft=le,fe.scrollLeft!==le&&setTimeout(function(){fe.scrollLeft=le},0)))}var wt=gt(function(le){var fe=le.currentTarget,He=le.scrollLeft,Ge=p==="rtl",_e=typeof He=="number"?He:fe.scrollLeft,Et=fe||ri;if(!Xn()||Xn()===Et){var yt;zo(Et),Vt(_e,ot.current),Vt(_e,Y.current),Vt(_e,Ce.current),Vt(_e,(yt=Mt.current)===null||yt===void 0?void 0:yt.setScrollLeft)}var xt=fe||ot.current;if(xt){var Lt=xt.scrollWidth,fn=xt.clientWidth;if(Lt===fn){Te(!1),Ve(!1);return}Ge?(Te(-_e<Lt-fn),Ve(-_e>0)):(Te(_e>0),Ve(_e<Lt-fn))}}),jo=gt(function(le){wt(le),k==null||k(le)}),Un=function(){ze&&Y.current?wt({currentTarget:Y.current}):(Te(!1),Ve(!1))},Wo=function(fe){var He,Ge=fe.width;(He=Mt.current)===null||He===void 0||He.checkScrollBarVisible();var _e=De.current?De.current.offsetWidth:Ge;B&&K&&De.current&&(_e=K(De.current,_e)||_e),_e!==A&&(Un(),ie(_e))},Gn=c.useRef(!1);c.useEffect(function(){Gn.current&&Un()},[ze,i,we.length]),c.useEffect(function(){Gn.current=!0},[]);var Vo=c.useState(0),Yn=me(Vo,2),qt=Yn[0],Zn=Yn[1],qo=c.useState(!0),Jn=me(qo,2),Qn=Jn[0],Xo=Jn[1];c.useEffect(function(){(!M||!B)&&(Y.current instanceof Element?Zn(fr(Y.current).width):Zn(fr(be.current).width)),Xo(Na("position","sticky"))},[]),c.useEffect(function(){B&&$&&($.body.current=Y.current)});var Uo=c.useCallback(function(le){return c.createElement(c.Fragment,null,c.createElement(yr,le),Ie==="top"&&c.createElement(Gt,le,Se))},[Ie,Se]),Go=c.useCallback(function(le){return c.createElement(Gt,le,Se)},[Se]),er=P(["table"],"table"),Xt=c.useMemo(function(){return f||(Qe?he==="max-content"?"auto":"fixed":Le||et||X.some(function(le){var fe=le.ellipsis;return fe})?"fixed":"auto")},[Le,Qe,X,f,et]),sn,cn={colWidths:St,columCount:X.length,stickyOffsets:lt,onHeaderRow:N,fixHeader:Le,scroll:u},tr=c.useMemo(function(){return I?null:typeof C=="function"?C():C},[I,C]),nr=c.createElement(Tl,{data:D,measureColumnWidth:Le||ze||et}),rr=c.createElement(Co,{colWidths:X.map(function(le){var fe=le.width;return fe}),columns:X}),or=g!=null?c.createElement("caption",{className:"".concat(o,"-caption")},g):void 0,Yo=Jt(e,{data:!0}),ar=Jt(e,{aria:!0});if(Le||et){var dn;typeof W=="function"?(dn=W(D,{scrollbarSize:qt,ref:Y,onScroll:wt}),cn.colWidths=X.map(function(le,fe){var He=le.width,Ge=fe===X.length-1?He-qt:He;return typeof Ge=="number"&&!Number.isNaN(Ge)?Ge:0})):dn=c.createElement("div",{style:_(_({},je),Fe),onScroll:jo,ref:Y,className:Z("".concat(o,"-body"))},c.createElement(er,de({style:_(_({},it),{},{tableLayout:Xt})},ar),or,rr,nr,!Ie&&Se&&c.createElement(Gt,{stickyOffsets:lt,flattenColumns:X},Se)));var lr=_(_(_({noData:!D.length,maxContentScroll:ze&&he==="max-content"},cn),We),{},{direction:p,stickyClassName:oe,onScroll:wt});sn=c.createElement(c.Fragment,null,b!==!1&&c.createElement(hr,de({},lr,{stickyTopOffset:an,className:"".concat(o,"-header"),ref:ot}),Uo),dn,Ie&&Ie!=="top"&&c.createElement(hr,de({},lr,{stickyBottomOffset:Wt,className:"".concat(o,"-summary"),ref:Ce}),Go),et&&Y.current&&Y.current instanceof Element&&c.createElement(ti,{ref:Mt,offsetScroll:ln,scrollBodyRef:Y,onScroll:wt,container:ye}))}else sn=c.createElement("div",{style:_(_({},je),Fe),className:Z("".concat(o,"-content")),onScroll:wt,ref:Y},c.createElement(er,de({style:_(_({},it),{},{tableLayout:Xt})},ar),or,rr,b!==!1&&c.createElement(yr,de({},cn,We)),nr,Se&&c.createElement(Gt,{stickyOffsets:lt,flattenColumns:X},Se)));var un=c.createElement("div",de({className:Z(o,l,(r={},w(r,"".concat(o,"-rtl"),p==="rtl"),w(r,"".concat(o,"-ping-left"),Ee),w(r,"".concat(o,"-ping-right"),at),w(r,"".concat(o,"-layout-fixed"),f==="fixed"),w(r,"".concat(o,"-fixed-header"),Le),w(r,"".concat(o,"-fixed-column"),Qe),w(r,"".concat(o,"-fixed-column-gapped"),Qe&&xe),w(r,"".concat(o,"-scroll-horizontal"),ze),w(r,"".concat(o,"-has-fix-left"),X[0]&&X[0].fixed),w(r,"".concat(o,"-has-fix-right"),X[X.length-1]&&X[X.length-1].fixed==="right"),r)),style:a,id:y,ref:De},Yo),v&&c.createElement(Cr,{className:"".concat(o,"-title")},v(D)),c.createElement("div",{ref:be,className:"".concat(o,"-container")},sn),m&&c.createElement(Cr,{className:"".concat(o,"-footer")},m(D)));ze&&(un=c.createElement(Dn,{onResize:Wo},un));var ir=Gl(X,lt,p),Zo=c.useMemo(function(){return{scrollX:he,prefixCls:o,getComponent:P,scrollbarSize:qt,direction:p,fixedInfoList:ir,isSticky:et,supportSticky:Qn,componentWidth:A,fixHeader:Le,fixColumn:Qe,horizonScroll:ze,tableLayout:Xt,rowClassName:d,expandedRowClassName:te.expandedRowClassName,expandIcon:U,expandableType:ce,expandRowByClick:te.expandRowByClick,expandedRowRender:te.expandedRowRender,onTriggerExpand:F,expandIconColumnIndex:te.expandIconColumnIndex,indentSize:te.indentSize,allColumnsFixedLeft:X.every(function(le){return le.fixed==="left"}),emptyNode:tr,columns:we,flattenColumns:X,onColumnResize:Be,hoverStartRow:$e,hoverEndRow:ge,onHover:Ne,rowExpandable:te.rowExpandable,onRow:S,getRowKey:V,expandedKeys:Q,childrenColumnName:z,rowHoverable:L}},[he,o,P,qt,p,ir,et,Qn,A,Le,Qe,ze,Xt,d,te.expandedRowClassName,U,ce,te.expandRowByClick,te.expandedRowRender,F,te.expandIconColumnIndex,te.indentSize,tr,we,X,Be,$e,ge,Ne,te.rowExpandable,S,V,Q,z,L]);return c.createElement(Ue.Provider,{value:Zo},un)}var li=c.forwardRef(ai);function ko(n){return fo(li,n)}var Tt=ko();Tt.EXPAND_COLUMN=vt;Tt.INTERNAL_HOOKS=jt;Tt.Column=El;Tt.ColumnGroup=kl;Tt.Summary=mo;var Fn=Ln(null),$o=Ln(null);function ii(n,t,r){var e=t||1;return r[n+e]-(r[n]||0)}function si(n){var t=n.rowInfo,r=n.column,e=n.colIndex,o=n.indent,l=n.index,d=n.component,a=n.renderIndex,i=n.record,s=n.style,u=n.className,f=n.inverse,p=n.getHeight,v=r.render,m=r.dataIndex,h=r.className,g=r.width,y=Ae($o,["columnsOffset"]),b=y.columnsOffset,x=bo(t,r,e,o,l),C=x.key,S=x.fixedInfo,N=x.appendCellNode,k=x.additionalCellProps,T=k.style,E=k.colSpan,$=E===void 0?1:E,M=k.rowSpan,K=M===void 0?1:M,O=e-1,R=ii(O,$,b),L=$>1?g-R:0,D=_(_(_({},T),s),{},{flex:"0 0 ".concat(R,"px"),width:"".concat(R,"px"),marginRight:L,pointerEvents:"auto"}),I=c.useMemo(function(){return f?K<=1:$===0||K===0||K>1},[K,$,f]);I?D.visibility="hidden":f&&(D.height=p==null?void 0:p(K));var B=I?function(){return null}:v,P={};return(K===0||$===0)&&(P.rowSpan=1,P.colSpan=1),c.createElement(Ct,de({className:Z(h,u),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:C,record:i,index:l,renderIndex:a,dataIndex:m,render:B,shouldCellUpdate:r.shouldCellUpdate},S,{appendNode:N,additionalProps:_(_({},k),{},{style:D},P)}))}var ci=["data","index","className","rowKey","style","extra","getHeight"],di=c.forwardRef(function(n,t){var r=n.data,e=n.index,o=n.className,l=n.rowKey,d=n.style,a=n.extra,i=n.getHeight,s=Je(n,ci),u=r.record,f=r.indent,p=r.index,v=Ae(Ue,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),m=v.scrollX,h=v.flattenColumns,g=v.prefixCls,y=v.fixColumn,b=v.componentWidth,x=Ae(Fn,["getComponent"]),C=x.getComponent,S=yo(u,l,e,f),N=C(["body","row"],"div"),k=C(["body","cell"],"div"),T=S.rowSupportExpand,E=S.expanded,$=S.rowProps,M=S.expandedRowRender,K=S.expandedRowClassName,O;if(T&&E){var R=M(u,e,f+1,E),L=K==null?void 0:K(u,e,f),D={};y&&(D={style:w({},"--virtual-width","".concat(b,"px"))});var I="".concat(g,"-expanded-row-cell");O=c.createElement(N,{className:Z("".concat(g,"-expanded-row"),"".concat(g,"-expanded-row-level-").concat(f+1),L)},c.createElement(Ct,{component:k,prefixCls:g,className:Z(I,w({},"".concat(I,"-fixed"),y)),additionalProps:D},R))}var B=_(_({},d),{},{width:m});a&&(B.position="absolute",B.pointerEvents="none");var P=c.createElement(N,de({},$,s,{"data-row-key":l,ref:T?null:t,className:Z(o,"".concat(g,"-row"),$==null?void 0:$.className,w({},"".concat(g,"-row-extra"),a)),style:_(_({},B),$==null?void 0:$.style)}),h.map(function(V,W){return c.createElement(si,{key:W,component:k,rowInfo:S,column:V,colIndex:W,indent:f,index:e,renderIndex:p,record:u,inverse:a,getHeight:i})}));return T?c.createElement("div",{ref:t},P,O):P}),Sr=It(di),ui=c.forwardRef(function(n,t){var r=n.data,e=n.onScroll,o=Ae(Ue,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","emptyNode","scrollX"]),l=o.flattenColumns,d=o.onColumnResize,a=o.getRowKey,i=o.expandedKeys,s=o.prefixCls,u=o.childrenColumnName,f=o.emptyNode,p=o.scrollX,v=Ae(Fn),m=v.sticky,h=v.scrollY,g=v.listItemHeight,y=v.getComponent,b=v.onScroll,x=c.useRef(),C=ho(r,u,i,a),S=c.useMemo(function(){var D=0;return l.map(function(I){var B=I.width,P=I.key;return D+=B,[P,B,D]})},[l]),N=c.useMemo(function(){return S.map(function(D){return D[2]})},[S]);c.useEffect(function(){S.forEach(function(D){var I=me(D,2),B=I[0],P=I[1];d(B,P)})},[S]),c.useImperativeHandle(t,function(){var D={scrollTo:function(B){var P;(P=x.current)===null||P===void 0||P.scrollTo(B)}};return Object.defineProperty(D,"scrollLeft",{get:function(){var B;return((B=x.current)===null||B===void 0?void 0:B.getScrollInfo().x)||0},set:function(B){var P;(P=x.current)===null||P===void 0||P.scrollTo({left:B})}}),D});var k=function(I,B){var P,V=(P=C[B])===null||P===void 0?void 0:P.record,W=I.onCell;if(W){var re,J=W(V,B);return(re=J==null?void 0:J.rowSpan)!==null&&re!==void 0?re:1}return 1},T=function(I){var B=I.start,P=I.end,V=I.getSize,W=I.offsetY;if(P<0)return null;for(var re=l.filter(function(F){return k(F,B)===0}),J=B,$e=function(q){if(re=re.filter(function(ae){return k(ae,q)===0}),!re.length)return J=q,1},ge=B;ge>=0&&!$e(ge);ge-=1);for(var Ne=l.filter(function(F){return k(F,P)!==1}),ee=P,pe=function(q){if(Ne=Ne.filter(function(ae){return k(ae,q)!==1}),!Ne.length)return ee=Math.max(q-1,P),1},te=P;te<C.length&&!pe(te);te+=1);for(var ce=[],Q=function(q){var ae=C[q];if(!ae)return 1;l.some(function(ne){return k(ne,q)>1})&&ce.push(q)},U=J;U<=ee;U+=1)Q(U);var z=ce.map(function(F){var q=C[F],ae=a(q.record,F),ne=function(G){var ue=F+G-1,we=a(C[ue].record,ue),X=V(ae,we);return X.bottom-X.top},A=V(ae);return c.createElement(Sr,{key:F,data:q,rowKey:ae,index:F,style:{top:-W+A.top},extra:!0,getHeight:ne})});return z},E=c.useMemo(function(){return{columnsOffset:N}},[N]),$="".concat(s,"-tbody"),M=y(["body","wrapper"]),K=y(["body","row"],"div"),O=y(["body","cell"],"div"),R;if(C.length){var L={};m&&(L.position="sticky",L.bottom=0,nt(m)==="object"&&m.offsetScroll&&(L.bottom=m.offsetScroll)),R=c.createElement(no,{fullHeight:!1,ref:x,prefixCls:"".concat($,"-virtual"),styles:{horizontalScrollBar:L},className:$,height:h,itemHeight:g||24,data:C,itemKey:function(I){return a(I.record)},component:M,scrollWidth:p,onVirtualScroll:function(I){var B=I.x;e({scrollLeft:B})},onScroll:b,extraRender:T},function(D,I,B){var P=a(D.record,I);return c.createElement(Sr,{data:D,rowKey:P,index:I,style:B.style})})}else R=c.createElement(K,{className:Z("".concat(s,"-placeholder"))},c.createElement(Ct,{component:O,prefixCls:s},f));return c.createElement($o.Provider,{value:E},R)}),fi=It(ui),pi=function(t,r){var e=r.ref,o=r.onScroll;return c.createElement(fi,{ref:e,data:t,onScroll:o})};function vi(n,t){var r=n.columns,e=n.scroll,o=n.sticky,l=n.prefixCls,d=l===void 0?Eo:l,a=n.className,i=n.listItemHeight,s=n.components,u=n.onScroll,f=e||{},p=f.x,v=f.y;typeof p!="number"&&(p=1),typeof v!="number"&&(v=500);var m=gt(function(y,b){return Tn(s,y)||b}),h=gt(u),g=c.useMemo(function(){return{sticky:o,scrollY:v,listItemHeight:i,getComponent:m,onScroll:h}},[o,v,i,m,h]);return c.createElement(Fn.Provider,{value:g},c.createElement(Tt,de({},n,{className:Z(a,"".concat(d,"-virtual")),scroll:_(_({},e),{},{x:p}),components:_(_({},s),{},{body:pi}),columns:r,internalHooks:jt,tailor:!0,ref:t})))}var mi=c.forwardRef(vi);function No(n){return fo(mi,n)}No();const gi=n=>null,hi=n=>null;var An=c.createContext(null),yi=function(t){for(var r=t.prefixCls,e=t.level,o=t.isStart,l=t.isEnd,d="".concat(r,"-indent-unit"),a=[],i=0;i<e;i+=1)a.push(c.createElement("span",{key:i,className:Z(d,w(w({},"".concat(d,"-start"),o[i]),"".concat(d,"-end"),l[i]))}));return c.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},a)};const xi=c.memo(yi);var bi=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],wr="open",Er="close",Ci="---",Si=function(n){Xr(r,n);var t=Ur(r);function r(){var e;Gr(this,r);for(var o=arguments.length,l=new Array(o),d=0;d<o;d++)l[d]=arguments[d];return e=t.call.apply(t,[this].concat(l)),w(H(e),"state",{dragNodeHighlight:!1}),w(H(e),"selectHandle",void 0),w(H(e),"cacheIndent",void 0),w(H(e),"onSelectorClick",function(a){var i=e.props.context.onNodeClick;i(a,Oe(e.props)),e.isSelectable()?e.onSelect(a):e.onCheck(a)}),w(H(e),"onSelectorDoubleClick",function(a){var i=e.props.context.onNodeDoubleClick;i(a,Oe(e.props))}),w(H(e),"onSelect",function(a){if(!e.isDisabled()){var i=e.props.context.onNodeSelect;i(a,Oe(e.props))}}),w(H(e),"onCheck",function(a){if(!e.isDisabled()){var i=e.props,s=i.disableCheckbox,u=i.checked,f=e.props.context.onNodeCheck;if(!(!e.isCheckable()||s)){var p=!u;f(a,Oe(e.props),p)}}}),w(H(e),"onMouseEnter",function(a){var i=e.props.context.onNodeMouseEnter;i(a,Oe(e.props))}),w(H(e),"onMouseLeave",function(a){var i=e.props.context.onNodeMouseLeave;i(a,Oe(e.props))}),w(H(e),"onContextMenu",function(a){var i=e.props.context.onNodeContextMenu;i(a,Oe(e.props))}),w(H(e),"onDragStart",function(a){var i=e.props.context.onNodeDragStart;a.stopPropagation(),e.setState({dragNodeHighlight:!0}),i(a,H(e));try{a.dataTransfer.setData("text/plain","")}catch{}}),w(H(e),"onDragEnter",function(a){var i=e.props.context.onNodeDragEnter;a.preventDefault(),a.stopPropagation(),i(a,H(e))}),w(H(e),"onDragOver",function(a){var i=e.props.context.onNodeDragOver;a.preventDefault(),a.stopPropagation(),i(a,H(e))}),w(H(e),"onDragLeave",function(a){var i=e.props.context.onNodeDragLeave;a.stopPropagation(),i(a,H(e))}),w(H(e),"onDragEnd",function(a){var i=e.props.context.onNodeDragEnd;a.stopPropagation(),e.setState({dragNodeHighlight:!1}),i(a,H(e))}),w(H(e),"onDrop",function(a){var i=e.props.context.onNodeDrop;a.preventDefault(),a.stopPropagation(),e.setState({dragNodeHighlight:!1}),i(a,H(e))}),w(H(e),"onExpand",function(a){var i=e.props,s=i.loading,u=i.context.onNodeExpand;s||u(a,Oe(e.props))}),w(H(e),"setSelectHandle",function(a){e.selectHandle=a}),w(H(e),"getNodeState",function(){var a=e.props.expanded;return e.isLeaf()?null:a?wr:Er}),w(H(e),"hasChildren",function(){var a=e.props.eventKey,i=e.props.context.keyEntities,s=Xe(i,a)||{},u=s.children;return!!(u||[]).length}),w(H(e),"isLeaf",function(){var a=e.props,i=a.isLeaf,s=a.loaded,u=e.props.context.loadData,f=e.hasChildren();return i===!1?!1:i||!u&&!f||u&&s&&!f}),w(H(e),"isDisabled",function(){var a=e.props.disabled,i=e.props.context.disabled;return!!(i||a)}),w(H(e),"isCheckable",function(){var a=e.props.checkable,i=e.props.context.checkable;return!i||a===!1?!1:i}),w(H(e),"syncLoadData",function(a){var i=a.expanded,s=a.loading,u=a.loaded,f=e.props.context,p=f.loadData,v=f.onNodeLoad;s||p&&i&&!e.isLeaf()&&!u&&v(Oe(e.props))}),w(H(e),"isDraggable",function(){var a=e.props,i=a.data,s=a.context.draggable;return!!(s&&(!s.nodeDraggable||s.nodeDraggable(i)))}),w(H(e),"renderDragHandler",function(){var a=e.props.context,i=a.draggable,s=a.prefixCls;return i!=null&&i.icon?c.createElement("span",{className:"".concat(s,"-draggable-icon")},i.icon):null}),w(H(e),"renderSwitcherIconDom",function(a){var i=e.props.switcherIcon,s=e.props.context.switcherIcon,u=i||s;return typeof u=="function"?u(_(_({},e.props),{},{isLeaf:a})):u}),w(H(e),"renderSwitcher",function(){var a=e.props.expanded,i=e.props.context.prefixCls;if(e.isLeaf()){var s=e.renderSwitcherIconDom(!0);return s!==!1?c.createElement("span",{className:Z("".concat(i,"-switcher"),"".concat(i,"-switcher-noop"))},s):null}var u=Z("".concat(i,"-switcher"),"".concat(i,"-switcher_").concat(a?wr:Er)),f=e.renderSwitcherIconDom(!1);return f!==!1?c.createElement("span",{onClick:e.onExpand,className:u},f):null}),w(H(e),"renderCheckbox",function(){var a=e.props,i=a.checked,s=a.halfChecked,u=a.disableCheckbox,f=e.props.context.prefixCls,p=e.isDisabled(),v=e.isCheckable();if(!v)return null;var m=typeof v!="boolean"?v:null;return c.createElement("span",{className:Z("".concat(f,"-checkbox"),i&&"".concat(f,"-checkbox-checked"),!i&&s&&"".concat(f,"-checkbox-indeterminate"),(p||u)&&"".concat(f,"-checkbox-disabled")),onClick:e.onCheck},m)}),w(H(e),"renderIcon",function(){var a=e.props.loading,i=e.props.context.prefixCls;return c.createElement("span",{className:Z("".concat(i,"-iconEle"),"".concat(i,"-icon__").concat(e.getNodeState()||"docu"),a&&"".concat(i,"-icon_loading"))})}),w(H(e),"renderSelector",function(){var a=e.state.dragNodeHighlight,i=e.props,s=i.title,u=s===void 0?Ci:s,f=i.selected,p=i.icon,v=i.loading,m=i.data,h=e.props.context,g=h.prefixCls,y=h.showIcon,b=h.icon,x=h.loadData,C=h.titleRender,S=e.isDisabled(),N="".concat(g,"-node-content-wrapper"),k;if(y){var T=p||b;k=T?c.createElement("span",{className:Z("".concat(g,"-iconEle"),"".concat(g,"-icon__customize"))},typeof T=="function"?T(e.props):T):e.renderIcon()}else x&&v&&(k=e.renderIcon());var E;typeof u=="function"?E=u(m):C?E=C(m):E=u;var $=c.createElement("span",{className:"".concat(g,"-title")},E);return c.createElement("span",{ref:e.setSelectHandle,title:typeof u=="string"?u:"",className:Z("".concat(N),"".concat(N,"-").concat(e.getNodeState()||"normal"),!S&&(f||a)&&"".concat(g,"-node-selected")),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onContextMenu:e.onContextMenu,onClick:e.onSelectorClick,onDoubleClick:e.onSelectorDoubleClick},k,$,e.renderDropIndicator())}),w(H(e),"renderDropIndicator",function(){var a=e.props,i=a.disabled,s=a.eventKey,u=e.props.context,f=u.draggable,p=u.dropLevelOffset,v=u.dropPosition,m=u.prefixCls,h=u.indent,g=u.dropIndicatorRender,y=u.dragOverNodeKey,b=u.direction,x=!!f,C=!i&&x&&y===s,S=h??e.cacheIndent;return e.cacheIndent=h,C?g({dropPosition:v,dropLevelOffset:p,indent:S,prefixCls:m,direction:b}):null}),e}return Yr(r,[{key:"componentDidMount",value:function(){this.syncLoadData(this.props)}},{key:"componentDidUpdate",value:function(){this.syncLoadData(this.props)}},{key:"isSelectable",value:function(){var o=this.props.selectable,l=this.props.context.selectable;return typeof o=="boolean"?o:l}},{key:"render",value:function(){var o,l=this.props,d=l.eventKey,a=l.className,i=l.style,s=l.dragOver,u=l.dragOverGapTop,f=l.dragOverGapBottom,p=l.isLeaf,v=l.isStart,m=l.isEnd,h=l.expanded,g=l.selected,y=l.checked,b=l.halfChecked,x=l.loading,C=l.domRef,S=l.active;l.data;var N=l.onMouseMove,k=l.selectable,T=Je(l,bi),E=this.props.context,$=E.prefixCls,M=E.filterTreeNode,K=E.keyEntities,O=E.dropContainerKey,R=E.dropTargetKey,L=E.draggingNodeKey,D=this.isDisabled(),I=Jt(T,{aria:!0,data:!0}),B=Xe(K,d)||{},P=B.level,V=m[m.length-1],W=this.isDraggable(),re=!D&&W,J=L===d,$e=k!==void 0?{"aria-selected":!!k}:void 0;return c.createElement("div",de({ref:C,className:Z(a,"".concat($,"-treenode"),(o={},w(w(w(w(w(w(w(w(w(w(o,"".concat($,"-treenode-disabled"),D),"".concat($,"-treenode-switcher-").concat(h?"open":"close"),!p),"".concat($,"-treenode-checkbox-checked"),y),"".concat($,"-treenode-checkbox-indeterminate"),b),"".concat($,"-treenode-selected"),g),"".concat($,"-treenode-loading"),x),"".concat($,"-treenode-active"),S),"".concat($,"-treenode-leaf-last"),V),"".concat($,"-treenode-draggable"),W),"dragging",J),w(w(w(w(w(w(o,"drop-target",R===d),"drop-container",O===d),"drag-over",!D&&s),"drag-over-gap-top",!D&&u),"drag-over-gap-bottom",!D&&f),"filter-node",M&&M(Oe(this.props))))),style:i,draggable:re,"aria-grabbed":J,onDragStart:re?this.onDragStart:void 0,onDragEnter:W?this.onDragEnter:void 0,onDragOver:W?this.onDragOver:void 0,onDragLeave:W?this.onDragLeave:void 0,onDrop:W?this.onDrop:void 0,onDragEnd:W?this.onDragEnd:void 0,onMouseMove:N},$e,I),c.createElement(xi,{prefixCls:$,level:P,isStart:v,isEnd:m}),this.renderDragHandler(),this.renderSwitcher(),this.renderCheckbox(),this.renderSelector())}}]),r}(c.Component),Rt=function(t){return c.createElement(An.Consumer,null,function(r){return c.createElement(Si,de({},t,{context:r}))})};Rt.displayName="TreeNode";Rt.isTreeNode=1;function tt(n,t){if(!n)return[];var r=n.slice(),e=r.indexOf(t);return e>=0&&r.splice(e,1),r}function st(n,t){var r=(n||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function zn(n){return n.split("-")}function wi(n,t){var r=[],e=Xe(t,n);function o(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];l.forEach(function(d){var a=d.key,i=d.children;r.push(a),o(i)})}return o(e.children),r}function Ei(n){if(n.parent){var t=zn(n.pos);return Number(t[t.length-1])===n.parent.children.length-1}return!1}function ki(n){var t=zn(n.pos);return Number(t[t.length-1])===0}function kr(n,t,r,e,o,l,d,a,i,s){var u,f=n.clientX,p=n.clientY,v=n.target.getBoundingClientRect(),m=v.top,h=v.height,g=(s==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-f),y=(g-12)/e,b=i.filter(function(D){var I;return(I=a[D])===null||I===void 0||(I=I.children)===null||I===void 0?void 0:I.length}),x=Xe(a,r.props.eventKey);if(p<m+h/2){var C=d.findIndex(function(D){return D.key===x.key}),S=C<=0?0:C-1,N=d[S].key;x=Xe(a,N)}var k=x.key,T=x,E=x.key,$=0,M=0;if(!b.includes(k))for(var K=0;K<y&&Ei(x);K+=1)x=x.parent,M+=1;var O=t.props.data,R=x.node,L=!0;return ki(x)&&x.level===0&&p<m+h/2&&l({dragNode:O,dropNode:R,dropPosition:-1})&&x.key===r.props.eventKey?$=-1:(T.children||[]).length&&b.includes(E)?l({dragNode:O,dropNode:R,dropPosition:0})?$=0:L=!1:M===0?y>-1.5?l({dragNode:O,dropNode:R,dropPosition:1})?$=1:L=!1:l({dragNode:O,dropNode:R,dropPosition:0})?$=0:l({dragNode:O,dropNode:R,dropPosition:1})?$=1:L=!1:l({dragNode:O,dropNode:R,dropPosition:1})?$=1:L=!1,{dropPosition:$,dropLevelOffset:M,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:E,dropContainerKey:$===0?null:((u=x.parent)===null||u===void 0?void 0:u.key)||null,dropAllowed:L}}function $r(n,t){if(n){var r=t.multiple;return r?n.slice():n.length?[n[0]]:n}}function vn(n){if(!n)return null;var t;if(Array.isArray(n))t={checkedKeys:n,halfCheckedKeys:void 0};else if(nt(n)==="object")t={checkedKeys:n.checked||void 0,halfCheckedKeys:n.halfChecked||void 0};else return mt(!1,"`checkedKeys` is not an array or an object"),null;return t}function Sn(n,t){var r=new Set;function e(o){if(!r.has(o)){var l=Xe(t,o);if(l){r.add(o);var d=l.parent,a=l.node;a.disabled||d&&e(d.key)}}}return(n||[]).forEach(function(o){e(o)}),se(r)}function $i(n){const[t,r]=c.useState(null);return[c.useCallback((l,d,a)=>{const i=t??l,s=Math.min(i||0,l),u=Math.max(i||0,l),f=d.slice(s,u+1).map(m=>n(m)),p=f.some(m=>!a.has(m)),v=[];return f.forEach(m=>{p?(a.has(m)||v.push(m),a.add(m)):(a.delete(m),v.push(m))}),r(p?u:null),v},[t]),l=>{r(l)}]}const ft={},wn="SELECT_ALL",En="SELECT_INVERT",kn="SELECT_NONE",Nr=[],Ko=(n,t)=>{let r=[];return(t||[]).forEach(e=>{r.push(e),e&&typeof e=="object"&&n in e&&(r=[].concat(se(r),se(Ko(n,e[n]))))}),r},Ni=(n,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:e,defaultSelectedRowKeys:o,getCheckboxProps:l,onChange:d,onSelect:a,onSelectAll:i,onSelectInvert:s,onSelectNone:u,onSelectMultiple:f,columnWidth:p,type:v,selections:m,fixed:h,renderCell:g,hideSelectAll:y,checkStrictly:b=!0}=t||{},{prefixCls:x,data:C,pageData:S,getRecordByKey:N,getRowKey:k,expandType:T,childrenColumnName:E,locale:$,getPopupContainer:M}=n,K=On(),[O,R]=$i(Q=>Q),[L,D]=ca(e||o||Nr,{value:e}),I=c.useRef(new Map),B=c.useCallback(Q=>{if(r){const U=new Map;Q.forEach(z=>{let F=N(z);!F&&I.current.has(z)&&(F=I.current.get(z)),U.set(z,F)}),I.current=U}},[N,r]);c.useEffect(()=>{B(L)},[L]);const{keyEntities:P}=c.useMemo(()=>{if(b)return{keyEntities:null};let Q=C;if(r){const U=new Set(C.map((F,q)=>k(F,q))),z=Array.from(I.current).reduce((F,q)=>{let[ae,ne]=q;return U.has(ae)?F:F.concat(ne)},[]);Q=[].concat(se(Q),se(z))}return Mn(Q,{externalGetKey:k,childrenPropName:E})},[C,k,b,E,r]),V=c.useMemo(()=>Ko(E,S),[E,S]),W=c.useMemo(()=>{const Q=new Map;return V.forEach((U,z)=>{const F=k(U,z),q=(l?l(U):null)||{};Q.set(F,q)}),Q},[V,k,l]),re=c.useCallback(Q=>{var U;return!!(!((U=W.get(k(Q)))===null||U===void 0)&&U.disabled)},[W,k]),[J,$e]=c.useMemo(()=>{if(b)return[L||[],[]];const{checkedKeys:Q,halfCheckedKeys:U}=kt(L,!0,P,re);return[Q||[],U]},[L,b,P,re]),ge=c.useMemo(()=>{const Q=v==="radio"?J.slice(0,1):J;return new Set(Q)},[J,v]),Ne=c.useMemo(()=>v==="radio"?new Set:new Set($e),[$e,v]);c.useEffect(()=>{t||D(Nr)},[!!t]);const ee=c.useCallback((Q,U)=>{let z,F;B(Q),r?(z=Q,F=Q.map(q=>I.current.get(q))):(z=[],F=[],Q.forEach(q=>{const ae=N(q);ae!==void 0&&(z.push(q),F.push(ae))})),D(z),d==null||d(z,F,{type:U})},[D,N,d,r]),pe=c.useCallback((Q,U,z,F)=>{if(a){const q=z.map(ae=>N(ae));a(N(Q),U,q,F)}ee(z,"single")},[a,N,ee]),te=c.useMemo(()=>!m||y?null:(m===!0?[wn,En,kn]:m).map(U=>U===wn?{key:"all",text:$.selectionAll,onSelect(){ee(C.map((z,F)=>k(z,F)).filter(z=>{const F=W.get(z);return!(F!=null&&F.disabled)||ge.has(z)}),"all")}}:U===En?{key:"invert",text:$.selectInvert,onSelect(){const z=new Set(ge);S.forEach((q,ae)=>{const ne=k(q,ae),A=W.get(ne);A!=null&&A.disabled||(z.has(ne)?z.delete(ne):z.add(ne))});const F=Array.from(z);s&&(K.deprecated(!1,"onSelectInvert","onChange"),s(F)),ee(F,"invert")}}:U===kn?{key:"none",text:$.selectNone,onSelect(){u==null||u(),ee(Array.from(ge).filter(z=>{const F=W.get(z);return F==null?void 0:F.disabled}),"none")}}:U).map(U=>Object.assign(Object.assign({},U),{onSelect:function(){for(var z,F,q=arguments.length,ae=new Array(q),ne=0;ne<q;ne++)ae[ne]=arguments[ne];(F=U.onSelect)===null||F===void 0||(z=F).call.apply(z,[U].concat(ae)),R(null)}})),[m,ge,S,k,s,ee]);return[c.useCallback(Q=>{var U;if(!t)return Q.filter(Y=>Y!==ft);let z=se(Q);const F=new Set(ge),q=V.map(k).filter(Y=>!W.get(Y).disabled),ae=q.every(Y=>F.has(Y)),ne=q.some(Y=>F.has(Y)),A=()=>{const Y=[];ae?q.forEach(Ce=>{F.delete(Ce),Y.push(Ce)}):q.forEach(Ce=>{F.has(Ce)||(F.add(Ce),Y.push(Ce))});const be=Array.from(F);i==null||i(!ae,be.map(Ce=>N(Ce)),Y.map(Ce=>N(Ce))),ee(be,"all"),R(null)};let ie,G;if(v!=="radio"){let Y;if(te){const Ee={getPopupContainer:M,items:te.map((Te,Me)=>{const{key:Re,text:at,onSelect:Ve}=Te;return{key:Re??Me,onClick:()=>{Ve==null||Ve(q)},label:at}})};Y=c.createElement("div",{className:`${x}-selection-extra`},c.createElement(ro,{menu:Ee,getPopupContainer:M},c.createElement("span",null,c.createElement(da,null))))}const be=V.map((Ee,Te)=>{const Me=k(Ee,Te),Re=W.get(Me)||{};return Object.assign({checked:F.has(Me)},Re)}).filter(Ee=>{let{disabled:Te}=Ee;return Te}),Ce=!!be.length&&be.length===V.length,Pe=Ce&&be.every(Ee=>{let{checked:Te}=Ee;return Te}),Ke=Ce&&be.some(Ee=>{let{checked:Te}=Ee;return Te});G=c.createElement(Qt,{checked:Ce?Pe:!!V.length&&ae,indeterminate:Ce?!Pe&&Ke:!ae&&ne,onChange:A,disabled:V.length===0||Ce,"aria-label":Y?"Custom selection":"Select all",skipGroup:!0}),ie=!y&&c.createElement("div",{className:`${x}-selection`},G,Y)}let ue;v==="radio"?ue=(Y,be,Ce)=>{const Pe=k(be,Ce),Ke=F.has(Pe);return{node:c.createElement(oo,Object.assign({},W.get(Pe),{checked:Ke,onClick:Ee=>Ee.stopPropagation(),onChange:Ee=>{F.has(Pe)||pe(Pe,!0,[Pe],Ee.nativeEvent)}})),checked:Ke}}:ue=(Y,be,Ce)=>{var Pe;const Ke=k(be,Ce),Ee=F.has(Ke),Te=Ne.has(Ke),Me=W.get(Ke);let Re;return T==="nest"?Re=Te:Re=(Pe=Me==null?void 0:Me.indeterminate)!==null&&Pe!==void 0?Pe:Te,{node:c.createElement(Qt,Object.assign({},Me,{indeterminate:Re,checked:Ee,skipGroup:!0,onClick:at=>at.stopPropagation(),onChange:at=>{let{nativeEvent:Ve}=at;const{shiftKey:Pt}=Ve,ke=q.findIndex(qe=>qe===Ke),Dt=J.some(qe=>q.includes(qe));if(Pt&&b&&Dt){const qe=O(ke,q,F),Ye=Array.from(F);f==null||f(!Ee,Ye.map(Ze=>N(Ze)),qe.map(Ze=>N(Ze))),ee(Ye,"multiple")}else{const qe=J;if(b){const Ye=Ee?tt(qe,Ke):st(qe,Ke);pe(Ke,!Ee,Ye,Ve)}else{const Ye=kt([].concat(se(qe),[Ke]),!0,P,re),{checkedKeys:Ze,halfCheckedKeys:St}=Ye;let lt=Ze;if(Ee){const Le=new Set(Ze);Le.delete(Ke),lt=kt(Array.from(Le),{checked:!1,halfCheckedKeys:St},P,re).checkedKeys}pe(Ke,!Ee,lt,Ve)}}R(Ee?null:ke)}})),checked:Ee}};const we=(Y,be,Ce)=>{const{node:Pe,checked:Ke}=ue(Y,be,Ce);return g?g(Ke,be,Ce,Pe):Pe};if(!z.includes(ft))if(z.findIndex(Y=>{var be;return((be=Y[Ft])===null||be===void 0?void 0:be.columnType)==="EXPAND_COLUMN"})===0){const[Y,...be]=z;z=[Y,ft].concat(se(be))}else z=[ft].concat(se(z));const X=z.indexOf(ft);z=z.filter((Y,be)=>Y!==ft||be===X);const ve=z[X-1],xe=z[X+1];let he=h;he===void 0&&((xe==null?void 0:xe.fixed)!==void 0?he=xe.fixed:(ve==null?void 0:ve.fixed)!==void 0&&(he=ve.fixed)),he&&ve&&((U=ve[Ft])===null||U===void 0?void 0:U.columnType)==="EXPAND_COLUMN"&&ve.fixed===void 0&&(ve.fixed=he);const We=Z(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:m&&v==="checkbox"}),De=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(G):t.columnTitle:ie,ot={fixed:he,width:p,className:`${x}-selection-column`,title:De(),render:we,onCell:t.onCell,[Ft]:{className:We}};return z.map(Y=>Y===ft?ot:Y)},[k,V,t,J,ge,Ne,p,te,T,W,f,pe,re]),ge]};function Ki(n,t){return n._antProxy=n._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in n._antProxy)){const e=n[r];n._antProxy[r]=e,n[r]=t[r]}}),n}function Ri(n,t){return c.useImperativeHandle(n,()=>{const r=t(),{nativeElement:e}=r;return typeof Proxy<"u"?new Proxy(e,{get(o,l){return r[l]?r[l]:Reflect.get(o,l)}}):Ki(e,r)})}function Ii(n){return t=>{const{prefixCls:r,onExpand:e,record:o,expanded:l,expandable:d}=t,a=`${r}-row-expand-icon`;return c.createElement("button",{type:"button",onClick:i=>{e(o,i),i.stopPropagation()},className:Z(a,{[`${a}-spaced`]:!d,[`${a}-expanded`]:d&&l,[`${a}-collapsed`]:d&&!l}),"aria-label":l?n.collapse:n.expand,"aria-expanded":l})}}function Ti(n){return(r,e)=>{const o=r.querySelector(`.${n}-container`);let l=e;if(o){const d=getComputedStyle(o),a=parseInt(d.borderLeftWidth,10),i=parseInt(d.borderRightWidth,10);l=e-a-i}return l}}const ht=(n,t)=>"key"in n&&n.key!==void 0&&n.key!==null?n.key:n.dataIndex?Array.isArray(n.dataIndex)?n.dataIndex.join("."):n.dataIndex:t;function Ot(n,t){return t?`${t}-${n}`:`${n}`}const on=(n,t)=>typeof n=="function"?n(t):n,Oi=(n,t)=>{const r=on(n,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Pi(n){const t=c.useRef(n),r=ya();return[()=>t.current,e=>{t.current=e,r()}]}function Di(n){var t=n.dropPosition,r=n.dropLevelOffset,e=n.indent,o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(t){case-1:o.top=0,o.left=-r*e;break;case 1:o.bottom=0,o.left=-r*e;break;case 0:o.bottom=0,o.left=e;break}return c.createElement("div",{style:o})}function Ro(n){if(n==null)throw new TypeError("Cannot destructure "+n)}function Mi(n,t){var r=c.useState(!1),e=me(r,2),o=e[0],l=e[1];Nt(function(){if(o)return n(),function(){t()}},[o]),Nt(function(){return l(!0),function(){l(!1)}},[])}var Li=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Io=function(t,r){var e=t.className,o=t.style,l=t.motion,d=t.motionNodes,a=t.motionType,i=t.onMotionStart,s=t.onMotionEnd,u=t.active,f=t.treeNodeRequiredProps,p=Je(t,Li),v=c.useState(!0),m=me(v,2),h=m[0],g=m[1],y=c.useContext(An),b=y.prefixCls,x=d&&a!=="hide";Nt(function(){d&&x!==h&&g(x)},[d]);var C=function(){d&&i()},S=c.useRef(!1),N=function(){d&&!S.current&&(S.current=!0,s())};Mi(C,N);var k=function(E){x===E&&N()};return d?c.createElement(Qo,de({ref:r,visible:h},l,{motionAppear:a==="show",onVisibleChanged:k}),function(T,E){var $=T.className,M=T.style;return c.createElement("div",{ref:E,className:Z("".concat(b,"-treenode-motion"),$),style:M},d.map(function(K){var O=Object.assign({},(Ro(K.data),K.data)),R=K.title,L=K.key,D=K.isStart,I=K.isEnd;delete O.children;var B=_t(L,f);return c.createElement(Rt,de({},O,B,{title:R,active:u,data:K.data,key:L,isStart:D,isEnd:I}))}))}):c.createElement(Rt,de({domRef:r,className:e,style:o},p,{active:u}))};Io.displayName="MotionTreeNode";var Bi=c.forwardRef(Io);function Hi(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=n.length,e=t.length;if(Math.abs(r-e)!==1)return{add:!1,key:null};function o(l,d){var a=new Map;l.forEach(function(s){a.set(s,!0)});var i=d.filter(function(s){return!a.has(s)});return i.length===1?i[0]:null}return r<e?{add:!0,key:o(n,t)}:{add:!1,key:o(t,n)}}function Kr(n,t,r){var e=n.findIndex(function(a){return a.key===r}),o=n[e+1],l=t.findIndex(function(a){return a.key===r});if(o){var d=t.findIndex(function(a){return a.key===o.key});return t.slice(l+1,d)}return t.slice(l+1)}var _i=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],Rr={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Fi=function(){},bt="RC_TREE_MOTION_".concat(Math.random()),$n={key:bt},To={key:bt,level:0,index:0,pos:"0",node:$n,nodes:[$n]},Ir={parent:null,children:[],pos:To.pos,data:$n,title:null,key:bt,isStart:[],isEnd:[]};function Tr(n,t,r,e){return t===!1||!r?n:n.slice(0,Math.ceil(r/e)+1)}function Or(n){var t=n.key,r=n.pos;return zt(t,r)}function Ai(n){for(var t=String(n.data.key),r=n;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var Oo=c.forwardRef(function(n,t){var r=n.prefixCls,e=n.data;n.selectable,n.checkable;var o=n.expandedKeys,l=n.selectedKeys,d=n.checkedKeys,a=n.loadedKeys,i=n.loadingKeys,s=n.halfCheckedKeys,u=n.keyEntities,f=n.disabled,p=n.dragging,v=n.dragOverNodeKey,m=n.dropPosition,h=n.motion,g=n.height,y=n.itemHeight,b=n.virtual,x=n.focusable,C=n.activeItem,S=n.focused,N=n.tabIndex,k=n.onKeyDown,T=n.onFocus,E=n.onBlur,$=n.onActiveChange,M=n.onListChangeStart,K=n.onListChangeEnd,O=Je(n,_i),R=c.useRef(null),L=c.useRef(null);c.useImperativeHandle(t,function(){return{scrollTo:function(ue){R.current.scrollTo(ue)},getIndentWidth:function(){return L.current.offsetWidth}}});var D=c.useState(o),I=me(D,2),B=I[0],P=I[1],V=c.useState(e),W=me(V,2),re=W[0],J=W[1],$e=c.useState(e),ge=me($e,2),Ne=ge[0],ee=ge[1],pe=c.useState([]),te=me(pe,2),ce=te[0],Q=te[1],U=c.useState(null),z=me(U,2),F=z[0],q=z[1],ae=c.useRef(e);ae.current=e;function ne(){var G=ae.current;J(G),ee(G),Q([]),q(null),K()}Nt(function(){P(o);var G=Hi(B,o);if(G.key!==null)if(G.add){var ue=re.findIndex(function(We){var De=We.key;return De===G.key}),we=Tr(Kr(re,e,G.key),b,g,y),X=re.slice();X.splice(ue+1,0,Ir),ee(X),Q(we),q("show")}else{var ve=e.findIndex(function(We){var De=We.key;return De===G.key}),xe=Tr(Kr(e,re,G.key),b,g,y),he=e.slice();he.splice(ve+1,0,Ir),ee(he),Q(xe),q("hide")}else re!==e&&(J(e),ee(e))},[o,e]),c.useEffect(function(){p||ne()},[p]);var A=h?Ne:e,ie={expandedKeys:o,selectedKeys:l,loadedKeys:a,loadingKeys:i,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:v,dropPosition:m,keyEntities:u};return c.createElement(c.Fragment,null,S&&C&&c.createElement("span",{style:Rr,"aria-live":"assertive"},Ai(C)),c.createElement("div",null,c.createElement("input",{style:Rr,disabled:x===!1||f,tabIndex:x!==!1?N:null,onKeyDown:k,onFocus:T,onBlur:E,value:"",onChange:Fi,"aria-label":"for screen reader"})),c.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},c.createElement("div",{className:"".concat(r,"-indent")},c.createElement("div",{ref:L,className:"".concat(r,"-indent-unit")}))),c.createElement(no,de({},O,{data:A,itemKey:Or,height:g,fullHeight:!1,virtual:b,itemHeight:y,prefixCls:"".concat(r,"-list"),ref:R,onVisibleChange:function(ue,we){var X=new Set(ue),ve=we.filter(function(xe){return!X.has(xe)});ve.some(function(xe){return Or(xe)===bt})&&ne()}}),function(G){var ue=G.pos,we=Object.assign({},(Ro(G.data),G.data)),X=G.title,ve=G.key,xe=G.isStart,he=G.isEnd,We=zt(ve,ue);delete we.key,delete we.children;var De=_t(We,ie);return c.createElement(Bi,de({},we,De,{title:X,active:!!C&&ve===C.key,pos:ue,data:G.data,isStart:xe,isEnd:he,motion:h,motionNodes:ve===bt?ce:null,motionType:F,onMotionStart:M,onMotionEnd:ne,treeNodeRequiredProps:ie,onMouseMove:function(){$(null)}}))}))});Oo.displayName="NodeList";var zi=10,jn=function(n){Xr(r,n);var t=Ur(r);function r(){var e;Gr(this,r);for(var o=arguments.length,l=new Array(o),d=0;d<o;d++)l[d]=arguments[d];return e=t.call.apply(t,[this].concat(l)),w(H(e),"destroyed",!1),w(H(e),"delayedDragEnterLogic",void 0),w(H(e),"loadingRetryTimes",{}),w(H(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Kt()}),w(H(e),"dragStartMousePosition",null),w(H(e),"dragNode",void 0),w(H(e),"currentMouseOverDroppableNodeKey",null),w(H(e),"listRef",c.createRef()),w(H(e),"onNodeDragStart",function(a,i){var s=e.state,u=s.expandedKeys,f=s.keyEntities,p=e.props.onDragStart,v=i.props.eventKey;e.dragNode=i,e.dragStartMousePosition={x:a.clientX,y:a.clientY};var m=tt(u,v);e.setState({draggingNodeKey:v,dragChildrenKeys:wi(v,f),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(m),window.addEventListener("dragend",e.onWindowDragEnd),p==null||p({event:a,node:Oe(i.props)})}),w(H(e),"onNodeDragEnter",function(a,i){var s=e.state,u=s.expandedKeys,f=s.keyEntities,p=s.dragChildrenKeys,v=s.flattenNodes,m=s.indent,h=e.props,g=h.onDragEnter,y=h.onExpand,b=h.allowDrop,x=h.direction,C=i.props,S=C.pos,N=C.eventKey,k=H(e),T=k.dragNode;if(e.currentMouseOverDroppableNodeKey!==N&&(e.currentMouseOverDroppableNodeKey=N),!T){e.resetDragState();return}var E=kr(a,T,i,m,e.dragStartMousePosition,b,v,f,u,x),$=E.dropPosition,M=E.dropLevelOffset,K=E.dropTargetKey,O=E.dropContainerKey,R=E.dropTargetPos,L=E.dropAllowed,D=E.dragOverNodeKey;if(p.indexOf(K)!==-1||!L){e.resetDragState();return}if(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(I){clearTimeout(e.delayedDragEnterLogic[I])}),T.props.eventKey!==i.props.eventKey&&(a.persist(),e.delayedDragEnterLogic[S]=window.setTimeout(function(){if(e.state.draggingNodeKey!==null){var I=se(u),B=Xe(f,i.props.eventKey);B&&(B.children||[]).length&&(I=st(u,i.props.eventKey)),"expandedKeys"in e.props||e.setExpandedKeys(I),y==null||y(I,{node:Oe(i.props),expanded:!0,nativeEvent:a.nativeEvent})}},800)),T.props.eventKey===K&&M===0){e.resetDragState();return}e.setState({dragOverNodeKey:D,dropPosition:$,dropLevelOffset:M,dropTargetKey:K,dropContainerKey:O,dropTargetPos:R,dropAllowed:L}),g==null||g({event:a,node:Oe(i.props),expandedKeys:u})}),w(H(e),"onNodeDragOver",function(a,i){var s=e.state,u=s.dragChildrenKeys,f=s.flattenNodes,p=s.keyEntities,v=s.expandedKeys,m=s.indent,h=e.props,g=h.onDragOver,y=h.allowDrop,b=h.direction,x=H(e),C=x.dragNode;if(C){var S=kr(a,C,i,m,e.dragStartMousePosition,y,f,p,v,b),N=S.dropPosition,k=S.dropLevelOffset,T=S.dropTargetKey,E=S.dropContainerKey,$=S.dropAllowed,M=S.dropTargetPos,K=S.dragOverNodeKey;u.indexOf(T)!==-1||!$||(C.props.eventKey===T&&k===0?e.state.dropPosition===null&&e.state.dropLevelOffset===null&&e.state.dropTargetKey===null&&e.state.dropContainerKey===null&&e.state.dropTargetPos===null&&e.state.dropAllowed===!1&&e.state.dragOverNodeKey===null||e.resetDragState():N===e.state.dropPosition&&k===e.state.dropLevelOffset&&T===e.state.dropTargetKey&&E===e.state.dropContainerKey&&M===e.state.dropTargetPos&&$===e.state.dropAllowed&&K===e.state.dragOverNodeKey||e.setState({dropPosition:N,dropLevelOffset:k,dropTargetKey:T,dropContainerKey:E,dropTargetPos:M,dropAllowed:$,dragOverNodeKey:K}),g==null||g({event:a,node:Oe(i.props)}))}}),w(H(e),"onNodeDragLeave",function(a,i){e.currentMouseOverDroppableNodeKey===i.props.eventKey&&!a.currentTarget.contains(a.relatedTarget)&&(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var s=e.props.onDragLeave;s==null||s({event:a,node:Oe(i.props)})}),w(H(e),"onWindowDragEnd",function(a){e.onNodeDragEnd(a,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)}),w(H(e),"onNodeDragEnd",function(a,i){var s=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),s==null||s({event:a,node:Oe(i.props)}),e.dragNode=null,window.removeEventListener("dragend",e.onWindowDragEnd)}),w(H(e),"onNodeDrop",function(a,i){var s,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=e.state,p=f.dragChildrenKeys,v=f.dropPosition,m=f.dropTargetKey,h=f.dropTargetPos,g=f.dropAllowed;if(g){var y=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),m!==null){var b=_(_({},_t(m,e.getTreeNodeRequiredProps())),{},{active:((s=e.getActiveItem())===null||s===void 0?void 0:s.key)===m,data:Xe(e.state.keyEntities,m).node}),x=p.indexOf(m)!==-1;mt(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var C=zn(h),S={event:a,node:Oe(b),dragNode:e.dragNode?Oe(e.dragNode.props):null,dragNodesKeys:[e.dragNode.props.eventKey].concat(p),dropToGap:v!==0,dropPosition:v+Number(C[C.length-1])};u||y==null||y(S),e.dragNode=null}}}),w(H(e),"cleanDragState",function(){var a=e.state.draggingNodeKey;a!==null&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null}),w(H(e),"triggerExpandActionExpand",function(a,i){var s=e.state,u=s.expandedKeys,f=s.flattenNodes,p=i.expanded,v=i.key,m=i.isLeaf;if(!(m||a.shiftKey||a.metaKey||a.ctrlKey)){var h=f.filter(function(y){return y.key===v})[0],g=Oe(_(_({},_t(v,e.getTreeNodeRequiredProps())),{},{data:h.data}));e.setExpandedKeys(p?tt(u,v):st(u,v)),e.onNodeExpand(a,g)}}),w(H(e),"onNodeClick",function(a,i){var s=e.props,u=s.onClick,f=s.expandAction;f==="click"&&e.triggerExpandActionExpand(a,i),u==null||u(a,i)}),w(H(e),"onNodeDoubleClick",function(a,i){var s=e.props,u=s.onDoubleClick,f=s.expandAction;f==="doubleClick"&&e.triggerExpandActionExpand(a,i),u==null||u(a,i)}),w(H(e),"onNodeSelect",function(a,i){var s=e.state.selectedKeys,u=e.state,f=u.keyEntities,p=u.fieldNames,v=e.props,m=v.onSelect,h=v.multiple,g=i.selected,y=i[p.key],b=!g;b?h?s=st(s,y):s=[y]:s=tt(s,y);var x=s.map(function(C){var S=Xe(f,C);return S?S.node:null}).filter(function(C){return C});e.setUncontrolledState({selectedKeys:s}),m==null||m(s,{event:"select",selected:b,node:i,selectedNodes:x,nativeEvent:a.nativeEvent})}),w(H(e),"onNodeCheck",function(a,i,s){var u=e.state,f=u.keyEntities,p=u.checkedKeys,v=u.halfCheckedKeys,m=e.props,h=m.checkStrictly,g=m.onCheck,y=i.key,b,x={event:"check",node:i,checked:s,nativeEvent:a.nativeEvent};if(h){var C=s?st(p,y):tt(p,y),S=tt(v,y);b={checked:C,halfChecked:S},x.checkedNodes=C.map(function(M){return Xe(f,M)}).filter(function(M){return M}).map(function(M){return M.node}),e.setUncontrolledState({checkedKeys:C})}else{var N=kt([].concat(se(p),[y]),!0,f),k=N.checkedKeys,T=N.halfCheckedKeys;if(!s){var E=new Set(k);E.delete(y);var $=kt(Array.from(E),{checked:!1,halfCheckedKeys:T},f);k=$.checkedKeys,T=$.halfCheckedKeys}b=k,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=T,k.forEach(function(M){var K=Xe(f,M);if(K){var O=K.node,R=K.pos;x.checkedNodes.push(O),x.checkedNodesPositions.push({node:O,pos:R})}}),e.setUncontrolledState({checkedKeys:k},!1,{halfCheckedKeys:T})}g==null||g(b,x)}),w(H(e),"onNodeLoad",function(a){var i,s=a.key,u=e.state.keyEntities,f=Xe(u,s);if(!(f!=null&&(i=f.children)!==null&&i!==void 0&&i.length)){var p=new Promise(function(v,m){e.setState(function(h){var g=h.loadedKeys,y=g===void 0?[]:g,b=h.loadingKeys,x=b===void 0?[]:b,C=e.props,S=C.loadData,N=C.onLoad;if(!S||y.indexOf(s)!==-1||x.indexOf(s)!==-1)return null;var k=S(a);return k.then(function(){var T=e.state.loadedKeys,E=st(T,s);N==null||N(E,{event:"load",node:a}),e.setUncontrolledState({loadedKeys:E}),e.setState(function($){return{loadingKeys:tt($.loadingKeys,s)}}),v()}).catch(function(T){if(e.setState(function($){return{loadingKeys:tt($.loadingKeys,s)}}),e.loadingRetryTimes[s]=(e.loadingRetryTimes[s]||0)+1,e.loadingRetryTimes[s]>=zi){var E=e.state.loadedKeys;mt(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:st(E,s)}),v()}m(T)}),{loadingKeys:st(x,s)}})});return p.catch(function(){}),p}}),w(H(e),"onNodeMouseEnter",function(a,i){var s=e.props.onMouseEnter;s==null||s({event:a,node:i})}),w(H(e),"onNodeMouseLeave",function(a,i){var s=e.props.onMouseLeave;s==null||s({event:a,node:i})}),w(H(e),"onNodeContextMenu",function(a,i){var s=e.props.onRightClick;s&&(a.preventDefault(),s({event:a,node:i}))}),w(H(e),"onFocus",function(){var a=e.props.onFocus;e.setState({focused:!0});for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];a==null||a.apply(void 0,s)}),w(H(e),"onBlur",function(){var a=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];a==null||a.apply(void 0,s)}),w(H(e),"getTreeNodeRequiredProps",function(){var a=e.state,i=a.expandedKeys,s=a.selectedKeys,u=a.loadedKeys,f=a.loadingKeys,p=a.checkedKeys,v=a.halfCheckedKeys,m=a.dragOverNodeKey,h=a.dropPosition,g=a.keyEntities;return{expandedKeys:i||[],selectedKeys:s||[],loadedKeys:u||[],loadingKeys:f||[],checkedKeys:p||[],halfCheckedKeys:v||[],dragOverNodeKey:m,dropPosition:h,keyEntities:g}}),w(H(e),"setExpandedKeys",function(a){var i=e.state,s=i.treeData,u=i.fieldNames,f=pn(s,a,u);e.setUncontrolledState({expandedKeys:a,flattenNodes:f},!0)}),w(H(e),"onNodeExpand",function(a,i){var s=e.state.expandedKeys,u=e.state,f=u.listChanging,p=u.fieldNames,v=e.props,m=v.onExpand,h=v.loadData,g=i.expanded,y=i[p.key];if(!f){var b=s.indexOf(y),x=!g;if(mt(g&&b!==-1||!g&&b===-1,"Expand state not sync with index check"),x?s=st(s,y):s=tt(s,y),e.setExpandedKeys(s),m==null||m(s,{node:i,expanded:x,nativeEvent:a.nativeEvent}),x&&h){var C=e.onNodeLoad(i);C&&C.then(function(){var S=pn(e.state.treeData,s,p);e.setUncontrolledState({flattenNodes:S})}).catch(function(){var S=e.state.expandedKeys,N=tt(S,y);e.setExpandedKeys(N)})}}}),w(H(e),"onListChangeStart",function(){e.setUncontrolledState({listChanging:!0})}),w(H(e),"onListChangeEnd",function(){setTimeout(function(){e.setUncontrolledState({listChanging:!1})})}),w(H(e),"onActiveChange",function(a){var i=e.state.activeKey,s=e.props,u=s.onActiveChange,f=s.itemScrollOffset,p=f===void 0?0:f;i!==a&&(e.setState({activeKey:a}),a!==null&&e.scrollTo({key:a,offset:p}),u==null||u(a))}),w(H(e),"getActiveItem",function(){var a=e.state,i=a.activeKey,s=a.flattenNodes;return i===null?null:s.find(function(u){var f=u.key;return f===i})||null}),w(H(e),"offsetActiveKey",function(a){var i=e.state,s=i.flattenNodes,u=i.activeKey,f=s.findIndex(function(m){var h=m.key;return h===u});f===-1&&a<0&&(f=s.length),f=(f+a+s.length)%s.length;var p=s[f];if(p){var v=p.key;e.onActiveChange(v)}else e.onActiveChange(null)}),w(H(e),"onKeyDown",function(a){var i=e.state,s=i.activeKey,u=i.expandedKeys,f=i.checkedKeys,p=i.fieldNames,v=e.props,m=v.onKeyDown,h=v.checkable,g=v.selectable;switch(a.which){case pt.UP:{e.offsetActiveKey(-1),a.preventDefault();break}case pt.DOWN:{e.offsetActiveKey(1),a.preventDefault();break}}var y=e.getActiveItem();if(y&&y.data){var b=e.getTreeNodeRequiredProps(),x=y.data.isLeaf===!1||!!(y.data[p.children]||[]).length,C=Oe(_(_({},_t(s,b)),{},{data:y.data,active:!0}));switch(a.which){case pt.LEFT:{x&&u.includes(s)?e.onNodeExpand({},C):y.parent&&e.onActiveChange(y.parent.key),a.preventDefault();break}case pt.RIGHT:{x&&!u.includes(s)?e.onNodeExpand({},C):y.children&&y.children.length&&e.onActiveChange(y.children[0].key),a.preventDefault();break}case pt.ENTER:case pt.SPACE:{h&&!C.disabled&&C.checkable!==!1&&!C.disableCheckbox?e.onNodeCheck({},C,!f.includes(s)):!h&&g&&!C.disabled&&C.selectable!==!1&&e.onNodeSelect({},C);break}}}m==null||m(a)}),w(H(e),"setUncontrolledState",function(a){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!e.destroyed){var u=!1,f=!0,p={};Object.keys(a).forEach(function(v){if(v in e.props){f=!1;return}u=!0,p[v]=a[v]}),u&&(!i||f)&&e.setState(_(_({},p),s))}}),w(H(e),"scrollTo",function(a){e.listRef.current.scrollTo(a)}),e}return Yr(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,l=o.activeKey,d=o.itemScrollOffset,a=d===void 0?0:d;l!==void 0&&l!==this.state.activeKey&&(this.setState({activeKey:l}),l!==null&&this.scrollTo({key:l,offset:a}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,l=o.focused,d=o.flattenNodes,a=o.keyEntities,i=o.draggingNodeKey,s=o.activeKey,u=o.dropLevelOffset,f=o.dropContainerKey,p=o.dropTargetKey,v=o.dropPosition,m=o.dragOverNodeKey,h=o.indent,g=this.props,y=g.prefixCls,b=g.className,x=g.style,C=g.showLine,S=g.focusable,N=g.tabIndex,k=N===void 0?0:N,T=g.selectable,E=g.showIcon,$=g.icon,M=g.switcherIcon,K=g.draggable,O=g.checkable,R=g.checkStrictly,L=g.disabled,D=g.motion,I=g.loadData,B=g.filterTreeNode,P=g.height,V=g.itemHeight,W=g.virtual,re=g.titleRender,J=g.dropIndicatorRender,$e=g.onContextMenu,ge=g.onScroll,Ne=g.direction,ee=g.rootClassName,pe=g.rootStyle,te=Jt(this.props,{aria:!0,data:!0}),ce;return K&&(nt(K)==="object"?ce=K:typeof K=="function"?ce={nodeDraggable:K}:ce={}),c.createElement(An.Provider,{value:{prefixCls:y,selectable:T,showIcon:E,icon:$,switcherIcon:M,draggable:ce,draggingNodeKey:i,checkable:O,checkStrictly:R,disabled:L,keyEntities:a,dropLevelOffset:u,dropContainerKey:f,dropTargetKey:p,dropPosition:v,dragOverNodeKey:m,indent:h,direction:Ne,dropIndicatorRender:J,loadData:I,filterTreeNode:B,titleRender:re,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop}},c.createElement("div",{role:"tree",className:Z(y,b,ee,w(w(w({},"".concat(y,"-show-line"),C),"".concat(y,"-focused"),l),"".concat(y,"-active-focused"),s!==null)),style:pe},c.createElement(Oo,de({ref:this.listRef,prefixCls:y,style:x,data:d,disabled:L,selectable:T,checkable:!!O,motion:D,dragging:i!==null,height:P,itemHeight:V,virtual:W,focusable:S,focused:l,tabIndex:k,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:$e,onScroll:ge},this.getTreeNodeRequiredProps(),te))))}}],[{key:"getDerivedStateFromProps",value:function(o,l){var d=l.prevProps,a={prevProps:o};function i(N){return!d&&N in o||d&&d[N]!==o[N]}var s,u=l.fieldNames;if(i("fieldNames")&&(u=Kt(o.fieldNames),a.fieldNames=u),i("treeData")?s=o.treeData:i("children")&&(mt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),s=so(o.children)),s){a.treeData=s;var f=Mn(s,{fieldNames:u});a.keyEntities=_(w({},bt,To),f.keyEntities)}var p=a.keyEntities||l.keyEntities;if(i("expandedKeys")||d&&i("autoExpandParent"))a.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?Sn(o.expandedKeys,p):o.expandedKeys;else if(!d&&o.defaultExpandAll){var v=_({},p);delete v[bt],a.expandedKeys=Object.keys(v).map(function(N){return v[N].key})}else!d&&o.defaultExpandedKeys&&(a.expandedKeys=o.autoExpandParent||o.defaultExpandParent?Sn(o.defaultExpandedKeys,p):o.defaultExpandedKeys);if(a.expandedKeys||delete a.expandedKeys,s||a.expandedKeys){var m=pn(s||l.treeData,a.expandedKeys||l.expandedKeys,u);a.flattenNodes=m}if(o.selectable&&(i("selectedKeys")?a.selectedKeys=$r(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(a.selectedKeys=$r(o.defaultSelectedKeys,o))),o.checkable){var h;if(i("checkedKeys")?h=vn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?h=vn(o.defaultCheckedKeys)||{}:s&&(h=vn(o.checkedKeys)||{checkedKeys:l.checkedKeys,halfCheckedKeys:l.halfCheckedKeys}),h){var g=h,y=g.checkedKeys,b=y===void 0?[]:y,x=g.halfCheckedKeys,C=x===void 0?[]:x;if(!o.checkStrictly){var S=kt(b,!0,p);b=S.checkedKeys,C=S.halfCheckedKeys}a.checkedKeys=b,a.halfCheckedKeys=C}}return i("loadedKeys")&&(a.loadedKeys=o.loadedKeys),a}}]),r}(c.Component);w(jn,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Di,allowDrop:function(){return!0},expandAction:!1});w(jn,"TreeNode",Rt);const ji=new ea("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Wi=(n,t)=>({[`.${n}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Vi=(n,t)=>({[`.${n}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${j(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),qi=(n,t)=>{const{treeCls:r,treeNodeCls:e,treeNodePadding:o,titleHeight:l,nodeSelectedBg:d,nodeHoverBg:a}=t,i=t.paddingXS;return{[r]:Object.assign(Object.assign({},Pn(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,[`&${r}-rtl`]:{[`${r}-switcher`]:{"&_close":{[`${r}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},ta(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${e}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:o,insetInlineStart:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:ji,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[e]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${j(o)} 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${e}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:l,lineHeight:j(l),textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${t.motionDurationSlow}`,[`${e}:hover &`]:{opacity:.45}},[`&${e}-disabled`]:{[`${r}-draggable-icon`]:{visibility:"hidden"}}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:l}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher`]:Object.assign(Object.assign({},Wi(n,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,margin:0,lineHeight:j(l),textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:l,height:l,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},"&_close":{[`${r}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-checkbox`]:{top:"initial",marginInlineEnd:i,alignSelf:"flex-start",marginTop:t.marginXXS},[`${r}-node-content-wrapper, ${r}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:l,margin:0,padding:`0 ${j(t.calc(t.paddingXS).div(2).equal())}`,color:"inherit",lineHeight:j(l),background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:a},[`&${r}-node-selected`]:{backgroundColor:d},[`${r}-iconEle`]:{display:"inline-block",width:l,height:l,lineHeight:j(l),textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${r}-node-content-wrapper`]:Object.assign({lineHeight:j(l),userSelect:"none"},Vi(n,t)),[`${e}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${t.colorPrimary}`}},"&-show-line":{[`${r}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${e}-leaf-last`]:{[`${r}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${j(t.calc(l).div(2).equal())} !important`}}}}})}},Xi=n=>{const{treeCls:t,treeNodeCls:r,treeNodePadding:e,directoryNodeSelectedBg:o,directoryNodeSelectedColor:l}=n;return{[`${t}${t}-directory`]:{[r]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:e,insetInlineStart:0,transition:`background-color ${n.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:n.controlItemBgHover}},"> *":{zIndex:1},[`${t}-switcher`]:{transition:`color ${n.motionDurationMid}`},[`${t}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${t}-node-selected`]:{color:l,background:"transparent"}},"&-selected":{"\n            &:hover::before,\n            &::before\n          ":{background:o},[`${t}-switcher`]:{color:l},[`${t}-node-content-wrapper`]:{color:l,background:"transparent"}}}}}},Ui=(n,t)=>{const r=`.${n}`,e=`${r}-treenode`,o=t.calc(t.paddingXS).div(2).equal(),l=Jr(t,{treeCls:r,treeNodeCls:e,treeNodePadding:o});return[qi(n,l),Xi(l)]},Gi=n=>{const{controlHeightSM:t}=n;return{titleHeight:t,nodeHoverBg:n.controlItemBgHover,nodeSelectedBg:n.controlItemBgActive}},Yi=n=>{const{colorTextLightSolid:t,colorPrimary:r}=n;return Object.assign(Object.assign({},Gi(n)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},Zi=Zr("Tree",(n,t)=>{let{prefixCls:r}=t;return[{[n.componentCls]:ua(`${r}-checkbox`,n)},Ui(r,n),fa(n)]},Yi),Pr=4;function Ji(n){const{dropPosition:t,dropLevelOffset:r,prefixCls:e,indent:o,direction:l="ltr"}=n,d=l==="ltr"?"left":"right",a=l==="ltr"?"right":"left",i={[d]:-r*o+Pr,[a]:0};switch(t){case-1:i.top=-3;break;case 1:i.bottom=-3;break;default:i.bottom=-3,i[d]=o+Pr;break}return ut.createElement("div",{style:i,className:`${e}-drop-indicator`})}const Qi=n=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:e,showLine:o,switcherLoadingIcon:l}=n,{isLeaf:d,expanded:a,loading:i}=e;if(i)return c.isValidElement(l)?l:c.createElement(na,{className:`${t}-switcher-loading-icon`});let s;if(o&&typeof o=="object"&&(s=o.showLeafIcon),d){if(!o)return null;if(typeof s!="boolean"&&s){const p=typeof s=="function"?s(e):s,v=`${t}-switcher-line-custom-icon`;return c.isValidElement(p)?dr(p,{className:Z(p.props.className||"",v)}):p}return s?c.createElement(uo,{className:`${t}-switcher-line-icon`}):c.createElement("span",{className:`${t}-switcher-leaf-line`})}const u=`${t}-switcher-icon`,f=typeof r=="function"?r(e):r;return c.isValidElement(f)?dr(f,{className:Z(f.props.className||"",u)}):f!==void 0?f:o?a?c.createElement(ll,{className:`${t}-switcher-line-icon`}):c.createElement(cl,{className:`${t}-switcher-line-icon`}):c.createElement(Ba,{className:u})},Po=ut.forwardRef((n,t)=>{var r;const{getPrefixCls:e,direction:o,virtual:l,tree:d}=ut.useContext(tn),{prefixCls:a,className:i,showIcon:s=!1,showLine:u,switcherIcon:f,switcherLoadingIcon:p,blockNode:v=!1,children:m,checkable:h=!1,selectable:g=!0,draggable:y,motion:b,style:x}=n,C=e("tree",a),S=e(),N=b??Object.assign(Object.assign({},pa(S)),{motionAppear:!1}),k=Object.assign(Object.assign({},n),{checkable:h,selectable:g,showIcon:s,motion:N,blockNode:v,showLine:!!u,dropIndicatorRender:Ji}),[T,E,$]=Zi(C),[,M]=Qr(),K=M.paddingXS/2+(((r=M.Tree)===null||r===void 0?void 0:r.titleHeight)||M.controlHeightSM),O=ut.useMemo(()=>{if(!y)return!1;let L={};switch(typeof y){case"function":L.nodeDraggable=y;break;case"object":L=Object.assign({},y);break}return L.icon!==!1&&(L.icon=L.icon||ut.createElement(rl,null)),L},[y]),R=L=>ut.createElement(Qi,{prefixCls:C,switcherIcon:f,switcherLoadingIcon:p,treeNodeProps:L,showLine:u});return T(ut.createElement(jn,Object.assign({itemHeight:K,ref:t,virtual:l},k,{style:Object.assign(Object.assign({},d==null?void 0:d.style),x),prefixCls:C,className:Z({[`${C}-icon-hide`]:!s,[`${C}-block-node`]:v,[`${C}-unselectable`]:!g,[`${C}-rtl`]:o==="rtl"},d==null?void 0:d.className,i,E,$),direction:o,checkable:h&&ut.createElement("span",{className:`${C}-checkbox-inner`}),selectable:g,switcherIcon:R,draggable:O}),m))}),Dr=0,mn=1,Mr=2;function Wn(n,t,r){const{key:e,children:o}=r;function l(d){const a=d[e],i=d[o];t(a,d)!==!1&&Wn(i||[],t,r)}n.forEach(l)}function es(n){let{treeData:t,expandedKeys:r,startKey:e,endKey:o,fieldNames:l}=n;const d=[];let a=Dr;if(e&&e===o)return[e];if(!e||!o)return[];function i(s){return s===e||s===o}return Wn(t,s=>{if(a===Mr)return!1;if(i(s)){if(d.push(s),a===Dr)a=mn;else if(a===mn)return a=Mr,!1}else a===mn&&d.push(s);return r.includes(s)},Kt(l)),d}function gn(n,t,r){const e=se(t),o=[];return Wn(n,(l,d)=>{const a=e.indexOf(l);return a!==-1&&(o.push(d),e.splice(a,1)),!!e.length},Kt(r)),o}var Lr=function(n,t){var r={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&t.indexOf(e)<0&&(r[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,e=Object.getOwnPropertySymbols(n);o<e.length;o++)t.indexOf(e[o])<0&&Object.prototype.propertyIsEnumerable.call(n,e[o])&&(r[e[o]]=n[e[o]]);return r};function ts(n){const{isLeaf:t,expanded:r}=n;return t?c.createElement(uo,null):r?c.createElement(Za,null):c.createElement(el,null)}function Br(n){let{treeData:t,children:r}=n;return t||so(r)}const ns=(n,t)=>{var{defaultExpandAll:r,defaultExpandParent:e,defaultExpandedKeys:o}=n,l=Lr(n,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=c.useRef(),a=c.useRef(),i=()=>{const{keyEntities:T}=Mn(Br(l));let E;return r?E=Object.keys(T):e?E=Sn(l.expandedKeys||o||[],T):E=l.expandedKeys||o||[],E},[s,u]=c.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[f,p]=c.useState(()=>i());c.useEffect(()=>{"selectedKeys"in l&&u(l.selectedKeys)},[l.selectedKeys]),c.useEffect(()=>{"expandedKeys"in l&&p(l.expandedKeys)},[l.expandedKeys]);const v=(T,E)=>{var $;return"expandedKeys"in l||p(T),($=l.onExpand)===null||$===void 0?void 0:$.call(l,T,E)},m=(T,E)=>{var $;const{multiple:M,fieldNames:K}=l,{node:O,nativeEvent:R}=E,{key:L=""}=O,D=Br(l),I=Object.assign(Object.assign({},E),{selected:!0}),B=(R==null?void 0:R.ctrlKey)||(R==null?void 0:R.metaKey),P=R==null?void 0:R.shiftKey;let V;M&&B?(V=T,d.current=L,a.current=V,I.selectedNodes=gn(D,V,K)):M&&P?(V=Array.from(new Set([].concat(se(a.current||[]),se(es({treeData:D,expandedKeys:f,startKey:L,endKey:d.current,fieldNames:K}))))),I.selectedNodes=gn(D,V,K)):(V=[L],d.current=L,a.current=V,I.selectedNodes=gn(D,V,K)),($=l.onSelect)===null||$===void 0||$.call(l,V,I),"selectedKeys"in l||u(V)},{getPrefixCls:h,direction:g}=c.useContext(tn),{prefixCls:y,className:b,showIcon:x=!0,expandAction:C="click"}=l,S=Lr(l,["prefixCls","className","showIcon","expandAction"]),N=h("tree",y),k=Z(`${N}-directory`,{[`${N}-directory-rtl`]:g==="rtl"},b);return c.createElement(Po,Object.assign({icon:ts,ref:t,blockNode:!0},S,{showIcon:x,expandAction:C,prefixCls:N,className:k,expandedKeys:f,selectedKeys:s,onSelect:m,onExpand:v}))},rs=c.forwardRef(ns),Vn=Po;Vn.DirectoryTree=rs;Vn.TreeNode=Rt;const Hr=n=>{const{value:t,filterSearch:r,tablePrefixCls:e,locale:o,onChange:l}=n;return r?c.createElement("div",{className:`${e}-filter-dropdown-search`},c.createElement(va,{prefix:c.createElement(ma,null),placeholder:o.filterSearchPlaceholder,onChange:l,value:t,htmlSize:1,className:`${e}-filter-dropdown-search-input`})):null},os=n=>{const{keyCode:t}=n;t===pt.ENTER&&n.stopPropagation()},as=c.forwardRef((n,t)=>c.createElement("div",{className:n.className,onClick:r=>r.stopPropagation(),onKeyDown:os,ref:t},n.children));function $t(n){let t=[];return(n||[]).forEach(r=>{let{value:e,children:o}=r;t.push(e),o&&(t=[].concat(se(t),se($t(o))))}),t}function ls(n){return n.some(t=>{let{children:r}=t;return r})}function Do(n,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(n.trim().toLowerCase()):!1}function Mo(n){let{filters:t,prefixCls:r,filteredKeys:e,filterMultiple:o,searchValue:l,filterSearch:d}=n;return t.map((a,i)=>{const s=String(a.value);if(a.children)return{key:s||i,label:a.text,popupClassName:`${r}-dropdown-submenu`,children:Mo({filters:a.children,prefixCls:r,filteredKeys:e,filterMultiple:o,searchValue:l,filterSearch:d})};const u=o?Qt:oo,f={key:a.value!==void 0?s:i,label:c.createElement(c.Fragment,null,c.createElement(u,{checked:e.includes(s)}),c.createElement("span",null,a.text))};return l.trim()?typeof d=="function"?d(l,a)?f:null:Do(l,a.text)?f:null:f})}function hn(n){return n||[]}const is=n=>{var t,r;const{tablePrefixCls:e,prefixCls:o,column:l,dropdownPrefixCls:d,columnKey:a,filterOnClose:i,filterMultiple:s,filterMode:u="menu",filterSearch:f=!1,filterState:p,triggerFilter:v,locale:m,children:h,getPopupContainer:g,rootClassName:y}=n,{filterDropdownOpen:b,onFilterDropdownOpenChange:x,filterResetToDefaultFilteredValue:C,defaultFilteredValue:S,filterDropdownVisible:N,onFilterDropdownVisibleChange:k}=l,[T,E]=c.useState(!1),$=!!(p&&(!((t=p.filteredKeys)===null||t===void 0)&&t.length||p.forceFiltered)),M=A=>{E(A),x==null||x(A),k==null||k(A)},K=(r=b??N)!==null&&r!==void 0?r:T,O=p==null?void 0:p.filteredKeys,[R,L]=Pi(hn(O)),D=A=>{let{selectedKeys:ie}=A;L(ie)},I=(A,ie)=>{let{node:G,checked:ue}=ie;D(s?{selectedKeys:A}:{selectedKeys:ue&&G.key?[G.key]:[]})};c.useEffect(()=>{T&&D({selectedKeys:hn(O)})},[O]);const[B,P]=c.useState([]),V=A=>{P(A)},[W,re]=c.useState(""),J=A=>{const{value:ie}=A.target;re(ie)};c.useEffect(()=>{T||re("")},[T]);const $e=A=>{const ie=A!=null&&A.length?A:null;if(ie===null&&(!p||!p.filteredKeys)||At(ie,p==null?void 0:p.filteredKeys,!0))return null;v({column:l,key:a,filteredKeys:ie})},ge=()=>{M(!1),$e(R())},Ne=function(){let{confirm:A,closeDropdown:ie}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{confirm:!1,closeDropdown:!1};A&&$e([]),ie&&M(!1),re(""),L(C?(S||[]).map(G=>String(G)):[])},ee=function(){let{closeDropdown:A}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{closeDropdown:!0};A&&M(!1),$e(R())},pe=(A,ie)=>{ie.source==="trigger"&&(A&&O!==void 0&&L(hn(O)),M(A),!A&&!l.filterDropdown&&i&&ge())},te=Z({[`${d}-menu-without-submenu`]:!ls(l.filters||[])}),ce=A=>{if(A.target.checked){const ie=$t(l==null?void 0:l.filters).map(G=>String(G));L(ie)}else L([])},Q=A=>{let{filters:ie}=A;return(ie||[]).map((G,ue)=>{const we=String(G.value),X={title:G.text,key:G.value!==void 0?we:String(ue)};return G.children&&(X.children=Q({filters:G.children})),X})},U=A=>{var ie;return Object.assign(Object.assign({},A),{text:A.title,value:A.key,children:((ie=A.children)===null||ie===void 0?void 0:ie.map(G=>U(G)))||[]})};let z;const{direction:F,renderEmpty:q}=c.useContext(tn);if(typeof l.filterDropdown=="function")z=l.filterDropdown({prefixCls:`${d}-custom`,setSelectedKeys:A=>D({selectedKeys:A}),selectedKeys:R(),confirm:ee,clearFilters:Ne,filters:l.filters,visible:K,close:()=>{M(!1)}});else if(l.filterDropdown)z=l.filterDropdown;else{const A=R()||[],ie=()=>{var ue;const we=(ue=q==null?void 0:q("Table.filter"))!==null&&ue!==void 0?ue:c.createElement(vr,{image:vr.PRESENTED_IMAGE_SIMPLE,description:m.filterEmptyText,imageStyle:{height:24},style:{margin:0,padding:"16px 0"}});if((l.filters||[]).length===0)return we;if(u==="tree")return c.createElement(c.Fragment,null,c.createElement(Hr,{filterSearch:f,value:W,onChange:J,tablePrefixCls:e,locale:m}),c.createElement("div",{className:`${e}-filter-dropdown-tree`},s?c.createElement(Qt,{checked:A.length===$t(l.filters).length,indeterminate:A.length>0&&A.length<$t(l.filters).length,className:`${e}-filter-dropdown-checkall`,onChange:ce},m.filterCheckall):null,c.createElement(Vn,{checkable:!0,selectable:!1,blockNode:!0,multiple:s,checkStrictly:!s,className:`${d}-menu`,onCheck:I,checkedKeys:A,selectedKeys:A,showIcon:!1,treeData:Q({filters:l.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:W.trim()?xe=>typeof f=="function"?f(W,U(xe)):Do(W,xe.title):void 0})));const X=Mo({filters:l.filters||[],filterSearch:f,prefixCls:o,filteredKeys:R(),filterMultiple:s,searchValue:W}),ve=X.every(xe=>xe===null);return c.createElement(c.Fragment,null,c.createElement(Hr,{filterSearch:f,value:W,onChange:J,tablePrefixCls:e,locale:m}),ve?we:c.createElement(xa,{selectable:!0,multiple:s,prefixCls:`${d}-menu`,className:te,onSelect:D,onDeselect:D,selectedKeys:A,getPopupContainer:g,openKeys:B,onOpenChange:V,items:X}))},G=()=>C?At((S||[]).map(ue=>String(ue)),A,!0):A.length===0;z=c.createElement(c.Fragment,null,ie(),c.createElement("div",{className:`${o}-dropdown-btns`},c.createElement(pr,{type:"link",size:"small",disabled:G(),onClick:()=>Ne()},m.filterReset),c.createElement(pr,{type:"primary",size:"small",onClick:ge},m.filterConfirm)))}l.filterDropdown&&(z=c.createElement(ba,{selectable:void 0},z));const ae=()=>c.createElement(as,{className:`${o}-dropdown`},z);let ne;return typeof l.filterIcon=="function"?ne=l.filterIcon($):l.filterIcon?ne=l.filterIcon:ne=c.createElement(Ua,null),c.createElement("div",{className:`${o}-column`},c.createElement("span",{className:`${e}-column-title`},h),c.createElement(ro,{dropdownRender:ae,trigger:["click"],open:K,onOpenChange:pe,getPopupContainer:g,placement:F==="rtl"?"bottomLeft":"bottomRight",rootClassName:y},c.createElement("span",{role:"button",tabIndex:-1,className:Z(`${o}-trigger`,{active:$}),onClick:A=>{A.stopPropagation()}},ne)))},Nn=(n,t,r)=>{let e=[];return(n||[]).forEach((o,l)=>{var d;const a=Ot(l,r);if(o.filters||"filterDropdown"in o||"onFilter"in o)if("filteredValue"in o){let i=o.filteredValue;"filterDropdown"in o||(i=(d=i==null?void 0:i.map(String))!==null&&d!==void 0?d:i),e.push({column:o,key:ht(o,a),filteredKeys:i,forceFiltered:o.filtered})}else e.push({column:o,key:ht(o,a),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(e=[].concat(se(e),se(Nn(o.children,t,a))))}),e};function Lo(n,t,r,e,o,l,d,a,i){return r.map((s,u)=>{const f=Ot(u,a),{filterOnClose:p=!0,filterMultiple:v=!0,filterMode:m,filterSearch:h}=s;let g=s;if(g.filters||g.filterDropdown){const y=ht(g,f),b=e.find(x=>{let{key:C}=x;return y===C});g=Object.assign(Object.assign({},g),{title:x=>c.createElement(is,{tablePrefixCls:n,prefixCls:`${n}-filter`,dropdownPrefixCls:t,column:g,columnKey:y,filterState:b,filterOnClose:p,filterMultiple:v,filterMode:m,filterSearch:h,triggerFilter:l,locale:o,getPopupContainer:d,rootClassName:i},on(s.title,x))})}return"children"in g&&(g=Object.assign(Object.assign({},g),{children:Lo(n,t,g.children,e,o,l,d,f,i)})),g})}const _r=n=>{const t={};return n.forEach(r=>{let{key:e,filteredKeys:o,column:l}=r;const d=e,{filters:a,filterDropdown:i}=l;if(i)t[d]=o||null;else if(Array.isArray(o)){const s=$t(a);t[d]=s.filter(u=>o.includes(String(u)))}else t[d]=null}),t},Kn=(n,t,r)=>t.reduce((o,l)=>{const{column:{onFilter:d,filters:a},filteredKeys:i}=l;return d&&i&&i.length?o.map(s=>Object.assign({},s)).filter(s=>i.some(u=>{const f=$t(a),p=f.findIndex(m=>String(m)===String(u)),v=p!==-1?f[p]:u;return s[r]&&(s[r]=Kn(s[r],t,r)),d(v,s)})):o},n),Bo=n=>n.flatMap(t=>"children"in t?[t].concat(se(Bo(t.children||[]))):[t]),ss=n=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:e,onFilterChange:o,getPopupContainer:l,locale:d,rootClassName:a}=n;On();const i=c.useMemo(()=>Bo(e||[]),[e]),[s,u]=c.useState(()=>Nn(i,!0)),f=c.useMemo(()=>{const h=Nn(i,!1);if(h.length===0)return h;let g=!0;if(h.forEach(y=>{let{filteredKeys:b}=y;b!==void 0&&(g=!1)}),g){const y=(i||[]).map((b,x)=>ht(b,Ot(x)));return s.filter(b=>{let{key:x}=b;return y.includes(x)}).map(b=>{const x=i[y.findIndex(C=>C===b.key)];return Object.assign(Object.assign({},b),{column:Object.assign(Object.assign({},b.column),x),forceFiltered:x.filtered})})}return h},[i,s]),p=c.useMemo(()=>_r(f),[f]),v=h=>{const g=f.filter(y=>{let{key:b}=y;return b!==h.key});g.push(h),u(g),o(_r(g),g)};return[h=>Lo(t,r,h,f,d,v,l,void 0,a),f,p]},cs=(n,t,r)=>{const e=c.useRef({});function o(l){var d;if(!e.current||e.current.data!==n||e.current.childrenColumnName!==t||e.current.getRowKey!==r){let i=function(s){s.forEach((u,f)=>{const p=r(u,f);a.set(p,u),u&&typeof u=="object"&&t in u&&i(u[t]||[])})};const a=new Map;i(n),e.current={data:n,childrenColumnName:t,kvMap:a,getRowKey:r}}return(d=e.current.kvMap)===null||d===void 0?void 0:d.get(l)}return[o]};var ds=function(n,t){var r={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&t.indexOf(e)<0&&(r[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,e=Object.getOwnPropertySymbols(n);o<e.length;o++)t.indexOf(e[o])<0&&Object.prototype.propertyIsEnumerable.call(n,e[o])&&(r[e[o]]=n[e[o]]);return r};const Ho=10;function us(n,t){const r={current:n.current,pageSize:n.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const l=n[o];typeof l!="function"&&(r[o]=l)}),r}function fs(n,t,r){const e=r&&typeof r=="object"?r:{},{total:o=0}=e,l=ds(e,["total"]),[d,a]=c.useState(()=>({current:"defaultCurrent"in l?l.defaultCurrent:1,pageSize:"defaultPageSize"in l?l.defaultPageSize:Ho})),i=Da(d,l,{total:o>0?o:n}),s=Math.ceil((o||n)/i.pageSize);i.current>s&&(i.current=s||1);const u=(p,v)=>{a({current:p??1,pageSize:v||i.pageSize})},f=(p,v)=>{var m;r&&((m=r.onChange)===null||m===void 0||m.call(r,p,v)),u(p,v),t(p,v||(i==null?void 0:i.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},i),{onChange:f}),u]}const Yt="ascend",yn="descend",en=n=>typeof n.sorter=="object"&&typeof n.sorter.multiple=="number"?n.sorter.multiple:!1,Fr=n=>typeof n=="function"?n:n&&typeof n=="object"&&n.compare?n.compare:!1,ps=(n,t)=>t?n[n.indexOf(t)+1]:n[0],Rn=(n,t,r)=>{let e=[];const o=(l,d)=>{e.push({column:l,key:ht(l,d),multiplePriority:en(l),sortOrder:l.sortOrder})};return(n||[]).forEach((l,d)=>{const a=Ot(d,r);l.children?("sortOrder"in l&&o(l,a),e=[].concat(se(e),se(Rn(l.children,t,a)))):l.sorter&&("sortOrder"in l?o(l,a):t&&l.defaultSortOrder&&e.push({column:l,key:ht(l,a),multiplePriority:en(l),sortOrder:l.defaultSortOrder}))}),e},_o=(n,t,r,e,o,l,d,a)=>(t||[]).map((s,u)=>{const f=Ot(u,a);let p=s;if(p.sorter){const v=p.sortDirections||o,m=p.showSorterTooltip===void 0?d:p.showSorterTooltip,h=ht(p,f),g=r.find(E=>{let{key:$}=E;return $===h}),y=g?g.sortOrder:null,b=ps(v,y);let x;if(s.sortIcon)x=s.sortIcon({sortOrder:y});else{const E=v.includes(Yt)&&c.createElement(ja,{className:Z(`${n}-column-sorter-up`,{active:y===Yt})}),$=v.includes(yn)&&c.createElement(Fa,{className:Z(`${n}-column-sorter-down`,{active:y===yn})});x=c.createElement("span",{className:Z(`${n}-column-sorter`,{[`${n}-column-sorter-full`]:!!(E&&$)})},c.createElement("span",{className:`${n}-column-sorter-inner`,"aria-hidden":"true"},E,$))}const{cancelSort:C,triggerAsc:S,triggerDesc:N}=l||{};let k=C;b===yn?k=N:b===Yt&&(k=S);const T=typeof m=="object"?Object.assign({title:k},m):{title:k};p=Object.assign(Object.assign({},p),{className:Z(p.className,{[`${n}-column-sort`]:y}),title:E=>{const $=`${n}-column-sorters`,M=c.createElement("span",{className:`${n}-column-title`},on(s.title,E)),K=c.createElement("div",{className:$},M,x);return m?typeof m!="boolean"&&(m==null?void 0:m.target)==="sorter-icon"?c.createElement("div",{className:`${$} ${n}-column-sorters-tooltip-target-sorter`},M,c.createElement(mr,Object.assign({},T),x)):c.createElement(mr,Object.assign({},T),K):K},onHeaderCell:E=>{var $;const M=(($=s.onHeaderCell)===null||$===void 0?void 0:$.call(s,E))||{},K=M.onClick,O=M.onKeyDown;M.onClick=D=>{e({column:s,key:h,sortOrder:b,multiplePriority:en(s)}),K==null||K(D)},M.onKeyDown=D=>{D.keyCode===pt.ENTER&&(e({column:s,key:h,sortOrder:b,multiplePriority:en(s)}),O==null||O(D))};const R=Oi(s.title,{}),L=R==null?void 0:R.toString();return y?M["aria-sort"]=y==="ascend"?"ascending":"descending":M["aria-label"]=L||"",M.className=Z(M.className,`${n}-column-has-sorters`),M.tabIndex=0,s.ellipsis&&(M.title=(R??"").toString()),M}})}return"children"in p&&(p=Object.assign(Object.assign({},p),{children:_o(n,p.children,r,e,o,l,d,f)})),p}),Ar=n=>{const{column:t,sortOrder:r}=n;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},zr=n=>{const t=n.filter(r=>{let{sortOrder:e}=r;return e}).map(Ar);if(t.length===0&&n.length){const r=n.length-1;return Object.assign(Object.assign({},Ar(n[r])),{column:void 0})}return t.length<=1?t[0]||{}:t},In=(n,t,r)=>{const e=t.slice().sort((d,a)=>a.multiplePriority-d.multiplePriority),o=n.slice(),l=e.filter(d=>{let{column:{sorter:a},sortOrder:i}=d;return Fr(a)&&i});return l.length?o.sort((d,a)=>{for(let i=0;i<l.length;i+=1){const s=l[i],{column:{sorter:u},sortOrder:f}=s,p=Fr(u);if(p&&f){const v=p(d,a,f);if(v!==0)return f===Yt?v:-v}}return 0}).map(d=>{const a=d[r];return a?Object.assign(Object.assign({},d),{[r]:In(a,t,r)}):d}):o},vs=n=>{const{prefixCls:t,mergedColumns:r,sortDirections:e,tableLocale:o,showSorterTooltip:l,onSorterChange:d}=n,[a,i]=c.useState(Rn(r,!0)),s=(h,g)=>{const y=[];return h.forEach((b,x)=>{const C=Ot(x,g);if(y.push(ht(b,C)),Array.isArray(b.children)){const S=s(b.children,C);y.push.apply(y,se(S))}}),y},u=c.useMemo(()=>{let h=!0;const g=Rn(r,!1);if(!g.length){const C=s(r);return a.filter(S=>{let{key:N}=S;return C.includes(N)})}const y=[];function b(C){h?y.push(C):y.push(Object.assign(Object.assign({},C),{sortOrder:null}))}let x=null;return g.forEach(C=>{x===null?(b(C),C.sortOrder&&(C.multiplePriority===!1?h=!1:x=!0)):(x&&C.multiplePriority!==!1||(h=!1),b(C))}),y},[r,a]),f=c.useMemo(()=>{var h,g;const y=u.map(b=>{let{column:x,sortOrder:C}=b;return{column:x,order:C}});return{sortColumns:y,sortColumn:(h=y[0])===null||h===void 0?void 0:h.column,sortOrder:(g=y[0])===null||g===void 0?void 0:g.order}},[u]),p=h=>{let g;h.multiplePriority===!1||!u.length||u[0].multiplePriority===!1?g=[h]:g=[].concat(se(u.filter(y=>{let{key:b}=y;return b!==h.key})),[h]),i(g),d(zr(g),g)};return[h=>_o(t,h,u,p,e,o,l),u,f,()=>zr(u)]},Fo=(n,t)=>n.map(e=>{const o=Object.assign({},e);return o.title=on(e.title,t),"children"in o&&(o.children=Fo(o.children,t)),o}),ms=n=>[c.useCallback(r=>Fo(r,n),[n])],gs=ko((n,t)=>{const{_renderTimes:r}=n,{_renderTimes:e}=t;return r!==e}),hs=No((n,t)=>{const{_renderTimes:r}=n,{_renderTimes:e}=t;return r!==e}),ys=n=>{const{componentCls:t,lineWidth:r,lineType:e,tableBorderColor:o,tableHeaderBg:l,tablePaddingVertical:d,tablePaddingHorizontal:a,calc:i}=n,s=`${j(r)} ${e} ${o}`,u=(f,p,v)=>({[`&${t}-${f}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${j(i(p).mul(-1).equal())}
              ${j(i(i(v).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${j(i(d).mul(-1).equal())} ${j(i(i(a).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},u("middle",n.tablePaddingVerticalMiddle,n.tablePaddingHorizontalMiddle)),u("small",n.tablePaddingVerticalSmall,n.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${j(r)} 0 ${j(r)} ${l}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}},xs=n=>{const{componentCls:t}=n;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},ra),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},bs=n=>{const{componentCls:t}=n;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:n.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:n.colorBgContainer}}}}},Cs=n=>{const{componentCls:t,antCls:r,motionDurationSlow:e,lineWidth:o,paddingXS:l,lineType:d,tableBorderColor:a,tableExpandIconBg:i,tableExpandColumnWidth:s,borderRadius:u,tablePaddingVertical:f,tablePaddingHorizontal:p,tableExpandedRowBg:v,paddingXXS:m,expandIconMarginTop:h,expandIconSize:g,expandIconHalfInner:y,expandIconScale:b,calc:x}=n,C=`${j(o)} ${d} ${a}`,S=x(m).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},Sa(n)),{position:"relative",float:"left",boxSizing:"border-box",width:g,height:g,padding:0,color:"inherit",lineHeight:j(g),background:i,border:C,borderRadius:u,transform:`scale(${b})`,transition:`all ${e}`,userSelect:"none","&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${e} ease-out`,content:'""'},"&::before":{top:y,insetInlineEnd:S,insetInlineStart:S,height:o},"&::after":{top:S,bottom:S,insetInlineStart:y,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:h,marginInlineEnd:l},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:v}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${j(x(f).mul(-1).equal())} ${j(x(p).mul(-1).equal())}`,padding:`${j(f)} ${j(p)}`}}}},Ss=n=>{const{componentCls:t,antCls:r,iconCls:e,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:l,paddingXXS:d,paddingXS:a,colorText:i,lineWidth:s,lineType:u,tableBorderColor:f,headerIconColor:p,fontSizeSM:v,tablePaddingHorizontal:m,borderRadius:h,motionDurationSlow:g,colorTextDescription:y,colorPrimary:b,tableHeaderFilterActiveBg:x,colorTextDisabled:C,tableFilterDropdownBg:S,tableFilterDropdownHeight:N,controlItemBgHover:k,controlItemBgActive:T,boxShadowSecondary:E,filterDropdownMenuBg:$,calc:M}=n,K=`${r}-dropdown`,O=`${t}-filter-dropdown`,R=`${r}-tree`,L=`${j(s)} ${u} ${f}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:M(d).mul(-1).equal(),marginInline:`${j(d)} ${j(M(m).div(2).mul(-1).equal())}`,padding:`0 ${j(d)}`,color:p,fontSize:v,borderRadius:h,cursor:"pointer",transition:`all ${g}`,"&:hover":{color:y,background:x},"&.active":{color:b}}}},{[`${r}-dropdown`]:{[O]:Object.assign(Object.assign({},Pn(n)),{minWidth:o,backgroundColor:S,borderRadius:h,boxShadow:E,overflow:"hidden",[`${K}-menu`]:{maxHeight:N,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:$,"&:empty::after":{display:"block",padding:`${j(a)} 0`,color:C,fontSize:v,textAlign:"center",content:'"Not Found"'}},[`${O}-tree`]:{paddingBlock:`${j(a)} 0`,paddingInline:a,[R]:{padding:0},[`${R}-treenode ${R}-node-content-wrapper:hover`]:{backgroundColor:k},[`${R}-treenode-checkbox-checked ${R}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:T}}},[`${O}-search`]:{padding:a,borderBottom:L,"&-input":{input:{minWidth:l},[e]:{color:C}}},[`${O}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${O}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${j(M(a).sub(s).equal())} ${j(a)}`,overflow:"hidden",borderTop:L}})}},{[`${r}-dropdown ${O}, ${O}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:a,color:i},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},ws=n=>{const{componentCls:t,lineWidth:r,colorSplit:e,motionDurationSlow:o,zIndexTableFixed:l,tableBg:d,zIndexTableSticky:a,calc:i}=n,s=e;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:l,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:i(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:i(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:i(a).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},Es=n=>{const{componentCls:t,antCls:r,margin:e}=n;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${j(e)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:n.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},ks=n=>{const{componentCls:t,tableRadius:r}=n;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${j(r)} ${j(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${j(r)} ${j(r)}`}}}}},$s=n=>{const{componentCls:t}=n;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},Ns=n=>{const{componentCls:t,antCls:r,iconCls:e,fontSizeIcon:o,padding:l,paddingXS:d,headerIconColor:a,headerIconHoverColor:i,tableSelectionColumnWidth:s,tableSelectedRowBg:u,tableSelectedRowHoverBg:f,tableRowHoverBg:p,tablePaddingHorizontal:v,calc:m}=n;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(o).add(m(l).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:m(s).add(m(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(o).add(m(l).div(4)).add(m(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:n.paddingXS,paddingInlineStart:n.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:m(n.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${n.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:j(m(v).div(4).equal()),[e]:{color:a,fontSize:o,verticalAlign:"baseline","&:hover":{color:i}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:u,"&-row-hover":{background:f}}},[`> ${t}-cell-row-hover`]:{background:p}}}}}},Ks=n=>{const{componentCls:t,tableExpandColumnWidth:r,calc:e}=n,o=(l,d,a,i)=>({[`${t}${t}-${l}`]:{fontSize:i,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${j(d)} ${j(a)}`},[`${t}-filter-trigger`]:{marginInlineEnd:j(e(a).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${j(e(d).mul(-1).equal())} ${j(e(a).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:j(e(d).mul(-1).equal()),marginInline:`${j(e(r).sub(a).equal())} ${j(e(a).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:j(e(a).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",n.tablePaddingVerticalMiddle,n.tablePaddingHorizontalMiddle,n.tableFontSizeMiddle)),o("small",n.tablePaddingVerticalSmall,n.tablePaddingHorizontalSmall,n.tableFontSizeSmall))}},Rs=n=>{const{componentCls:t,marginXXS:r,fontSizeIcon:e,headerIconColor:o,headerIconHoverColor:l}=n;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${n.motionDurationSlow}`,"&:hover":{background:n.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:n.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:n.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:n.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:n.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${n.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:e,"&.active":{color:n.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:l}}}},Is=n=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:e,tableScrollThumbBgHover:o,tableScrollThumbSize:l,tableScrollBg:d,zIndexTableSticky:a,stickyScrollBarBorderRadius:i,lineWidth:s,lineType:u,tableBorderColor:f}=n,p=`${j(s)} ${u} ${f}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:a,background:n.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${j(l)} !important`,zIndex:a,display:"flex",alignItems:"center",background:d,borderTop:p,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:l,backgroundColor:e,borderRadius:i,transition:`all ${n.motionDurationSlow}, transform none`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},jr=n=>{const{componentCls:t,lineWidth:r,tableBorderColor:e,calc:o}=n,l=`${j(r)} ${n.lineType} ${e}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:n.zIndexTableFixed,background:n.tableBg,"> tr":{"> th, > td":{borderBottom:l}}},[`div${t}-summary`]:{boxShadow:`0 ${j(o(r).mul(-1).equal())} 0 ${e}`}}}},Ts=n=>{const{componentCls:t,motionDurationMid:r,lineWidth:e,lineType:o,tableBorderColor:l,calc:d}=n,a=`${j(e)} ${o} ${l}`,i=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-row:not(tr)`]:{display:"flex",boxSizing:"border-box",width:"100%"},[`${t}-cell`]:{borderBottom:a,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${i}${i}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${j(e)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:a,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:a,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(e).mul(-1).equal(),borderInlineStart:a}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:a,borderBottom:a}}}}}},Os=n=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:e,tablePaddingHorizontal:o,tableExpandColumnWidth:l,lineWidth:d,lineType:a,tableBorderColor:i,tableFontSize:s,tableBg:u,tableRadius:f,tableHeaderTextColor:p,motionDurationMid:v,tableHeaderBg:m,tableHeaderCellSplitColor:h,tableFooterTextColor:g,tableFooterBg:y,calc:b}=n,x=`${j(d)} ${a} ${i}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},oa()),{[t]:Object.assign(Object.assign({},Pn(n)),{fontSize:s,background:u,borderRadius:`${j(f)} ${j(f)} 0 0`,scrollbarColor:`${n.tableScrollThumbBg} ${n.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${j(f)} ${j(f)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${j(e)} ${j(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${j(e)} ${j(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:m,borderBottom:x,transition:`background ${v} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:h,transform:"translateY(-50%)",transition:`background-color ${v}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${v}, border-color ${v}`,borderBottom:x,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:j(b(e).mul(-1).equal()),marginInline:`${j(b(l).sub(o).equal())}
                ${j(b(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottom:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:m,borderBottom:x,transition:`background ${v} ease`}}},[`${t}-footer`]:{padding:`${j(e)} ${j(o)}`,color:g,background:y}})}},Ps=n=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:e,colorFillSecondary:o,colorFillContent:l,controlItemBgActive:d,controlItemBgActiveHover:a,padding:i,paddingSM:s,paddingXS:u,colorBorderSecondary:f,borderRadiusLG:p,controlHeight:v,colorTextPlaceholder:m,fontSize:h,fontSizeSM:g,lineHeight:y,lineWidth:b,colorIcon:x,colorIconHover:C,opacityLoading:S,controlInteractiveSize:N}=n,k=new Bt(o).onBackground(r).toHexShortString(),T=new Bt(l).onBackground(r).toHexShortString(),E=new Bt(t).onBackground(r).toHexShortString(),$=new Bt(x),M=new Bt(C),K=N/2-b,O=K*2+b*3;return{headerBg:E,headerColor:e,headerSortActiveBg:k,headerSortHoverBg:T,bodySortBg:E,rowHoverBg:E,rowSelectedBg:d,rowSelectedHoverBg:a,rowExpandedBg:t,cellPaddingBlock:i,cellPaddingInline:i,cellPaddingBlockMD:s,cellPaddingInlineMD:u,cellPaddingBlockSM:u,cellPaddingInlineSM:u,borderColor:f,headerBorderRadius:p,footerBg:E,footerColor:e,cellFontSize:h,cellFontSizeMD:h,cellFontSizeSM:h,headerSplitColor:f,fixedHeaderSortActiveBg:k,headerFilterHoverBg:l,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:v,stickyScrollBarBg:m,stickyScrollBarBorderRadius:100,expandIconMarginTop:(h*y-b*3)/2-Math.ceil((g*1.4-b*3)/2),headerIconColor:$.clone().setAlpha($.getAlpha()*S).toRgbString(),headerIconHoverColor:M.clone().setAlpha(M.getAlpha()*S).toRgbString(),expandIconHalfInner:K,expandIconSize:O,expandIconScale:N/O}},Wr=2,Ds=Zr("Table",n=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:e,controlInteractiveSize:o,headerBg:l,headerColor:d,headerSortActiveBg:a,headerSortHoverBg:i,bodySortBg:s,rowHoverBg:u,rowSelectedBg:f,rowSelectedHoverBg:p,rowExpandedBg:v,cellPaddingBlock:m,cellPaddingInline:h,cellPaddingBlockMD:g,cellPaddingInlineMD:y,cellPaddingBlockSM:b,cellPaddingInlineSM:x,borderColor:C,footerBg:S,footerColor:N,headerBorderRadius:k,cellFontSize:T,cellFontSizeMD:E,cellFontSizeSM:$,headerSplitColor:M,fixedHeaderSortActiveBg:K,headerFilterHoverBg:O,filterDropdownBg:R,expandIconBg:L,selectionColumnWidth:D,stickyScrollBarBg:I,calc:B}=n,P=Jr(n,{tableFontSize:T,tableBg:e,tableRadius:k,tablePaddingVertical:m,tablePaddingHorizontal:h,tablePaddingVerticalMiddle:g,tablePaddingHorizontalMiddle:y,tablePaddingVerticalSmall:b,tablePaddingHorizontalSmall:x,tableBorderColor:C,tableHeaderTextColor:d,tableHeaderBg:l,tableFooterTextColor:N,tableFooterBg:S,tableHeaderCellSplitColor:M,tableHeaderSortBg:a,tableHeaderSortHoverBg:i,tableBodySortBg:s,tableFixedHeaderSortActiveBg:K,tableHeaderFilterActiveBg:O,tableFilterDropdownBg:R,tableRowHoverBg:u,tableSelectedRowBg:f,tableSelectedRowHoverBg:p,zIndexTableFixed:Wr,zIndexTableSticky:B(Wr).add(1).equal({unit:!1}),tableFontSizeMiddle:E,tableFontSizeSmall:$,tableSelectionColumnWidth:D,tableExpandIconBg:L,tableExpandColumnWidth:B(o).add(B(n.padding).mul(2)).equal(),tableExpandedRowBg:v,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:I,tableScrollThumbBgHover:t,tableScrollBg:r});return[Os(P),Es(P),jr(P),Rs(P),Ss(P),ys(P),ks(P),Cs(P),jr(P),bs(P),Ns(P),ws(P),Is(P),xs(P),Ks(P),$s(P),Ts(P)]},Ps,{unitless:{expandIconScale:!0}}),Ms=[],Ls=(n,t)=>{var r,e;const{prefixCls:o,className:l,rootClassName:d,style:a,size:i,bordered:s,dropdownPrefixCls:u,dataSource:f,pagination:p,rowSelection:v,rowKey:m="key",rowClassName:h,columns:g,children:y,childrenColumnName:b,onChange:x,getPopupContainer:C,loading:S,expandIcon:N,expandable:k,expandedRowRender:T,expandIconColumnIndex:E,indentSize:$,scroll:M,sortDirections:K,locale:O,showSorterTooltip:R={target:"full-header"},virtual:L}=n;On();const D=c.useMemo(()=>g||_n(y),[g,y]),I=c.useMemo(()=>D.some(oe=>oe.responsive),[D]),B=Ca(I),P=c.useMemo(()=>{const oe=new Set(Object.keys(B).filter(ye=>B[ye]));return D.filter(ye=>!ye.responsive||ye.responsive.some(Se=>oe.has(Se)))},[D,B]),V=to(n,["className","style","columns"]),{locale:W=ia,direction:re,table:J,renderEmpty:$e,getPrefixCls:ge,getPopupContainer:Ne}=c.useContext(tn),ee=ga(i),pe=Object.assign(Object.assign({},W.Table),O),te=f||Ms,ce=ge("table",o),Q=ge("dropdown",u),[,U]=Qr(),z=aa(ce),[F,q,ae]=Ds(ce,z),ne=Object.assign(Object.assign({childrenColumnName:b,expandIconColumnIndex:E},k),{expandIcon:(r=k==null?void 0:k.expandIcon)!==null&&r!==void 0?r:(e=J==null?void 0:J.expandable)===null||e===void 0?void 0:e.expandIcon}),{childrenColumnName:A="children"}=ne,ie=c.useMemo(()=>te.some(oe=>oe==null?void 0:oe[A])?"nest":T||k!=null&&k.expandedRowRender?"row":null,[te]),G={body:c.useRef()},ue=Ti(ce),we=c.useRef(null),X=c.useRef(null);Ri(t,()=>Object.assign(Object.assign({},X.current),{nativeElement:we.current}));const ve=c.useMemo(()=>typeof m=="function"?m:oe=>oe==null?void 0:oe[m],[m]),[xe]=cs(te,A,ve),he={},We=function(oe,ye){let Se=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var Ie,je,Fe,it;const Be=Object.assign(Object.assign({},he),oe);Se&&((Ie=he.resetPagination)===null||Ie===void 0||Ie.call(he),!((je=Be.pagination)===null||je===void 0)&&je.current&&(Be.pagination.current=1),p&&((Fe=p.onChange)===null||Fe===void 0||Fe.call(p,1,(it=Be.pagination)===null||it===void 0?void 0:it.pageSize))),M&&M.scrollToFirstRowOnChange!==!1&&G.body.current&&ka(0,{getContainer:()=>G.body.current}),x==null||x(Be.pagination,Be.filters,Be.sorter,{currentDataSource:Kn(In(te,Be.sorterStates,A),Be.filterStates,A),action:ye})},De=(oe,ye)=>{We({sorter:oe,sorterStates:ye},"sort",!1)},[ot,Y,be,Ce]=vs({prefixCls:ce,mergedColumns:P,onSorterChange:De,sortDirections:K||["ascend","descend"],tableLocale:pe,showSorterTooltip:R}),Pe=c.useMemo(()=>In(te,Y,A),[te,Y]);he.sorter=Ce(),he.sorterStates=Y;const Ke=(oe,ye)=>{We({filters:oe,filterStates:ye},"filter",!0)},[Ee,Te,Me]=ss({prefixCls:ce,locale:pe,dropdownPrefixCls:Q,mergedColumns:P,onFilterChange:Ke,getPopupContainer:C||Ne,rootClassName:Z(d,z)}),Re=Kn(Pe,Te,A);he.filters=Me,he.filterStates=Te;const at=c.useMemo(()=>{const oe={};return Object.keys(Me).forEach(ye=>{Me[ye]!==null&&(oe[ye]=Me[ye])}),Object.assign(Object.assign({},be),{filters:oe})},[be,Me]),[Ve]=ms(at),Pt=(oe,ye)=>{We({pagination:Object.assign(Object.assign({},he.pagination),{current:oe,pageSize:ye})},"paginate")},[ke,Dt]=fs(Re.length,Pt,p);he.pagination=p===!1?{}:us(ke,p),he.resetPagination=Dt;const qe=c.useMemo(()=>{if(p===!1||!ke.pageSize)return Re;const{current:oe=1,total:ye,pageSize:Se=Ho}=ke;return Re.length<ye?Re.length>Se?Re.slice((oe-1)*Se,oe*Se):Re:Re.slice((oe-1)*Se,oe*Se)},[!!p,Re,ke==null?void 0:ke.current,ke==null?void 0:ke.pageSize,ke==null?void 0:ke.total]),[Ye,Ze]=Ni({prefixCls:ce,data:Re,pageData:qe,getRowKey:ve,getRecordByKey:xe,expandType:ie,childrenColumnName:A,locale:pe,getPopupContainer:C||Ne},v),St=(oe,ye,Se)=>{let Ie;return typeof h=="function"?Ie=Z(h(oe,ye,Se)):Ie=Z(h),Z({[`${ce}-row-selected`]:Ze.has(ve(oe,ye))},Ie)};ne.__PARENT_RENDER_ICON__=ne.expandIcon,ne.expandIcon=ne.expandIcon||N||Ii(pe),ie==="nest"&&ne.expandIconColumnIndex===void 0?ne.expandIconColumnIndex=v?1:0:ne.expandIconColumnIndex>0&&v&&(ne.expandIconColumnIndex-=1),typeof ne.indentSize!="number"&&(ne.indentSize=typeof $=="number"?$:15);const lt=c.useCallback(oe=>Ve(Ye(Ee(ot(oe)))),[ot,Ee,Ye]);let Le,ze;if(p!==!1&&(ke!=null&&ke.total)){let oe;ke.size?oe=ke.size:oe=ee==="small"||ee==="middle"?"small":void 0;const ye=je=>c.createElement(ao,Object.assign({},ke,{className:Z(`${ce}-pagination ${ce}-pagination-${je}`,ke.className),size:oe})),Se=re==="rtl"?"left":"right",{position:Ie}=ke;if(Ie!==null&&Array.isArray(Ie)){const je=Ie.find(Be=>Be.includes("top")),Fe=Ie.find(Be=>Be.includes("bottom")),it=Ie.every(Be=>`${Be}`=="none");!je&&!Fe&&!it&&(ze=ye(Se)),je&&(Le=ye(je.toLowerCase().replace("top",""))),Fe&&(ze=ye(Fe.toLowerCase().replace("bottom","")))}else ze=ye(Se)}let Qe;typeof S=="boolean"?Qe={spinning:S}:typeof S=="object"&&(Qe=Object.assign({spinning:!0},S));const Mt=Z(ae,z,`${ce}-wrapper`,J==null?void 0:J.className,{[`${ce}-wrapper-rtl`]:re==="rtl"},l,d,q),dt=Object.assign(Object.assign({},J==null?void 0:J.style),a),et=typeof(O==null?void 0:O.emptyText)<"u"?O.emptyText:($e==null?void 0:$e("Table"))||c.createElement(ha,{componentName:"Table"}),an=L?hs:gs,Wt={},ln=c.useMemo(()=>{const{fontSize:oe,lineHeight:ye,padding:Se,paddingXS:Ie,paddingSM:je}=U,Fe=Math.floor(oe*ye);switch(ee){case"large":return Se*2+Fe;case"small":return Ie*2+Fe;default:return je*2+Fe}},[U,ee]);return L&&(Wt.listItemHeight=ln),F(c.createElement("div",{ref:we,className:Mt,style:dt},c.createElement(la,Object.assign({spinning:!1},Qe),Le,c.createElement(an,Object.assign({},Wt,V,{ref:X,columns:P,direction:re,expandable:ne,prefixCls:ce,className:Z({[`${ce}-middle`]:ee==="middle",[`${ce}-small`]:ee==="small",[`${ce}-bordered`]:s,[`${ce}-empty`]:te.length===0},ae,z,q),data:qe,rowKey:ve,rowClassName:St,emptyText:et,internalHooks:jt,internalRefs:G,transformColumns:lt,getContainerWidth:ue})),ze)))},Bs=c.forwardRef(Ls),Hs=(n,t)=>{const r=c.useRef(0);return r.current+=1,c.createElement(Bs,Object.assign({},n,{ref:t,_renderTimes:r.current}))},ct=c.forwardRef(Hs);ct.SELECTION_COLUMN=ft;ct.EXPAND_COLUMN=vt;ct.SELECTION_ALL=wn;ct.SELECTION_INVERT=En;ct.SELECTION_NONE=kn;ct.Column=gi;ct.ColumnGroup=hi;ct.Summary=mo;const Ws=({columns:n,data:t,loading:r,rowKey:e,showPagination:o,pagination:l,onChange:d,scroll:a={x:1e3}})=>{c.useState("checkbox");const i={onChange:(s,u)=>{},onSelect:(s,u,f)=>{},onSelectAll:(s,u,f)=>{}};return Ht.jsxs(Ht.Fragment,{children:[Ht.jsx(ct,{rowSelection:i,columns:n,dataSource:t,pagination:!1,loading:r,rowKey:e,scroll:a}),o&&Ht.jsx("div",{className:"custom-pagination d-flex justify-content-end mt-4",children:Ht.jsx(ao,{current:l.current||1,total:l.total||0,pageSize:l.pageSize||10,onChange:d})})]})};export{Ws as C};
