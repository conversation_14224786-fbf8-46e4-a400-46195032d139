// A simple in-memory cache service
class CacheService {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
  }

  generateKey(method, url, params) {
    return `${method}:${url}:${JSON.stringify(params)}`;
  }

  set(key, data, ttl = this.defaultTTL) {
    const expireAt = Date.now() + ttl;
    this.cache.set(key, {
      data,
      expireAt,
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() > cached.expireAt) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  invalidate(key) {
    this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  // Extract resource type from a URL
  getResourceFromUrl(url) {
    const urlParts = url.split("/");
    const apiIndex = urlParts.findIndex((part) => part === "api");
    return apiIndex >= 0 && urlParts[apiIndex + 1]
      ? urlParts[apiIndex + 1]
      : null;
  }

  // Invalidate all cache entries for a specific resource
  invalidateResource(resource) {
    if (!resource) return;

    for (let [key] of this.cache.entries()) {
      if (key.includes(`/${resource}/`) || key.includes(`/${resource}?`)) {
        this.cache.delete(key);
      }
    }
  }

  // Invalidate related resources (for handling relationships)
  invalidateRelatedResources(resource) {
    const relatedResources = {
      employees: ["users", "roles"],
      roles: ["employees", "permissions"],
      projects: ["employees", "tasks"],
      tasks: ["projects", "employees"],
      directories: ["files", "documents"],
      // Add more relationships as needed
    };

    // Invalidate the main resource
    this.invalidateResource(resource);

    // Invalidate related resources
    const related = relatedResources[resource] || [];
    related.forEach((relatedResource) => {
      this.invalidateResource(relatedResource);
    });
  }
}

// Create a singleton instance
const cacheService = new CacheService();
export default cacheService;
