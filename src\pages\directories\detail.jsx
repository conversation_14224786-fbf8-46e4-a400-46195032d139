import React, { memo, useEffect, useState } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import BaseInput from "../../components/shared/inputs";
import CustomTable from "../../components/shared/table/customtable";
import PageTitle from "../../components/shared/pagetitle";
import FlatButton from "../../components/shared/button/flatbutton";
import BackButton from "../../components/shared/button/backbutton";
import { ColumnsDirectoriesChild } from "../../components/partial/configdata/tabledata";
import { useParams, useLocation } from "react-router-dom";
import { useFetch } from "../../hooks";
import CustomModal from "../../components/shared/modal";
import Addfolder from "../../components/partial/modalforms/addfolder";
import useSweetAlert from "../../hooks/useSweetAlert";
import { Spin } from "antd";
import { debounce } from "lodash";
const DirectoriesDetail = () => {
  const user = window.user?.user;
  const { data: userObj } = useFetch("get_profile", {
    type: "mount",
    slug: `/${user._id}`,
    enablePagination: false,
  });
  const { showAlert } = useSweetAlert();
  let { project_id, parent_id } = useParams();
  const location = useLocation();
  const { record } = location.state || {};
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [search, setSearch] = useState(""); // State to hold search input value
  const { loading, data, fetchApi, pagination, setQueryParams } = useFetch(
    "directories",
    {
      slug: `/?project_id=${project_id}&parent_id=${parent_id}&`,
      enablePagination: true,
      defaultQueryParams: { page: 1, limit: 10 },
    }
  );
  const { postData: deleteItem } = useFetch("delete_directories", {
    type: "submit",
  });

  useEffect(() => {
    fetchApi();
  }, [project_id]);

  useEffect(() => {
    // Apply search query when the search value changes
    setQueryParams((prev) => ({
      ...prev,
      keyword: search, // Avoid sending empty search params
    }));
  }, [search]);

  const deleteRow = async (id) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Do you want to proceed with this action?",
      icon: "warning",
      background: "#f6f6f6", // Light background
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "No",
    });

    if (result.isConfirmed) {
      deleteItem("", cbSuccess, id);
    }
  };
  const cbSuccess = (res) => {
    if (res.statusCode === 200) {
      fetchApi();
    }
  };
  const handlePageChange = (page, pageSize) => {
    setQueryParams((prev) => ({
      ...prev,
      page,
      limit: pageSize,
      keyword: search, // Persist the search query
    }));
  };
  const handleSearchChange = debounce((e) => {
    const value = e.target.value;
    setSearch(value); // Update search state
    setQueryParams((prev) => ({
      ...prev,
      keyword: value,
      page: 1,
    }));
  }, 300);

  if (loading && window.lodash.isEmpty(record)) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          height: "100vh",
          alignItems: "center",
        }}
      >
        <Spin size="large" /> {/* Ant Design loader */}
      </div>
    );
  }
  return (
    <>
      <InnerLayout>
        <PageTitle
          title={<BackButton title={record?.title} />}
          buttons={
            <>
              <div>
                <BaseInput
                  name="search"
                  placeholder="Search"
                  icon={<img src="/admin/assets/img/search-icon.png" />}
                  value={search} // Bind search value
                  onChange={handleSearchChange} // Update on input change
                />
              </div>
              {(userObj?.role === "company" ||
                userObj?.policies?.some(
                  (policy) => policy.module === "project" && policy.can_create
                )) && (
                <div>
                  <FlatButton
                    title="Upload File"
                    className="mx-auto add-new-btn me-3"
                    onClick={() => setIsModalOpen(true)}
                  />
                </div>
              )}
            </>
          }
        />
        <div className="detail-table mt-4 mb-5">
          <CustomTable
            columns={ColumnsDirectoriesChild(deleteRow, userObj)}
            data={data}
            loading={loading}
            rowKey={"_id"}
            pagination={{
              current: pagination?.currentPage,
              total: pagination?.count,
              pageSize: pagination?.perPage,
            }}
            showPagination={true}
            onChange={handlePageChange}
          />
        </div>
      </InnerLayout>
      <CustomModal
        title="Upload PDF"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        className="custom-modal"
        footer={false}
      >
        <Addfolder
          onCancel={() => setIsModalOpen(false)}
          onSuccess={fetchApi}
          projectId={project_id}
          parentId={record?._id}
          type={"upload-pdf"}
        />
      </CustomModal>
    </>
  );
};

export default memo(DirectoriesDetail);
