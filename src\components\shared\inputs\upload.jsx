import React from "react";
import { useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { message, Upload } from "antd";
const getBase64 = (img, callback) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result));
    reader.readAsDataURL(img);
};
const beforeUpload = (file) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
        message.error("You can only upload JPG/PNG file!");
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error("Image must smaller than 2MB!");
    }
    return isJpgOrPng && isLt2M;
};
const UploadProfile = () => {
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState("/assets/images/my-profile.png");
    const handleChange = (info) => {
        if (info.file.status === "uploading") {
            setLoading(true);
            return;
        }
        if (info.file.status === "done") {
            // Get this url from response in real world.
            getBase64(info.file.originFileObj, (url) => {
                setLoading(false);
                setImageUrl(url);
            });
        }
    };
    const uploadButton = (
        <button
            style={{
                border: 0,
                background: "none",
            }}
            type="button"
        >
            {loading ? <LoadingOutlined /> : ""}
        </button>
    );
    return (
        <>
            <Upload
                name="avatar"
                listType="picture-circle"
                className="avatar-uploader"
                showUploadList={false}
                action="/assets/images/my-profile.png"
                beforeUpload={beforeUpload}
                onChange={handleChange}
            >
                {imageUrl ? (
                    <img
                        src={imageUrl}
                        alt="avatar"
                        style={{
                            width: "100%",
                        }}
                    />
                ) : (
                    <div className="upload-icon" onClick={handleChange}>
                        <img src="/assets/images/upload-icon.png" alt="" />
                    </div>
                )}
                <div className="upload-icon" onClick={handleChange}>
                    <img src="/assets/images/upload-icon.png" alt="" />
                </div>
            </Upload>
        </>
    );
};
export default UploadProfile;
