import { messaging, getToken } from "../firebase";

// ✅ Utility: Detect push support (FCM does not work on iOS Safari)
const isPushSupported = () => {
  return (
    "Notification" in window &&
    "serviceWorker" in navigator &&
    "PushManager" in window
  );
};

// ✅ Utility: Detect iOS Devices (for fallback)
const isIOS = () => {
  return /iPhone|iPad|iPod/i.test(navigator.userAgent);
};

// ✅ Request Notification Permission & Get Device Token
export const requestPermissionAndGetToken = async () => {
  if (!isPushSupported() || isIOS()) {
    // Fallback for unsupported browsers (like iOS Safari)
    console.warn("Push not supported or iOS Safari detected");
    return "123456789";
  }

  try {
    const permission = await Notification.requestPermission();

    if (permission === "granted") {
      const currentToken = await getToken(messaging, {
        vapidKey:
          "BAN75GqbMU2x1is2o32fWr90ib4kFz7GSI1UxCfYUWoc8et62zv5F48ostuaZxrLPggiYZYslLx5kFNRq-omkvg",
      });

      if (currentToken) {
        return currentToken;
      } else {
        console.warn("No token received");
        return "static_web_token";
      }
    } else {
      console.warn("Notification permission denied");
      return "static_denied_token";
    }
  } catch (err) {
    console.error("Token fetch error", err);
    return "static_error_token";
  }
};
